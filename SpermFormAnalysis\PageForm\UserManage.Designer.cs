﻿namespace SpermFormAnalysis.PageForm
{
    partial class UserManage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            this.ucGroupHead1 = new SunnyUI.Library.UCGroupHead();
            this.ucGroupHead2 = new SunnyUI.Library.UCGroupHead();
            this.addBtn = new Sunny.UI.UISymbolButton();
            this.delBtn = new Sunny.UI.UISymbolButton();
            this.editBtn = new Sunny.UI.UISymbolButton();
            this.dataGrid_Admin = new Sunny.UI.UIDataGridView();
            this.Admin_ID = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Admin_PWD = new System.Windows.Forms.DataGridViewTextBoxColumn();
            this.Admin_Level = new System.Windows.Forms.DataGridViewTextBoxColumn();
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid_Admin)).BeginInit();
            this.SuspendLayout();
            // 
            // ucGroupHead1
            // 
            this.ucGroupHead1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ucGroupHead1.Font = new System.Drawing.Font("微软雅黑", 15F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ucGroupHead1.HeadColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(144)))), ((int)(((byte)(255)))));
            this.ucGroupHead1.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(233)))), ((int)(((byte)(252)))));
            this.ucGroupHead1.Location = new System.Drawing.Point(12, 8);
            this.ucGroupHead1.Name = "ucGroupHead1";
            this.ucGroupHead1.Size = new System.Drawing.Size(908, 40);
            this.ucGroupHead1.TabIndex = 0;
            this.ucGroupHead1.Text = "用户管理";
            this.ucGroupHead1.TextColor = System.Drawing.Color.Black;
            // 
            // ucGroupHead2
            // 
            this.ucGroupHead2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ucGroupHead2.Font = new System.Drawing.Font("微软雅黑", 15F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ucGroupHead2.HeadColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(144)))), ((int)(((byte)(255)))));
            this.ucGroupHead2.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(233)))), ((int)(((byte)(252)))));
            this.ucGroupHead2.Location = new System.Drawing.Point(12, 143);
            this.ucGroupHead2.Name = "ucGroupHead2";
            this.ucGroupHead2.Size = new System.Drawing.Size(908, 40);
            this.ucGroupHead2.TabIndex = 1;
            this.ucGroupHead2.Text = "用户列表";
            this.ucGroupHead2.TextColor = System.Drawing.Color.Black;
            // 
            // addBtn
            // 
            this.addBtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.addBtn.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.addBtn.Location = new System.Drawing.Point(82, 75);
            this.addBtn.MinimumSize = new System.Drawing.Size(1, 1);
            this.addBtn.Name = "addBtn";
            this.addBtn.Size = new System.Drawing.Size(133, 35);
            this.addBtn.Style = Sunny.UI.UIStyle.Custom;
            this.addBtn.Symbol = 61543;
            this.addBtn.TabIndex = 11;
            this.addBtn.Text = "添加";
            this.addBtn.Click += new System.EventHandler(this.addBtn_Click);
            // 
            // delBtn
            // 
            this.delBtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.delBtn.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.delBtn.Location = new System.Drawing.Point(533, 75);
            this.delBtn.MinimumSize = new System.Drawing.Size(1, 1);
            this.delBtn.Name = "delBtn";
            this.delBtn.Size = new System.Drawing.Size(133, 35);
            this.delBtn.Style = Sunny.UI.UIStyle.Custom;
            this.delBtn.Symbol = 61527;
            this.delBtn.TabIndex = 10;
            this.delBtn.Text = "删除";
            this.delBtn.Click += new System.EventHandler(this.delBtn_Click);
            // 
            // editBtn
            // 
            this.editBtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.editBtn.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.editBtn.Location = new System.Drawing.Point(307, 75);
            this.editBtn.MinimumSize = new System.Drawing.Size(1, 1);
            this.editBtn.Name = "editBtn";
            this.editBtn.Size = new System.Drawing.Size(133, 35);
            this.editBtn.Style = Sunny.UI.UIStyle.Custom;
            this.editBtn.Symbol = 61508;
            this.editBtn.TabIndex = 9;
            this.editBtn.Text = "编辑";
            this.editBtn.Click += new System.EventHandler(this.editBtn_Click);
            // 
            // dataGrid_Admin
            // 
            this.dataGrid_Admin.AllowUserToAddRows = false;
            this.dataGrid_Admin.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.dataGrid_Admin.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dataGrid_Admin.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.dataGrid_Admin.BackgroundColor = System.Drawing.Color.White;
            this.dataGrid_Admin.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGrid_Admin.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dataGrid_Admin.ColumnHeadersHeight = 32;
            this.dataGrid_Admin.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dataGrid_Admin.Columns.AddRange(new System.Windows.Forms.DataGridViewColumn[] {
            this.Admin_ID,
            this.Admin_PWD,
            this.Admin_Level});
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGrid_Admin.DefaultCellStyle = dataGridViewCellStyle3;
            this.dataGrid_Admin.EnableHeadersVisualStyles = false;
            this.dataGrid_Admin.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dataGrid_Admin.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.dataGrid_Admin.Location = new System.Drawing.Point(52, 189);
            this.dataGrid_Admin.Name = "dataGrid_Admin";
            this.dataGrid_Admin.ReadOnly = true;
            this.dataGrid_Admin.RectColor = System.Drawing.Color.White;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGrid_Admin.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.White;
            this.dataGrid_Admin.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dataGrid_Admin.RowTemplate.Height = 29;
            this.dataGrid_Admin.SelectedIndex = -1;
            this.dataGrid_Admin.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGrid_Admin.ShowGridLine = true;
            this.dataGrid_Admin.Size = new System.Drawing.Size(545, 332);
            this.dataGrid_Admin.Style = Sunny.UI.UIStyle.Custom;
            this.dataGrid_Admin.TabIndex = 12;
            this.dataGrid_Admin.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dataGrid_Admin_CellFormatting);
            // 
            // Admin_ID
            // 
            this.Admin_ID.DataPropertyName = "Admin_ID";
            this.Admin_ID.HeaderText = "用户名";
            this.Admin_ID.Name = "Admin_ID";
            this.Admin_ID.ReadOnly = true;
            this.Admin_ID.Width = 150;
            // 
            // Admin_PWD
            // 
            this.Admin_PWD.DataPropertyName = "Admin_PWD";
            this.Admin_PWD.HeaderText = "密码";
            this.Admin_PWD.Name = "Admin_PWD";
            this.Admin_PWD.ReadOnly = true;
            this.Admin_PWD.Width = 150;
            // 
            // Admin_Level
            // 
            this.Admin_Level.DataPropertyName = "Admin_Level";
            this.Admin_Level.HeaderText = "级别";
            this.Admin_Level.Name = "Admin_Level";
            this.Admin_Level.ReadOnly = true;
            this.Admin_Level.Width = 200;
            // 
            // UserManage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 21F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(932, 533);
            this.Controls.Add(this.dataGrid_Admin);
            this.Controls.Add(this.addBtn);
            this.Controls.Add(this.delBtn);
            this.Controls.Add(this.editBtn);
            this.Controls.Add(this.ucGroupHead2);
            this.Controls.Add(this.ucGroupHead1);
            this.Name = "UserManage";
            this.Style = Sunny.UI.UIStyle.Custom;
            this.Text = "UserManage";
            ((System.ComponentModel.ISupportInitialize)(this.dataGrid_Admin)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private SunnyUI.Library.UCGroupHead ucGroupHead1;
        private SunnyUI.Library.UCGroupHead ucGroupHead2;
        private Sunny.UI.UISymbolButton addBtn;
        private Sunny.UI.UISymbolButton delBtn;
        private Sunny.UI.UISymbolButton editBtn;
        private Sunny.UI.UIDataGridView dataGrid_Admin;
        private System.Windows.Forms.DataGridViewTextBoxColumn Admin_ID;
        private System.Windows.Forms.DataGridViewTextBoxColumn Admin_PWD;
        private System.Windows.Forms.DataGridViewTextBoxColumn Admin_Level;
    }
}