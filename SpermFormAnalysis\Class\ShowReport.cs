﻿
using NPOI.XWPF.UserModel;
using Sunny.UI.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.Class
{
    public class ShowReport
    {
        public static bool printReport(String[] PatientInfo, string FL1_Name1, string FL3_Name1, bool open_flag, ref string msg)
        {
            #region word 自动保存参数

            string reports = "template\\Report.docx";
            string report1 = "MRFreport.docx";

            if (!File.Exists(reports))
            {
                msg = "未能找模板报告文件！\n注意：千万不要尝试删除安装目录下的任何文件，\n否则可能导致系统崩溃！\n\n提示：找不到该文件将无法完成打印工作！";
                return false;
            }
            string tmpDocFile = Environment.CurrentDirectory + "\\" + reports;
            //DateTime test_date = DateTime.Parse(PatientInfo[6]);
            string ReportPath = "Reports" + "/" + DateTime.Now.ToString("yyyy") + "/" + DateTime.Now.ToString("MM") + "/";
            if (!System.IO.Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + ReportPath)) 
                System.IO.Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + ReportPath);
            string String = PatientInfo[2] + " " + PatientInfo[0];//文件名为病人姓名+门诊编号
            string newFile = String + " " + DateTime.Now.ToString("MMdd") + " " + report1;

            #endregion


            #region 自动保存
            string reportfileFirst = AppDomain.CurrentDomain.BaseDirectory + ReportPath + newFile;
            try
            {
                File.Delete(reportfileFirst);
            }
            catch (Exception e)
            {
                msg = "检测到该实验报告文件已经被其它程序打开，请先关闭才可生成报告";
                return false;
            }
            if (!File.Exists(reportfileFirst)) File.Copy(tmpDocFile, reportfileFirst);
            FileStream fs = new FileStream(@reportfileFirst, FileMode.Open, FileAccess.Read);
            XWPFDocument myDocx = new XWPFDocument(fs);//打开07（.docx）以上的版本的文档

            

            string[] PatientInfoAddr = new string[]{"Hospital", "PatientName", "PatientAge",  "PatientID","abstinence","@", "#","department",
                                            "sendDate", "DoctorName", "inspection", "reviewer","checkdate","todaydate","spermnum","normalnum","rrate","abnormanum",
                                             "arate", "noheadnum", "hrate","nomiddlenum","mrate","notailnum","nrate","nomercnum","orate","tdzrate","sdirate","DV"};
            foreach (var para in myDocx.Paragraphs)
            {
                //_ = para.ParagraphText;
                IList<XWPFRun> runs = para.Runs;
                //string styleid = para.Style;
                for (int i = 0; i < runs.Count; i++)
                {
                    XWPFRun run = runs[i];
                    string text = run.ToString();
                    Type t = PatientInfo.GetType();
                    PropertyInfo[] pi = t.GetProperties();
                    foreach (PropertyInfo p in pi)
                    {
                        for (int j = 0; j < PatientInfoAddr.Length; j++)//$$与模板中$$对应，也可以改成其它符号，比如{$name},务必做到唯一
                            if (text.Contains(PatientInfoAddr[j]))
                            {
                                text = text.Replace(PatientInfoAddr[j], PatientInfo[j]);
                            }
                    }
                    runs[i].SetText(text, 0);
                }
            }
            var tables = myDocx.Tables;
            foreach (var table in tables)
            {
                foreach (var row in table.Rows)
                {
                    foreach (var cell in row.GetTableCells())
                    {
                        foreach (var para in cell.Paragraphs)
                        {
                            string text = para.ParagraphText;
                            var runs = para.Runs;
                            string styleid = para.Style;
                            for (int i = 0; i < runs.Count; i++)
                            {
                                var run = runs[i];
                                text = run.ToString();
                                Type t = PatientInfo.GetType();
                                PropertyInfo[] pi = t.GetProperties();
                                foreach (PropertyInfo p in pi)
                                {
                                    for (int j = 0; j < PatientInfoAddr.Length; j++)//$$与模板中$$对应，也可以改成其它符号，比如{$name},务必做到唯一
                                        if (text.Contains(PatientInfoAddr[j]))
                                        {
                                            text = text.Replace(PatientInfoAddr[j], PatientInfo[j]);
                                        }
                                }
                                runs[i].SetText(text, 0);
                            }
                        }
                    }
                }
            }
            #endregion

            #region 修改图片
            var pic_table = tables[1];
            var pic_row = pic_table.GetRow(6);
            String[] img_names = { FL1_Name1, FL3_Name1 };
            for (int i = 1; i <= 2; i++)
            {
                XWPFTableCell Cell = pic_row.GetCell(i - 1);//获取到进行图片插入的单元格
                XWPFRun run = Cell.AddParagraph().CreateRun();
                FileStream img = new FileStream(img_names[i - 1], FileMode.Open, FileAccess.Read);
                run.AddPicture(img, (int)PictureType.JPEG, "1" + i + ".png", 258 * 9525, 200 * 9525);
                Cell.RemoveParagraph(0);
                img.Close();
            }

            #endregion

            //报表参数 Hospital=配置中获取,PatientName=patientName,PatientAge=patientage,PatientID=patientId,department=department,sendDate=sendtime,DoctorName=sendDoctor,inspection=inspectionDoctor,reviewer=reviewer,checkdate=processed_time todaydate=todaydate
            //具体实际参数 spermnum=sperm_num,normalnum=normal_num,rrate=qualification_rate,abnormalnum=abnormal_num,arate=100-qualification_rate,noheadnum=abnormalshape_num,hrate=abnormalshape_num/sperm_num,
            //nomiddlenum=abnormalmiddlepiece_num,mrate=100.0*abnormalmiddlepiece_num/sperm_num,notailnum=abnormalTail_num,nrate=100.0*abnormalTail_num/sperm_num,
            //过量残留胞质（ERC）& 4     nomercnum=ERC_num,orate=100.0*ERC_num/sperm_num, 
            // tdzrate=, sdirate=,
            //qualification_rate 正常率
            //精子异常率  100-qualification_rate 头部异常数noheadnum=abnormalshape_num   头部形态异常率 hrate=abnormalshape_num/sperm_num  主段异常数=尾部异常数\
            //畸形精子指数(TZI)=（头部缺陷数+中段缺陷+主段缺陷）/精子缺陷数
            //精子畸形指数(SDI) =（头部缺陷数 + 中段缺陷 + 主段缺陷）/ 精子总数

            FileStream output = new FileStream(@reportfileFirst, FileMode.Create);
            myDocx.Write(output);
            fs.Close();
            fs.Dispose();
            output.Close();
            output.Dispose();
            //  System.Diagnostics.Process.Start(@reportfileFirst);
            if (open_flag)
            {
                System.Diagnostics.Process.Start(@reportfileFirst);
            }
            return true;
        }
    }
}
