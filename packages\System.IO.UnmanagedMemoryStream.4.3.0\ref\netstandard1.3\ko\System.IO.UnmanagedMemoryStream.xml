﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.UnmanagedMemoryStream</name>
  </assembly>
  <members>
    <member name="T:System.IO.UnmanagedMemoryAccessor">
      <summary>관리 코드에서 관리되지 않는 메모리 블록에 임의로 액세스할 수 있도록 합니다.</summary>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.#ctor">
      <summary>
        <see cref="T:System.IO.UnmanagedMemoryAccessor" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64)">
      <summary>지정된 버퍼, 오프셋 및 용량을 사용하여 <see cref="T:System.IO.UnmanagedMemoryAccessor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="buffer">접근자가 포함될 버퍼입니다.</param>
      <param name="offset">접근자를 시작할 바이트입니다.</param>
      <param name="capacity">할당할 메모리 크기(바이트)입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" />와 <paramref name="capacity" />를 더한 값이 <paramref name="buffer" />보다 큽니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="capacity" />가 0보다 작은 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="offset" />와 <paramref name="capacity" />을 더한 값이 주소 공간의 위쪽 끝에서 래핑됩니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>지정된 버퍼, 오프셋, 용량 및 액세스 권한을 사용하여 <see cref="T:System.IO.UnmanagedMemoryAccessor" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="buffer">접근자가 포함될 버퍼입니다.</param>
      <param name="offset">접근자를 시작할 바이트입니다.</param>
      <param name="capacity">할당할 메모리 크기(바이트)입니다.</param>
      <param name="access">메모리에 허용되는 액세스 형식입니다.기본값은 <see cref="F:System.IO.MemoryMappedFiles.MemoryMappedFileAccess.ReadWrite" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" />와 <paramref name="capacity" />를 더한 값이 <paramref name="buffer" />보다 큽니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="capacity" />가 0보다 작은 경우또는<paramref name="access" />가 유효한 <see cref="T:System.IO.MemoryMappedFiles.MemoryMappedFileAccess" /> 열거형 값이 아닌 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="offset" />와 <paramref name="capacity" />을 더한 값이 주소 공간의 위쪽 끝에서 래핑됩니다.</exception>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.CanRead">
      <summary>접근자가 읽을 수 있는지 여부를 확인합니다.</summary>
      <returns>접근자가 읽을 수 있으면 true이고, 읽을 수 없으면 false입니다. </returns>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.CanWrite">
      <summary>접근자가 쓸 수 있는지 여부를 확인합니다.</summary>
      <returns>접근자가 쓸 수 있으면 true이고, 쓸 수 없으면 false입니다. </returns>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.Capacity">
      <summary>접근자의 용량을 가져옵니다.</summary>
      <returns>접근자의 용량입니다.</returns>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Dispose">
      <summary>
        <see cref="T:System.IO.UnmanagedMemoryAccessor" />에서 사용하는 모든 리소스를 해제합니다. </summary>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.UnmanagedMemoryAccessor" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다. </summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다. </param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Initialize(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>접근자의 초기 값을 설정합니다.</summary>
      <param name="buffer">접근자가 포함될 버퍼입니다.</param>
      <param name="offset">접근자를 시작할 바이트입니다.</param>
      <param name="capacity">할당할 메모리 크기(바이트)입니다.</param>
      <param name="access">메모리에 허용되는 액세스 형식입니다.기본값은 <see cref="F:System.IO.MemoryMappedFiles.MemoryMappedFileAccess.ReadWrite" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" />와 <paramref name="capacity" />를 더한 값이 <paramref name="buffer" />보다 큽니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="capacity" />가 0보다 작은 경우또는<paramref name="access" />가 유효한 <see cref="T:System.IO.MemoryMappedFiles.MemoryMappedFileAccess" /> 열거형 값이 아닌 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="offset" />와 <paramref name="capacity" />을 더한 값이 주소 공간의 위쪽 끝에서 래핑됩니다.</exception>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.IsOpen">
      <summary>프로세스에서 현재 접근자가 열려 있는지 여부를 확인합니다.</summary>
      <returns>접근자가 열려 있으면 true이고,  닫혀 있으면 false입니다. </returns>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadBoolean(System.Int64)">
      <summary>접근자에서 부울 값을 읽습니다.</summary>
      <returns>true 또는 false</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadByte(System.Int64)">
      <summary>접근자에서 바이트 값을 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadChar(System.Int64)">
      <summary>접근자에서 문자를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadDecimal(System.Int64)">
      <summary>접근자에서 10진수 값을 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.또는읽을 십진수가 잘못되었습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadDouble(System.Int64)">
      <summary>접근자에서 배정밀도 부동 소수점 값을 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadInt16(System.Int64)">
      <summary>접근자에서 16비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadInt32(System.Int64)">
      <summary>접근자에서 32비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadInt64(System.Int64)">
      <summary>접근자에서 64비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadSByte(System.Int64)">
      <summary>접근자에서 부호 있는 8비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadSingle(System.Int64)">
      <summary>접근자에서 단정밀도 부동 소수점 값을 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadUInt16(System.Int64)">
      <summary>접근자에서 부호 없는 16비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadUInt32(System.Int64)">
      <summary>접근자에서 부호 없는 32비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadUInt64(System.Int64)">
      <summary>접근자에서 부호 없는 64비트 정수를 읽습니다.</summary>
      <returns>읽은 값입니다.</returns>
      <param name="position">읽기를 시작할 접근자의 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />이 값을 읽은 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Boolean)">
      <summary>접근자에 부울 값을 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Byte)">
      <summary>접근자에 바이트 값을 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Char)">
      <summary>접근자에 문자를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Decimal)">
      <summary>접근자에 10진수 값을 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.또는십진수가 잘못되었습니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Double)">
      <summary>접근자에 Double 값을 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Int16)">
      <summary>접근자에 16비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Int32)">
      <summary>접근자에 32비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Int64)">
      <summary>접근자에 64비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">값을 쓸 수 있는 위치 다음에 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.SByte)">
      <summary>접근자에 8비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Single)">
      <summary>접근자에 Single을(를) 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.UInt16)">
      <summary>접근자에 부호 없는 16비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.UInt32)">
      <summary>접근자에 부호 없는 32비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.UInt64)">
      <summary>접근자에 부호 없는 64비트 정수를 씁니다.</summary>
      <param name="position">쓰기를 시작할 접근자의 바이트 수입니다.</param>
      <param name="value">작성할 값입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="position" />가 값을 쓴 다음 바이트가 부족합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" />가 0보다 작거나 접근자의 용량보다 큰 경우</exception>
      <exception cref="T:System.NotSupportedException">접근자가 쓰기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ObjectDisposedException">접근자가 삭제되었습니다.</exception>
    </member>
    <member name="T:System.IO.UnmanagedMemoryStream">
      <summary>관리 코드에서 관리되지 않는 메모리 블록에 액세스할 수 있도록 합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor">
      <summary>
        <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <exception cref="T:System.Security.SecurityException">사용자에게 필요한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Byte*,System.Int64)">
      <summary>지정된 위치와 메모리 길이를 사용하여 <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="pointer">관리되지 않는 메모리 위치에 대한 포인터입니다.</param>
      <param name="length">사용할 메모리의 길이입니다.</param>
      <exception cref="T:System.Security.SecurityException">사용자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pointer" /> 값이 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> 값이 0보다 작은 경우또는<paramref name="length" />가 오버플로를 발생시킬 만큼 큰 경우</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Byte*,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>지정된 위치, 메모리 길이, 총 메모리 양 및 파일 액세스 값을 사용하여 <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="pointer">관리되지 않는 메모리 위치에 대한 포인터입니다.</param>
      <param name="length">사용할 메모리의 길이입니다.</param>
      <param name="capacity">시스템에 할당된 총 메모리 양입니다.</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 값 중 하나입니다.</param>
      <exception cref="T:System.Security.SecurityException">사용자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pointer" /> 값이 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> 값이 0보다 작은 경우또는 <paramref name="capacity" /> 값이 0보다 작은 경우또는<paramref name="length" /> 값이 <paramref name="capacity" /> 값보다 큰 경우</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64)">
      <summary>지정된 오프셋과 길이를 사용하여 안전한 버퍼에서 <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="buffer">관리되지 않는 메모리 스트림이 포함될 버퍼입니다.</param>
      <param name="offset">관리되지 않는 메모리 스트림을 시작할 버퍼의 바이트 위치입니다.</param>
      <param name="length">관리되지 않는 메모리 스트림의 길이입니다.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>지정된 오프셋, 길이 및 파일 액세스를 사용하여 안전한 버퍼에서 <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="buffer">관리되지 않는 메모리 스트림이 포함될 버퍼입니다.</param>
      <param name="offset">관리되지 않는 메모리 스트림을 시작할 버퍼의 바이트 위치입니다.</param>
      <param name="length">관리되지 않는 메모리 스트림의 길이입니다.</param>
      <param name="access">관리되지 않는 메모리 스트림에 대한 파일 액세스 모드입니다. </param>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.CanRead">
      <summary>스트림이 읽기를 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>생성자가 스트림 읽기를 포함하지 않는 <paramref name="access" /> 매개 변수를 사용하여 개체를 만들었고 스트림이 닫혔으면 false이고, 그렇지 않으면 true입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.CanSeek">
      <summary>스트림이 검색을 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>스트림이 닫혔으면 false이고, 그렇지 않으면 true입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.CanWrite">
      <summary>스트림이 쓰기를 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>생성자가 쓰기를 지원하는 <paramref name="access" /> 매개 변수 값을 사용하여 개체를 만들었거나 매개 변수가 없는 생성자가 개체를 만들었거나 스트림이 닫혔으면 false이고, 그렇지 않으면 true입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.Capacity">
      <summary>스트림에 할당된 스트림 길이(크기) 또는 총 메모리 양(용량)을 가져옵니다.</summary>
      <returns>스트림의 크기 또는 용량입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.UnmanagedMemoryStream" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Flush">
      <summary>
        <see cref="M:System.IO.Stream.Flush" /> 메서드를 재정의하여 아무런 작업도 수행되지 않도록 합니다.</summary>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>
        <see cref="M:System.IO.Stream.FlushAsync(System.Threading.CancellationToken)" /> 메서드를 재정의하여 지정된 경우 작업을 취소하지만 다른 작업이 수행되지 않도록 합니다.
                .NET Framework 2015 에서 시작 가능</summary>
      <returns>비동기 플러시 작업을 나타내는 작업입니다.</returns>
      <param name="cancellationToken">취소 요청을 모니터링할 토큰입니다.기본값은 <see cref="P:System.Threading.CancellationToken.None" />입니다.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Initialize(System.Byte*,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>관리되지 않는 메모리 위치에 대한 포인터를 사용하여 <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="pointer">관리되지 않는 메모리 위치에 대한 포인터입니다.</param>
      <param name="length">사용할 메모리의 길이입니다.</param>
      <param name="capacity">시스템에 할당된 총 메모리 양입니다.</param>
      <param name="access">
        <see cref="T:System.IO.FileAccess" /> 값 중 하나입니다. </param>
      <exception cref="T:System.Security.SecurityException">사용자에게 필요한 권한이 없는 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="pointer" /> 값이 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> 값이 0보다 작은 경우또는 <paramref name="capacity" /> 값이 0보다 작은 경우또는<paramref name="length" /> 값이 오버플로를 발생시킬 만큼 큰 경우</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Initialize(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>지정된 오프셋, 길이 및 파일 액세스를 사용하여 안전한 버퍼에서 <see cref="T:System.IO.UnmanagedMemoryStream" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="buffer">관리되지 않는 메모리 스트림이 포함될 버퍼입니다.</param>
      <param name="offset">관리되지 않는 메모리 스트림을 시작할 버퍼의 바이트 위치입니다.</param>
      <param name="length">관리되지 않는 메모리 스트림의 길이입니다.</param>
      <param name="access">관리되지 않는 메모리 스트림에 대한 파일 액세스 모드입니다.</param>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.Length">
      <summary>스트림의 데이터 길이를 가져옵니다.</summary>
      <returns>스트림의 데이터 길이입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.Position">
      <summary>스트림 내의 현재 위치를 가져오거나 설정합니다.</summary>
      <returns>스트림 내의 현재 위치입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">위치가 0보다 작은 값으로 설정된 경우 또는 위치가 <see cref="F:System.Int32.MaxValue" />보다 크거나 현재 포인터에 추가될 때 오버플로가 발생할 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.PositionPointer">
      <summary>스트림 내의 현재 위치를 기준으로 스트림에 대한 바이트 포인터를 가져오거나 설정합니다.</summary>
      <returns>바이트 포인터입니다.</returns>
      <exception cref="T:System.IndexOutOfRangeException">현재 위치가 스트림의 용량보다 큰 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">설정할 위치가 현재 스트림에서 올바르지 않은 경우</exception>
      <exception cref="T:System.IO.IOException">포인터가 스트림의 시작 위치 값보다 낮은 값으로 설정되는 경우</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Runtime.InteropServices.SafeBuffer" />에 사용할 수 있도록 스트림이 초기화되었습니다.<see cref="P:System.IO.UnmanagedMemoryStream.PositionPointer" /> 속성은 <see cref="T:System.Byte" /> 포인터를 사용하여 초기화된 스트림에만 사용할 수 있습니다.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이트 수를 지정된 배열로 읽어 들입니다.</summary>
      <returns>버퍼로 읽어온 총 바이트 수입니다.이 바이트 수는 현재 바이트가 충분하지 않은 경우 요청된 바이트 수보다 작을 수 있으며 스트림의 끝에 도달하면 0이 됩니다.</returns>
      <param name="buffer">이 메서드가 반환될 때 지정된 바이트 배열의 값은 <paramref name="offset" />과 (<paramref name="offset" /> + <paramref name="count" /> - 1) 사이에서 현재 원본으로부터 읽어온 바이트로 교체된 상태로 포함됩니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="offset">현재 스트림에서 읽은 데이터를 저장하기 시작하는 <paramref name="buffer" />의 바이트 오프셋(0부터 시작)입니다.</param>
      <param name="count">현재 스트림에서 읽을 최대 바이트 수입니다.</param>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <exception cref="T:System.NotSupportedException">내부 메모리가 읽기를 지원하지 않는 경우또는 <see cref="P:System.IO.UnmanagedMemoryStream.CanRead" /> 속성은 false로 설정됩니다. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 매개 변수가 null로 설정된 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 매개 변수가 0보다 작은 경우 또는 <paramref name="count" /> 매개 변수가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">버퍼 배열 길이에서 <paramref name="offset" /> 매개 변수를 뺀 값이 <paramref name="count" /> 매개 변수보다 작은 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>지정된 바이트 수를 지정된 배열로 비동기 방식으로 읽어 들입니다.
                .NET Framework 2015 에서 시작 가능</summary>
      <returns>비동기 읽기 작업을 나타내는 작업입니다.<paramref name="TResult" /> 매개 변수의 값은 버퍼 안으로 읽어들인 총 바이트 수를 포함합니다.현재 사용할 수 있는 바이트 수가 요청된 수보다 작을 경우 결과 값이 요청된 바이트 수보다 작을 수 있으며 스트림의 끝에 도달한 경우에는 0이 될 수도 있습니다.</returns>
      <param name="buffer">데이터를 쓸 버퍼입니다.</param>
      <param name="offset">스트림의 데이터를 쓰기 시작할 <paramref name="buffer" />의 바이트 오프셋입니다.</param>
      <param name="count">읽을 최대 바이트 수입니다.</param>
      <param name="cancellationToken">취소 요청을 모니터링할 토큰입니다.기본값은 <see cref="P:System.Threading.CancellationToken.None" />입니다.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.ReadByte">
      <summary>스트림에서 바이트를 읽고 스트림 내 위치를 한 바이트씩 앞으로 이동하거나 스트림 끝일 경우 -1을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Int32" /> 개체로 캐스팅된 부호 없는 바이트이거나, 스트림의 끝에 있는 경우 -1입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <exception cref="T:System.NotSupportedException">내부 메모리가 읽기를 지원하지 않는 경우또는현재 위치가 스트림의 끝에 있는 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>현재 스트림의 현재 위치를 주어진 값으로 설정합니다.</summary>
      <returns>스트림 내의 새 위치입니다.</returns>
      <param name="offset">검색을 시작할 <paramref name="origin" />에 상대적인 위치입니다. </param>
      <param name="loc">
        <see cref="T:System.IO.SeekOrigin" /> 형식의 값을 사용하여 시작, 끝 또는 현재 위치를 <paramref name="origin" />에 대한 참조 지점으로 지정합니다. </param>
      <exception cref="T:System.IO.IOException">스트림의 시작 위치 앞에서 검색하려고 한 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 값이 스트림의 최대 크기보다 큰 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="loc" />이 잘못되었습니다.</exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.SetLength(System.Int64)">
      <summary>스트림의 길이를 지정된 값으로 설정합니다.</summary>
      <param name="value">스트림의 길이입니다.</param>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생한 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <exception cref="T:System.NotSupportedException">내부 메모리가 쓰기를 지원하지 않는 경우또는스트림에 쓰려고 하는데 <see cref="P:System.IO.UnmanagedMemoryStream.CanWrite" /> 속성이 false인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">지정된 <paramref name="value" />가 스트림의 용량을 초과한 경우또는지정된 <paramref name="value" />가 음수인 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>버퍼의 데이터를 사용하여 현재 스트림에 바이트 블록을 씁니다.</summary>
      <param name="buffer">현재 스트림으로 복사할 바이트가 들어 있는 바이트 배열입니다.</param>
      <param name="offset">현재 스트림으로 바이트를 복사하기 시작할 버퍼의 오프셋입니다.</param>
      <param name="count">현재 스트림에 쓸 바이트 수입니다.</param>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <exception cref="T:System.NotSupportedException">내부 메모리가 쓰기를 지원하지 않는 경우 또는스트림에 쓰려고 하는데 <see cref="P:System.IO.UnmanagedMemoryStream.CanWrite" /> 속성이 false인 경우또는<paramref name="count" /> 값이 스트림의 용량보다 큰 경우또는위치가 스트림 용량의 끝에 있는 경우</exception>
      <exception cref="T:System.IO.IOException">I/O 오류가 발생하는 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">지정된 매개 변수 중 하나가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> 매개 변수에서 <paramref name="buffer" /> 매개 변수의 길이를 뺀 값이 <paramref name="count" /> 매개 변수보다 작은 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> 매개 변수가 null인 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>바이트의 시퀀스를 현재 스트림에 비동기적으로 쓰고 쓰여진 바이트 수만큼 이 스트림 내의 현재 위치를 앞으로 이동한 후 취소 요청을 모니터링합니다.
                .NET Framework 2015 에서 시작 가능</summary>
      <returns>비동기 쓰기 작업을 나타내는 작업입니다.</returns>
      <param name="buffer">데이터를 쓸 버퍼입니다.</param>
      <param name="offset">스트림으로 바이트를 복사하기 시작할 <paramref name="buffer" />의 바이트 오프셋(0부터 시작)입니다.</param>
      <param name="count">쓸 최대 바이트 수입니다.</param>
      <param name="cancellationToken">취소 요청을 모니터링할 토큰입니다.기본값은 <see cref="P:System.Threading.CancellationToken.None" />입니다.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.WriteByte(System.Byte)">
      <summary>파일 스트림의 현재 위치에 바이트를 씁니다.</summary>
      <param name="value">스트림에 쓰여지는 바이트 값입니다.</param>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <exception cref="T:System.NotSupportedException">내부 메모리가 쓰기를 지원하지 않는 경우또는스트림에 쓰려고 하는데 <see cref="P:System.IO.UnmanagedMemoryStream.CanWrite" /> 속성이 false인 경우또는 현재 위치가 스트림 용량의 끝에 있는 경우</exception>
      <exception cref="T:System.IO.IOException">제공된 <paramref name="value" />로 인해 스트림이 최대 용량을 초과하는 경우</exception>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>