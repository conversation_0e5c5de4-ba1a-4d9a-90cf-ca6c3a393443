﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.utils
{
    public class LogHelper
    {
        public static void WriteErrLog(string strErrCode)
        {
            try
            {
                string strTempPath = System.Windows.Forms.Application.StartupPath;//程序启动路径
                if (!Directory.Exists(strTempPath + "\\" + "Log"))//不存在Log日志文件夹则创建
                {
                    Directory.CreateDirectory(strTempPath + "\\" + "Log");
                }
                string strFileName = strTempPath + "\\" + "Log" + "\\" + "Log" + System.DateTime.Now.ToString("yyyyMMdd") + ".txt";

                StreamWriter sw = File.AppendText(strFileName);

                sw.WriteLine(System.DateTime.Now.ToString() + "," + strErrCode);

                sw.Flush();
                sw.Close();
            }
            catch
            {
                return;
            }
        }
    }
}
