﻿using SpermFormAnalysis.Class;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.Model;
using SpermFormAnalysis.Segments;
using SpermFormAnalysis.utils;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace SpermFormAnalysis.PageForm
{
    public partial class Searchs : UIPage
    {
        private int _ybpanel_width;
        private int _ybpanel_heigth;
        private GraphicsPath left_top_gp;
        private GraphicsPath left_bottom_gp;
        private GraphicsPath rigth_top_gp;
        private GraphicsPath rigth_bottom_gp;


        private GraphicsPath detail_icon_gp;
        private GraphicsPath detail_icon_gp2;
        private GraphicsPath detail_Panel_gp;

        private GraphicsPath prePic_btn_gp;
        private GraphicsPath nextPic_btn_gp;

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        public Searchs()
        {
            InitializeComponent();
            init();
            //this.PagePanel.Sh
            this.DoubleBuffered = true;
            ybpanel.MouseWheel += new MouseEventHandler(Form1_MouseWheel);
            //refresh();
            this.StartTestDate.Value = DateTime.Now;
            this.EndTestDate.Value = DateTime.Now;
            this.StartSendDate.Value = DateTime.Now;
            this.EndSendDate.Value = DateTime.Now;

            dataGridView1.AutoGenerateColumns = false;
            this.BindDataGridView();
        }
        /// <summary>
        /// 初始化界面
        /// </summary>
        private void init()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dataGridView1.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.dataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dataGridView1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dataGridView1.BackgroundColor = System.Drawing.Color.White;
            this.dataGridView1.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("微软雅黑", 11F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.DimGray;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.Color.DimGray;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridView1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dataGridView1.ColumnHeadersHeight = 45;
            this.dataGridView1.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dataGridView1.DefaultCellStyle = dataGridViewCellStyle3;
            this.dataGridView1.EnableHeadersVisualStyles = false;
            this.dataGridView1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dataGridView1.GridColor = System.Drawing.Color.Silver;// FromArgb(((int)(((byte)(139)))), ((int)(((byte)(144)))), ((int)(((byte)(151)))));
            this.dataGridView1.Margin = new System.Windows.Forms.Padding(0);
            this.dataGridView1.MultiSelect = false;
            this.dataGridView1.ReadOnly = true;
            this.dataGridView1.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(139)))), ((int)(((byte)(144)))), ((int)(((byte)(151)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(228)))), ((int)(((byte)(233)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("微软雅黑", 10F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.DimGray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.Silver;
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dataGridView1.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dataGridView1.RowHeadersWidth = 35;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.White;
            this.dataGridView1.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dataGridView1.RowTemplate.Height = 30;
            this.dataGridView1.SelectedIndex = -1;
            this.dataGridView1.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dataGridView1.ShowGridLine = true;
            this.dataGridView1.StripeOddColor = System.Drawing.Color.White;
            this.dataGridView1.Style = Sunny.UI.UIStyle.Custom;
            this.dataGridView1.TabIndex = 0;



            this.EditInfoBtn.BackColor = System.Drawing.Color.White;
            this.EditInfoBtn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.EditInfoBtn.FillColor = System.Drawing.Color.White;
            this.EditInfoBtn.ForeColor = System.Drawing.Color.DimGray;
            this.EditInfoBtn.MinimumSize = new System.Drawing.Size(1, 1);
            this.EditInfoBtn.RectColor = System.Drawing.Color.Gainsboro;
            this.EditInfoBtn.Style = Sunny.UI.UIStyle.Custom;

            //this.PushLisBtn.BackColor = System.Drawing.Color.White;
            //this.PushLisBtn.Cursor = System.Windows.Forms.Cursors.Hand;
            //this.PushLisBtn.FillColor = System.Drawing.Color.White;

            //this.PushLisBtn.ForeColor = System.Drawing.Color.DimGray;

            //this.PushLisBtn.MinimumSize = new System.Drawing.Size(1, 1);
            //this.PushLisBtn.RectColor = System.Drawing.Color.Gainsboro;
            //this.PushLisBtn.Style = Sunny.UI.UIStyle.Custom;


            this.uiSymbolButton2.BackColor = System.Drawing.Color.White;
            this.uiSymbolButton2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiSymbolButton2.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiSymbolButton2.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton2.RectColor = System.Drawing.Color.Gainsboro;
            this.uiSymbolButton2.Size = new System.Drawing.Size(35, 26);
            this.uiSymbolButton2.Style = Sunny.UI.UIStyle.Custom;

            this.btn_PrintRepor.BackColor = System.Drawing.Color.White;
            this.btn_PrintRepor.FillColor = System.Drawing.Color.White;
            this.btn_PrintRepor.ForeColor = System.Drawing.Color.DimGray;// FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btn_PrintRepor.RectColor = System.Drawing.Color.Gainsboro;
            this.btn_PrintRepor.Style = Sunny.UI.UIStyle.Custom;

            this.btn_Delete.BackColor = System.Drawing.Color.White;
            this.btn_Delete.FillColor = System.Drawing.Color.White;
            this.btn_Delete.ForeColor = System.Drawing.Color.DimGray;
            this.btn_Delete.RectColor = System.Drawing.Color.Gainsboro;
            this.btn_Delete.Style = Sunny.UI.UIStyle.Custom;

            this.btnsearchmore.BackColor = System.Drawing.Color.White;
            this.btnsearchmore.FillColor = System.Drawing.Color.Transparent;
            this.btnsearchmore.FillHoverColor = System.Drawing.Color.Silver;
            this.btnsearchmore.ForeHoverColor = System.Drawing.Color.DimGray;
            this.btnsearchmore.ForeColor = System.Drawing.Color.DimGray;
            this.btnsearchmore.RectColor = System.Drawing.Color.Transparent;

            this.btnshouqi.BackColor = System.Drawing.Color.White;
            this.btnshouqi.FillColor = System.Drawing.Color.Transparent;
            this.btnshouqi.FillHoverColor = System.Drawing.Color.Silver;
            this.btnshouqi.ForeHoverColor = System.Drawing.Color.DimGray;
            this.btnshouqi.ForeColor = System.Drawing.Color.DimGray;
            this.btnshouqi.RectColor = System.Drawing.Color.Transparent;


            this.uiSymbolButton5.BackColor = System.Drawing.Color.White;
            this.uiSymbolButton5.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton5.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton5.RectColor = System.Drawing.Color.Gainsboro;

            this.resetBtn.BackColor = System.Drawing.Color.White;
            this.resetBtn.FillColor = System.Drawing.Color.White;
            this.resetBtn.ForeColor = System.Drawing.Color.DimGray;
            this.resetBtn.RectColor = System.Drawing.Color.Gainsboro;

            this.uiSymbolButton8.BackColor = System.Drawing.Color.White;
            this.uiSymbolButton8.FillColor = System.Drawing.Color.White;
            this.uiSymbolButton8.ForeColor = System.Drawing.Color.DimGray;
            this.uiSymbolButton8.RectColor = System.Drawing.Color.Gainsboro;


            this.txt_sampleId.RectColor = System.Drawing.Color.Silver;
            this.txt_patientName.RectColor = System.Drawing.Color.Silver;
            this.txt_Diagnosis.RectColor = System.Drawing.Color.Silver;
            this.txt_patientId.RectColor = System.Drawing.Color.Silver;
            this.txt_sendDoctor.RectColor = System.Drawing.Color.Silver;
            this.txt_reviewer.RectColor = System.Drawing.Color.Silver;
            this.txt_inspectionDoctor.RectColor = System.Drawing.Color.Silver;
            // this.uiCheckBox1.CheckBoxColor = System.Drawing.Color.DimGray;


            this.StartSendDate.FillColor = System.Drawing.Color.White;
            this.StartSendDate.FillDisableColor = System.Drawing.Color.FromArgb(233, 233, 233);
            this.StartSendDate.RectColor = System.Drawing.Color.Silver;
            this.StartSendDate.RectDisableColor = System.Drawing.Color.Silver;
            this.EndSendDate.FillColor = System.Drawing.Color.White;
            this.EndSendDate.FillDisableColor = System.Drawing.Color.FromArgb(233, 233, 233);
            this.EndSendDate.RectColor = System.Drawing.Color.Silver;
            this.EndSendDate.RectDisableColor = System.Drawing.Color.Silver;

            this.StartTestDate.FillColor = System.Drawing.Color.White;
            this.StartTestDate.FillDisableColor = System.Drawing.Color.FromArgb(233, 233, 233);
            this.StartTestDate.RectColor = System.Drawing.Color.Silver;
            this.StartTestDate.RectDisableColor = System.Drawing.Color.Silver;
            this.EndTestDate.FillColor = System.Drawing.Color.White;
            this.EndTestDate.FillDisableColor = System.Drawing.Color.FromArgb(233, 233, 233);
            this.EndTestDate.RectColor = System.Drawing.Color.Silver;
            this.EndTestDate.RectDisableColor = System.Drawing.Color.Silver;

            uiComboBox2.Style = UIStyle.LightGray;
            SendDateCb.Style = UIStyle.LightGray;
            TestDateCb.Style = UIStyle.LightGray;
        }

        public Searchs(Mains main)
        {
            main.SearchFlushEvent += SearchFlush;
            InitializeComponent();
            init();
            SetStyle(ControlStyles.UserPaint, true);
            SetStyle(ControlStyles.AllPaintingInWmPaint, true); // 禁止擦除背景.
            SetStyle(ControlStyles.DoubleBuffer, true);
            ybpanel.MouseWheel += new MouseEventHandler(Form1_MouseWheel);
            this.StartTestDate.Value = DateTime.Now;
            this.EndTestDate.Value = DateTime.Now;
            this.StartSendDate.Value = DateTime.Now;
            this.EndSendDate.Value = DateTime.Now;

            dataGridView1.AutoGenerateColumns = false;
            this.BindDataGridView();
        }


        static bool IsFirstShow = true;
        void SearchFlush()
        {
            BindDataGridView();
        }


        private int Img_X;
        private int Img_Y;
        private int ImgWidth;
        private int ImgHeight;
        private Bitmap CurrImg;
        private Samplephoto CurrPhoto;
        private Bitmap PreImg;
        private Bitmap NextImg;
        /// <summary>
        /// 鼠标拖拽事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void Form1_MouseWheel(object sender, MouseEventArgs e)
        {
            if (CurrImg == null) return;
            if (e.Delta > 0)
            {
                if (ImgWidth > _ybpanel_width * 2) return;
                Img_X -= 16;
                Img_Y -= 10;
                ImgWidth += 32;
                ImgHeight += 20;
            }
            else
            {
                if (ImgWidth < 400) return;
                Img_X += Math.Min(Math.Max(-8, 16 - (Img_X + ImgWidth / 2 - _ybpanel_width / 2)), 32);
                Img_Y += Math.Min(Math.Max(-5, 10 - (Img_Y + ImgHeight / 2 - _ybpanel_heigth / 2)), 20);
                ImgWidth -= 32;
                ImgHeight -= 20;
            }
            this.ybpanel.Invalidate();
        }

        int RecordCount = 0;
        int PageSize = 20;
        int CurrPage = 1;
        string SearchSql = " 1=1 and processed=2 ";
        /// <summary>
        /// 绑定数据
        /// </summary>
        private void BindDataGridView()
        {
            //string sqlwhere = " 1=1 ";//用 来组合搜索结果
            dataGridView1.DataSource = SampleinfoServices.GetSampleInfoByPage(SearchSql, CurrPage, PageSize);
            RecordCount = SampleinfoServices.GetSampleCount(SearchSql);
            if (dataGridView1.RowCount > 0)
            {
                foreach (DataGridViewRow row in dataGridView1.Rows)
                {
                    if (row.Cells["sperm_num"].Value != null && row.Cells["normal_num"].Value != null)
                    {
                        double spermNum = Convert.ToDouble(row.Cells["sperm_num"].Value);
                        double normalnum = Convert.ToDouble(row.Cells["normal_num"].Value);
                        if (spermNum != 0)
                        {
                            row.Cells["qualification_rate"].Value = Math.Round((1.0 * normalnum / spermNum) * 100, 2);
                        }
                        else
                        {
                            row.Cells["qualification_rate"].Value = 0;
                        }
                    }
                }
                dataGridView1.SelectedIndex = 0;
                dataGridView1_CellClick(null, null);
            }
        }
        List<Samplephoto> infos = new List<Samplephoto>();
        private int p_index;//当前索引
        private static bool isMove = false;
        public static int ZoomStep;
        /// <summary>
        /// 列表选择事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dataGridView1_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            //if (e.RowIndex < 0) return;
            if (RecordCount == 0) return;
            int selindex = 0;
            if (e != null)
                selindex = e.RowIndex;
            if (e != null && e.RowIndex < 0)
            {
                return;
            }
            infos.Clear();
            var samp = dataGridView1.Rows[selindex].DataBoundItem as Sampleinfo;
            infos = SamplephotoServices.GetSamplePhoto(samp.sampleId).ToList();
            var subPath = "";
            for (int i = 0; i < infos.Count; i++)
            {
                subPath = infos[i].imageSource.Substring(infos[i].imageSource.IndexOf("photos"));
                infos[i].imageSource = AppDomain.CurrentDomain.BaseDirectory + subPath;
            }
            try
            {
                CurrImg = GetBitmap(0);
                CurrPhoto = infos[0];
            }
            catch (Exception ex)
            {
                CurrImg = null;
                //pictureBox.Image = null;
                ShowErrorTip(ex.Message);
            }
            p_index = 0;
            ybpanel.Invalidate();
        }
        /// <summary>
        /// 获取实验图片
        /// </summary>
        /// <param name="p_index"></param>
        /// <returns></returns>
        private Bitmap GetBitmap(int p_index)
        {

            Samplephoto info = infos[p_index];
            string subPath = info.imageSource.Substring(info.imageSource.IndexOf("photos"));
            Bitmap bitmap = new Bitmap(subPath);
            //获取该图上的精子数
            if (info.detailsList == null)
            {
                info.detailsList = new ObservableCollection<Sperm>();
                info.detailsList = SpermServices.GetObjects(info.id);
            }
            Graphics graphics = Graphics.FromImage(bitmap);
            graphics.Dispose();
            return bitmap;
        }
        /// <summary>
        /// 展开查询详细参数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnsearchmore_Click(object sender, EventArgs e)
        {
            int start_Y = this.uiGroupBox2.Location.Y;
            int start_Height = this.uiGroupBox2.Height;
            int step_num = 5;
            // 全长
            int totalDistance = 175;
            int step = totalDistance / step_num;

            for (int i = 1; i <= step_num; i++)
            {
                int margin = i < step_num ? step * i : totalDistance;
                this.uiGroupBox2.Size = new Size(this.uiGroupBox2.Width, start_Height - margin);
                this.uiGroupBox2.Location = new Point(this.uiGroupBox2.Location.X, start_Y + margin);
            }
            this.btnsearchmore.Hide();
            this.txt_sampleId.Focus();
        }
        /// <summary>
        /// 收起详细参数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnshouqi_Click(object sender, EventArgs e)
        {
            int start_Y = this.uiGroupBox2.Location.Y;
            int start_Height = this.uiGroupBox2.Height;
            int step_num = 15;
            // 全长
            int totalDistance = 175;
            int step = totalDistance / step_num;
            for (int i = 1; i <= step_num; i++)
            {
                int margin = i < step_num ? step * i : totalDistance;
                this.uiGroupBox2.Location = new Point(this.uiGroupBox2.Location.X, start_Y - margin);
                this.uiGroupBox2.Size = new Size(this.uiGroupBox2.Width, start_Height + margin);
            }
            this.btnsearchmore.Show();
        }
        /// <summary>
        /// 查询数据
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void searchBtn_Click(object sender, EventArgs e)
        {
            string strwhere = " 1=1 ";
            string temp = txt_sampleId.Text.Trim();
            if (!String.IsNullOrEmpty(temp))
                strwhere += " and sampleId = '" + temp + "' ";
            if (!String.IsNullOrEmpty(temp = this.txt_patientId.Text.TrimEnd()))
                strwhere += " and patientId = '" + temp + "' ";
            if (!String.IsNullOrEmpty(temp = this.txt_patientName.Text.TrimEnd()))
                strwhere += " and patientName = '" + temp + "' ";
            if (!String.IsNullOrEmpty(temp = this.txt_inspectionDoctor.Text.TrimEnd()))
                strwhere += " and inspectionDoctor = '" + temp + "' ";
            if (!String.IsNullOrEmpty(temp = this.txt_sendDoctor.Text.TrimEnd()))
                strwhere += " and sendDoctor = '" + temp + "' ";
            if (this.SendDateCb.Checked)
            {
                strwhere += " and sendtime >= '" + this.StartSendDate.Value.ToString("yyyy-MM-dd")
                    + "' and sendtime <= '" + this.EndSendDate.Value.ToString("yyyy-MM-dd") + "' ";
            }
            if (this.TestDateCb.Checked)
            {
                strwhere += " and scantime >= '" + this.StartTestDate.Value.ToString("yyyy-MM-dd")
                    + "' and scantime <= '" + this.EndTestDate.Value.ToString("yyyy-MM-dd") + "' ";
            }
            strwhere += " and processed=2 ";
            SearchSql = strwhere;
            this.BindDataGridView();
        }
        /// <summary>
        /// 导出报告
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_PrintRepor_MouseClick(object sender, MouseEventArgs e)
        {
            //searchBtn_Click(null, null);
            //若无选中行则不能执行删出操作
            if (this.dataGridView1.SelectedRows.Count <= 0 || this.dataGridView1.CurrentRow == null)
            {
                this.ShowErrorTip("请先选择要打印的报告！");
                return;
            }
            int index = this.dataGridView1.CurrentRow.Index;//获取选中行的行号

            var sample = dataGridView1.CurrentRow.DataBoundItem as Sampleinfo;
            //此处有很多计算

            //图表
            SeriesCollection series = chart1.Series;
            chart1.Series[0].Points[0].YValues[0] = sample.abnormal_num;//异常  samp.abnormal_rate;
            chart1.Series[0].Points[0].AxisLabel = sample.abnormal_num.ToString() + "(" + (sample.abnormal_rate * 100).ToString("f0") + "%)";
            chart1.Series[0].Points[1].YValues[0] = sample.normal_num;//正常
            chart1.Series[0].Points[1].AxisLabel = sample.normal_num + "(" + ((1 - sample.abnormal_rate) * 100).ToString("f0") + "%)";
            chart1.Invalidate();

            infos = SamplephotoServices.GetSamplePhoto(sample.sampleId).ToList();
            string ReportPath = "Reports" + "/" + DateTime.Now.ToString("yyyy") + "/" + DateTime.Now.ToString("MM") + "/";
            if (!System.IO.Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + ReportPath))
                System.IO.Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + ReportPath);
            string imgpicchart = AppDomain.CurrentDomain.BaseDirectory + ReportPath + sample.sampleId + ".png";//文件名为病人姓名+门诊编号
            chart1.SaveImage(imgpicchart, ChartImageFormat.Png);
            var subPath = "";
            for (int i = 0; i < infos.Count; i++)
            {
                subPath = infos[i].imageSource.Substring(infos[i].imageSource.IndexOf("photos"));
                infos[i].imageSource = AppDomain.CurrentDomain.BaseDirectory + subPath;
            }
            string PrintMsg = null;

            String[] PatientInfo = new String[] {
                GlobalProperty.Hospital,//院名
                sample.patientName,
                sample.patientage.ToString(),
                sample.patientId,
                sample.abstinenceDays.ToString(),
                sample.reviewed==0?"√":"",
                sample.reviewed==1?"√":"",
                sample.department,
                DateTime.Parse(sample.sendtime).ToString("yyyy-MM-dd"),
                sample.sendDoctor,//对应DoctorName
                sample.inspectionDoctor,
                sample.reviewer,
                DateTime.Parse(sample.processed_time).ToString("yyyy-MM-dd"),
                DateTime.Now.ToString("yyyy-MM-dd"),
                sample.sperm_num.ToString(),
                sample.normal_num.ToString(),
                (100*(1-sample.abnormal_rate)).ToString("f2"),
                sample.abnormal_num.ToString(),
                (sample.abnormal_rate*100).ToString("f2"),
                //sample.abnormal_num.ToString(),
                //((1D-sample.qualification_rate)*100).ToString("f0"),
                sample.abnormalshape_num.ToString(),
                ((1.0*sample.abnormalshape_num/sample.sperm_num)*100).ToString("f2"),
                sample.abnormalmiddlepiece_num.ToString(),
                ((1.0*sample.abnormalmiddlepiece_num/sample.sperm_num)*100).ToString("f2"),
                sample.abnormalTail_num.ToString(),
                ((1.0*sample.abnormalTail_num/sample.sperm_num)*100).ToString("f2"),
                sample.ERC_num.ToString(),
                ((1.0*sample.ERC_num/sample.sperm_num)*100).ToString("f2"),
                (1.0*(sample.abnormalshape_num+sample.abnormalmiddlepiece_num+sample.abnormalTail_num)/sample.abnormal_num).ToString("f2"),
                (1.0*(sample.abnormalshape_num+sample.abnormalmiddlepiece_num+sample.abnormalTail_num)/sample.sperm_num).ToString("f2"),
                (100*(1-sample.abnormal_rate)).ToString("f2"),
            };

            if (!ShowReport.printReport(PatientInfo, imgpicchart, infos[0].imageSource, true, ref PrintMsg))
            {
                ShowErrorDialog(PrintMsg ?? "打印失败！");
            }
        }
        /// <summary>
        /// 删除记录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_Delete_Click(object sender, EventArgs e)
        {
            if (this.dataGridView1.SelectedRows.Count <= 0)
            {
                this.ShowErrorTip("请先选择要删除的记录！");
                //MessageBox.Show("请先选择要删除的记录！", "友情提示");
                return;
            }

            if (!ShowAskDialog("确定删除？")) return;
            int index = this.dataGridView1.SelectedRows[0].Index;

            string sampleId = ((Sampleinfo)this.dataGridView1.Rows[index].DataBoundItem).sampleId;
            int samid = ((Sampleinfo)this.dataGridView1.Rows[index].DataBoundItem).Id;
            //删除实例。删除图片，删除精子
            SampleinfoServices.DeleteObject(samid);
            SamplephotoServices.DeletePhotos(sampleId);
            SpermServices.DelSperm(sampleId);
            ShowSuccessNotifier("删除成功");
            this.RecordCount--;
            this.BindDataGridView();
        }
        /// <summary>
        /// 清空查询参数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void resetBtn_Click(object sender, EventArgs e)
        {
            //将所有TextBox清空
            this.txt_sampleId.Text = "";
            this.txt_patientId.Text = "";
            this.txt_patientName.Text = "";
            //将试验时间设为当前时间
            this.StartTestDate.Value = DateTime.Now;
            this.EndTestDate.Value = DateTime.Now;
            this.StartSendDate.Value = DateTime.Now;
            this.EndSendDate.Value = DateTime.Now;
            SendDateCb.Checked = false;
            TestDateCb.Checked = false;
            this.txt_sendDoctor.Text = "";
            this.txt_inspectionDoctor.Text = "";
            this.txt_reviewer.Text = "";
            txt_Diagnosis.Text = "";
        }

        string year_month = "202204";
        /// <summary>
        /// 试验记录辅助纠错
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton8_Click(object sender, EventArgs e)
        {
            if (dataGridView1.Rows.Count == 0) return;
            int t_index = this.dataGridView1.SelectedRows[0].Index;
            Sampleinfo model = dataGridView1.Rows[t_index].DataBoundItem as Sampleinfo;
            new CellDetail(ref infos, p_index, this.year_month, ref model).ShowDialog();
            this.CurrImg = GetBitmap(p_index);
            this.BindDataGridView();
            ybpanel.Invalidate();
        }
        /// <summary>
        /// 实验参数修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void EditInfoBtn_Click(object sender, EventArgs e)
        {
            if (this.dataGridView1.Rows.Count == 0) return;
            int index = this.dataGridView1.CurrentRow == null ? 0 : this.dataGridView1.CurrentRow.Index;//获取选中行的行号
            var sample = dataGridView1.CurrentRow.DataBoundItem as Sampleinfo;

            if (new Sample(ref sample).ShowDialog() != DialogResult.OK) return;
            this.dataGridView1.Rows[index].Cells["patientage"].Value = sample.patientage;
            this.dataGridView1.Rows[index].Cells["patientName"].Value = sample.patientName;
            //this.dataGridView1.Rows[index].Cells["reviewed"].Value = sample.reviewed;
            //this.dataGridView1.Rows[index].Cells["sendDoctor"].Value = sample.sendDoctor;
            //this.dataGridView1.Rows[index].Cells["processed"].Value = sample.processed;
            //this.dataGridView1.Rows[index].Cells["Send_Date"].Value = sample.sendtime;
            //this.dataGridView1.Rows[index].Cells["Test_Date"].Value = model.Test_Date.ToString();
            //this.dataGridView1.Rows[index].Cells["Remark"].Value = model.Remark;
            this.dataGridView1.Invalidate();
        }
        private void ybpanel_Layout(object sender, LayoutEventArgs e)
        {
            int r = 15;
            _ybpanel_width = ybpanel.Width;
            _ybpanel_heigth = ybpanel.Height;
            var imgWidth = 1920;
            var imgHeight = 1200;
            if (CurrImg != null)
            {
                imgWidth = CurrImg.Width;
                imgHeight = CurrImg.Height;
            }
            if ((_ybpanel_width - 8 * r) > (_ybpanel_heigth - 2 * r - 50) * imgWidth / imgHeight)
            {
                this.ImgWidth = (_ybpanel_heigth - 2 * r - 50) * imgWidth / imgHeight;
                this.ImgHeight = (_ybpanel_heigth - 2 * r - 50);
            }
            else
            {
                this.ImgWidth = (_ybpanel_width - 8 * r);
                this.ImgHeight = (_ybpanel_width - 8 * r) * imgHeight / imgWidth;
            }
            this.Img_X = (_ybpanel_width - ImgWidth) / 2;
            this.Img_Y = (_ybpanel_heigth - ImgHeight) / 2;

            left_top_gp = new GraphicsPath();
            left_top_gp.AddLine(-r, -r, r, 0);
            left_top_gp.AddArc(new RectangleF(0, 0, 2 * r, 2 * r), 270f, -90f);
            left_bottom_gp = new GraphicsPath();
            left_bottom_gp.AddLine(-r, _ybpanel_heigth + r, r, _ybpanel_heigth);
            left_bottom_gp.AddArc(new RectangleF(0, _ybpanel_heigth - 2 * r, 2 * r, 2 * r), 90f, 90f);
            rigth_top_gp = new GraphicsPath();
            rigth_top_gp.AddLine(_ybpanel_width + r, -r, _ybpanel_width, r);
            rigth_top_gp.AddArc(new RectangleF(_ybpanel_width - 2 * r, 0, 2 * r, 2 * r), 0f, -90f);
            rigth_bottom_gp = new GraphicsPath();
            rigth_bottom_gp.AddArc(new RectangleF(_ybpanel_width - 2 * r, _ybpanel_heigth - 2 * r, 2 * r, 2 * r), 0f, 90f);
            rigth_bottom_gp.AddLine(_ybpanel_width - r, _ybpanel_heigth, _ybpanel_width, _ybpanel_heigth);
            detail_icon_gp = new GraphicsPath();
            detail_icon_gp.AddLine(_ybpanel_width - 33, 29, _ybpanel_width - 29, 33);
            detail_icon_gp.AddLine(_ybpanel_width - 29, 33, _ybpanel_width - 33, 37);
            detail_icon_gp2 = new GraphicsPath();
            detail_icon_gp2.AddLine(_ybpanel_width - 29, 29, _ybpanel_width - 33, 33);
            detail_icon_gp2.AddLine(_ybpanel_width - 33, 33, _ybpanel_width - 29, 37);
            //detail_Panel_gp = GraphicsUtils.GetRectangleGp(_ybpanel_width - 280, 60, 220, _ybpanel_heigth - 120, 15);
            prePic_btn_gp = new GraphicsPath();
            prePic_btn_gp.AddLine(35, _ybpanel_heigth / 2 - 25, 15, _ybpanel_heigth / 2);
            prePic_btn_gp.AddLine(15, _ybpanel_heigth / 2, 35, _ybpanel_heigth / 2 + 25);

            nextPic_btn_gp = new GraphicsPath();
            nextPic_btn_gp.AddLine(_ybpanel_width - 35, _ybpanel_heigth / 2 - 25, _ybpanel_width - 15, _ybpanel_heigth / 2);
            nextPic_btn_gp.AddLine(_ybpanel_width - 15, _ybpanel_heigth / 2, _ybpanel_width - 35, _ybpanel_heigth / 2 + 25);
            /* TemplateBitmap = new Bitmap(220, _ybpanel_heigth - 140);
             Graphics graphics = Graphics.FromImage(TemplateBitmap);
             graphics.SmoothingMode = SmoothingMode.HighQuality;
             GraphicsUtils.FillRectangleGp(0, 0, TemplateBitmap.Width, TemplateBitmap.Height, 15, Color.FromArgb(111,33,33,33), graphics);
             int left_X = 20;
             int temp_Y = 30;
             graphics.DrawString("门诊编号:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("实验编号:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("DFI:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("患者姓名:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("年龄:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("样本类型:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("送检日期:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("送检医生:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("检测日期:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("检测人:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("复核人:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("备注:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("细胞总数", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("正常细胞:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("异常细胞:", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("细胞总数(当前图):", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("正常细胞(当前图):", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.DrawString("异常细胞(当前图):", GraphicsUtils.Font10, GraphicsUtils.WhiteBrush, left_X, temp_Y += 30);
             graphics.Dispose();*/
        }


        private int offset_X;
        private int offset_Y;
        private int detail_panel_X;
        private bool show_detail;
        private void ybpanel_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && infos.Count > 0 && p_index != 0 && e.X > 15 && e.X < 40
    && e.Y > _ybpanel_heigth / 2 - 25 && e.Y < _ybpanel_heigth / 2 + 25)
            {
                try
                {
                    //PreImg = Image.FromFile(infos[--index].PicPath);
                    PreImg = GetBitmap(--p_index);
                    CurrPhoto = infos[p_index];
                }
                catch (Exception ex)
                {
                    PreImg = null;
                    ShowErrorTip(ex.Message);
                }
                int temp_x = Img_X;
                int step = 100;
                while ((Img_X += (step += 30)) < _ybpanel_width)
                {
                    ybpanel.Refresh();
                }
                Img_X = temp_x;
                CurrImg = PreImg;
                PreImg = null;
                ybpanel.Invalidate();
            }
            else if (e.Button == MouseButtons.Left && infos.Count > 0 && p_index != infos.Count - 1
                && e.X > _ybpanel_width - 40 && e.X < _ybpanel_width - 15
                && e.Y > _ybpanel_heigth / 2 - 25 && e.Y < _ybpanel_heigth / 2 + 25)
            {
                try
                {
                    //NextImg = Image.FromFile(infos[++index].PicPath);
                    //var info = infos[++index];
                    NextImg = GetBitmap(++p_index);
                    CurrPhoto = infos[p_index];
                }
                catch (Exception ex)
                {
                    NextImg = null;
                    ShowErrorTip(ex.Message);
                }
                int temp_x = Img_X;
                int step = 100;
                while ((Img_X -= (step += 30)) > -ImgWidth)
                {
                    ybpanel.Refresh();
                }
                Img_X = temp_x;
                CurrImg = NextImg;
                NextImg = null;
                ybpanel.Invalidate();
            }
            else if (e.Button == MouseButtons.Left &&
                e.X > _ybpanel_width - 90 && e.X < _ybpanel_width - 27
                && e.Y > 25 && e.Y < 42 && dataGridView1.Rows.Count > 0)
            {
                //var a = 1;
                if (!show_detail)
                {
                    show_detail = !show_detail;
                    for (int i = 1; i <= 14; i++)
                    {
                        detail_panel_X = _ybpanel_width - i * 20;
                        ybpanel.Refresh();
                    }
                }
                else
                {
                    for (int i = 1; i <= 14; i++)
                    {
                        detail_panel_X = _ybpanel_width - 240 + i * 20;
                        ybpanel.Refresh();
                    }
                    show_detail = !show_detail;
                }

                ybpanel.Invalidate();
            }
            else if (e.Button == MouseButtons.Left &&
                e.X > Img_X && e.X < Img_X + ImgWidth
                && e.Y > Img_Y && e.Y < Img_Y + ImgHeight)
            {
                offset_X = e.X - Img_X;
                offset_Y = e.Y - Img_Y;
                isMove = true;
            }
        }

        private void ybpanel_MouseMove(object sender, MouseEventArgs e)
        {
            if (isMove)
            {
                Img_X = Math.Min(Math.Max(e.X - offset_X, 192 - ImgWidth), _ybpanel_width - 192);
                Img_Y = Math.Min(Math.Max(e.Y - offset_Y, 120 - ImgHeight), _ybpanel_heigth - 120);
                ybpanel.Invalidate();
            }
        }

        private void ybpanel_MouseUp(object sender, MouseEventArgs e)
        {
            isMove = false;
        }

        private void ybpanel_Paint(object sender, PaintEventArgs e)
        {
            Graphics graphics = e.Graphics;

            if (CurrImg != null)
            {
                graphics.DrawImage(CurrImg, Img_X, Img_Y, ImgWidth, ImgHeight);
                var result = SpermServices.GetObjects(CurrPhoto.id).ToList();
                SpermFormAnalysis.common.Drawing.DrawTails(Img_X, Img_Y, graphics, ImgWidth, ImgHeight, result);//画尾巴
                SpermFormAnalysis.common.Drawing.DrawVacuoles(Img_X, Img_Y, graphics, ImgWidth, ImgHeight, result);//画空泡
                SpermFormAnalysis.common.Drawing.DrawPolygons(Img_X, Img_Y, graphics, ImgWidth, ImgHeight, result);//画精子
                SpermFormAnalysis.common.Drawing.DrawBoxs(Img_X,Img_Y, graphics, ImgWidth, ImgHeight, result);//画文字说明
                if (result.Count > 0 && result != null)
                {
                    double originalWidth = 1344;
                    double originalHeight = 840;
                    // 计算宽度和高度的比例
                    double widthRatio = ImgWidth / originalWidth;
                    double heightRatio = ImgHeight / originalHeight;
                    for (int i = 0; i < result.Count; i++)
                    {
                        double num1;
                        double num2;
                        double width;
                        double height;
                        clsMrfSegment.getBox(result[i], ImgWidth, ImgHeight, out num1, out num2, out width, out height);
                        if (result[i].isWrite == 2)
                        {
                            var rect = new Rectangle();
                            rect.X = (int)(result[i].xmin * widthRatio)+60;
                            rect.Y = (int)(result[i].ymin * heightRatio)+90;
                            rect.Width = (int)(((result[i].xmax - result[i].xmin)) * widthRatio);
                            rect.Height = (int)((result[i].ymax - result[i].ymin) * heightRatio);
                            if (result[i].isNormal == 1)
                            {
                                using (System.Drawing.Pen pen = new System.Drawing.Pen(System.Drawing.Color.Green, 2))
                                {
                                    graphics.DrawRectangle(pen, rect);
                                }
                            }
                            else
                            {
                                using (System.Drawing.Pen pen = new System.Drawing.Pen(System.Drawing.Color.Red, 2))
                                {
                                    graphics.DrawRectangle(pen, rect);
                                }
                            }
                        }
                        else
                        {
                            if (result[i].isNormal == 1)
                            {
                                graphics.DrawRectangle(Pens.Green, (float)(Img_X + num1), (float)(Img_Y + num2), (float)width, (float)height);
                            }
                            else
                            {
                                graphics.DrawRectangle(Pens.Red, (float)(Img_X + num1), (float)(Img_Y + num2), (float)width, (float)height);
                            }
                        }
                    }

                }
            }
            if (PreImg != null)
            {
                graphics.DrawImage(PreImg, Img_X - _ybpanel_width, Img_Y, ImgWidth, ImgHeight);
            }
            else if (NextImg != null)
            {
                graphics.DrawImage(NextImg, Img_X + _ybpanel_width, Img_Y, ImgWidth, ImgHeight);
            }

            //

            //return;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            graphics.DrawString("样本图片", GraphicsUtils.Font12B, GraphicsUtils.DimGrayBrush, 20, 20);

            if (infos.Count > 0 && p_index != 0)
            {
                graphics.DrawPath(GraphicsUtils.Pen111333333_15, prePic_btn_gp);
            }
            if (infos.Count > 0 && p_index != infos.Count - 1)
            {
                graphics.DrawPath(GraphicsUtils.Pen111333333_15, nextPic_btn_gp);
            }
        }
        /// <summary>
        /// 列表呈现行数变化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiComboBox2_SelectedIndexChanged(object sender, EventArgs e)
        {
            int index = this.uiComboBox2.SelectedIndex;
            this.PageSize = index == 3 ? 100 : index == 2 ? 50 : index == 1 ? 20 : 10;
            CurrPage = 1;//设置当前页面为1
            this.BindDataGridView();
        }

        private void Searchs_Load(object sender, EventArgs e)
        {
            uiComboBox2.SelectedIndex = 3;
        }

        private void uiDoubleUpDown1_ValueChanged(object sender, double value)
        {
            if (value == CurrPage) return;
            CurrPage = (int)value;
            this.BindDataGridView();
        }
    }
}
