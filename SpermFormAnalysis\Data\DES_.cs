﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.Data
{
    public class DES_
    {
        private DES mydes;

        public string Key;

        public string IV;

        public DES_(string key)
        {
            mydes = new DESCryptoServiceProvider();
            Key = key;
            IV = "728#$$%^TyguyshdsufhsfwofnhKJHJKHIYhfiusf98*(^%$^&&(*&()$##@%%$RHGJJHHJ";
            int length = IV.Length;
        }

        public DES_(string key, string iv)
        {
            mydes = new DESCryptoServiceProvider();
            Key = key;
            IV = iv;
        }

        private byte[] GetLegalKey()
        {
            string text = Key;
            mydes.GenerateKey();
            byte[] key = mydes.Key;
            int num = key.Length;
            if (text.Length > num)
            {
                text = text.Substring(0, num);
            }
            else if (text.Length < num)
            {
                text = text.PadRight(num, ' ');
            }
            return Encoding.ASCII.GetBytes(text);
        }

        private byte[] GetLegalIV()
        {
            string text = IV;
            mydes.GenerateIV();
            byte[] iV = mydes.IV;
            int num = iV.Length;
            if (text.Length > num)
            {
                text = text.Substring(0, num);
            }
            else if (text.Length < num)
            {
                text = text.PadRight(num, ' ');
            }
            return Encoding.ASCII.GetBytes(text);
        }

        public string Encrypt(string Source)
        {
            try
            {
                byte[] bytes = Encoding.UTF8.GetBytes(Source);
                MemoryStream memoryStream = new MemoryStream();
                mydes.Key = GetLegalKey();
                mydes.IV = GetLegalIV();
                ICryptoTransform transform = mydes.CreateEncryptor();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, transform, CryptoStreamMode.Write);
                cryptoStream.Write(bytes, 0, bytes.Length);
                cryptoStream.FlushFinalBlock();
                memoryStream.Close();
                byte[] inArray = memoryStream.ToArray();
                return Convert.ToBase64String(inArray);
            }
            catch (Exception ex)
            {
                throw new Exception("在文件加密的时候出现错误！错误提示： \n" + ex.Message);
            }
        }

        public string Decrypt(string Source)
        {
            if (Source.Trim() == "") return "";
            try
            {
                byte[] array = Convert.FromBase64String(Source);
                MemoryStream stream = new MemoryStream(array, 0, array.Length);
                mydes.Key = GetLegalKey();
                mydes.IV = GetLegalIV();
                ICryptoTransform transform = mydes.CreateDecryptor();
                CryptoStream stream2 = new CryptoStream(stream, transform, CryptoStreamMode.Read);
                StreamReader streamReader = new StreamReader(stream2);
                return streamReader.ReadToEnd();
            }
            catch (Exception ex)
            {
                throw new Exception("在文件解密的时候出现错误！错误提示： \n" + ex.Message);
            }
        }

        public byte[] Encrypt(byte[] Source)
        {
            try
            {
                MemoryStream memoryStream = new MemoryStream();
                mydes.Key = GetLegalKey();
                mydes.IV = GetLegalIV();
                ICryptoTransform transform = mydes.CreateEncryptor();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, transform, CryptoStreamMode.Write);
                cryptoStream.Write(Source, 0, Source.Length);
                cryptoStream.FlushFinalBlock();
                memoryStream.Close();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                throw new Exception("在文件加密的时候出现错误！错误提示： \n" + ex.Message);
            }
        }

        public byte[] Decrypt(byte[] Source)
        {
            try
            {
                MemoryStream stream = new MemoryStream(Source, 0, Source.Length);
                mydes.Key = GetLegalKey();
                mydes.IV = GetLegalIV();
                ICryptoTransform transform = mydes.CreateDecryptor();
                CryptoStream stream2 = new CryptoStream(stream, transform, CryptoStreamMode.Read);
                StreamReader streamReader = new StreamReader(stream2);
                return Encoding.UTF8.GetBytes(streamReader.ReadToEnd());
            }
            catch (Exception ex)
            {
                throw new Exception("在文件解密的时候出现错误！错误提示： \n" + ex.Message);
            }
        }

        public void Encrypt(string inFileName, string outFileName)
        {
            try
            {
                FileStream fileStream = new FileStream(inFileName, FileMode.Open, FileAccess.Read);
                FileStream fileStream2 = new FileStream(outFileName, FileMode.OpenOrCreate, FileAccess.Write);
                fileStream2.SetLength(0L);
                mydes.Key = GetLegalKey();
                mydes.IV = GetLegalIV();
                byte[] buffer = new byte[100];
                long num = 0L;
                long length = fileStream.Length;
                ICryptoTransform transform = mydes.CreateEncryptor();
                CryptoStream cryptoStream = new CryptoStream(fileStream2, transform, CryptoStreamMode.Write);
                int num2;
                for (; num < length; num += num2)
                {
                    num2 = fileStream.Read(buffer, 0, 100);
                    cryptoStream.Write(buffer, 0, num2);
                }
                cryptoStream.Close();
                fileStream2.Close();
                fileStream.Close();
            }
            catch (Exception ex)
            {
                throw new Exception("在文件加密的时候出现错误！错误提示： \n" + ex.Message);
            }
        }

        public void Decrypt(string inFileName, string outFileName)
        {
            try
            {
                FileStream fileStream = new FileStream(inFileName, FileMode.Open, FileAccess.Read);
                FileStream fileStream2 = new FileStream(outFileName, FileMode.OpenOrCreate, FileAccess.Write);
                fileStream2.SetLength(0L);
                byte[] buffer = new byte[100];
                long num = 0L;
                long length = fileStream.Length;
                mydes.Key = GetLegalKey();
                mydes.IV = GetLegalIV();
                ICryptoTransform transform = mydes.CreateDecryptor();
                CryptoStream cryptoStream = new CryptoStream(fileStream2, transform, CryptoStreamMode.Write);
                int num2;
                for (; num < length; num += num2)
                {
                    num2 = fileStream.Read(buffer, 0, 100);
                    cryptoStream.Write(buffer, 0, num2);
                }
                cryptoStream.Close();
                fileStream2.Close();
                fileStream.Close();
            }
            catch (Exception ex)
            {
                throw new Exception("在文件解密的时候出现错误！错误提示： \n" + ex.Message);
            }
        }
    }
}
