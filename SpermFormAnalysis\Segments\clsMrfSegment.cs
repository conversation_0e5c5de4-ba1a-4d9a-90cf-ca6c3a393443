﻿using Emgu.CV;
using Emgu.CV.CvEnum;
using Sunny.UI.Win32;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TFRunModel;
using SpermFormAnalysis.TFmodel;
using SpermFormAnalysis.utils;

namespace SpermFormAnalysis.Segments
{
    public static class clsMrfSegment
    {
        // Token: 0x17000195 RID: 405
        // (get) Token: 0x0600055E RID: 1374 RVA: 0x0000494F File Offset: 0x00002B4F
        // (set) Token: 0x0600055F RID: 1375 RVA: 0x00004956 File Offset: 0x00002B56
        public static int innerT { get; set; } = 0;

        // Token: 0x17000196 RID: 406
        // (get) Token: 0x06000560 RID: 1376 RVA: 0x0000495E File Offset: 0x00002B5E
        // (set) Token: 0x06000561 RID: 1377 RVA: 0x00004965 File Offset: 0x00002B65
        public static int outerT { get; set; } = -10;

        // Token: 0x17000197 RID: 407
        // (get) Token: 0x06000562 RID: 1378 RVA: 0x0000496D File Offset: 0x00002B6D
        // (set) Token: 0x06000563 RID: 1379 RVA: 0x00004974 File Offset: 0x00002B74
        public static double vacuolesT { get; set; } = 1.5;

        // Token: 0x17000198 RID: 408
        // (get) Token: 0x06000564 RID: 1380 RVA: 0x0000497C File Offset: 0x00002B7C
        // (set) Token: 0x06000565 RID: 1381 RVA: 0x00004983 File Offset: 0x00002B83
        public static double vacuolesAreaT { get; set; } = 1.0;

        // Token: 0x17000199 RID: 409
        // (get) Token: 0x06000566 RID: 1382 RVA: 0x0000498B File Offset: 0x00002B8B
        // (set) Token: 0x06000567 RID: 1383 RVA: 0x00004992 File Offset: 0x00002B92
        public static int outerEnlarge { get; set; } = 0;

        // Token: 0x1700019A RID: 410
        // (get) Token: 0x06000568 RID: 1384 RVA: 0x0000499A File Offset: 0x00002B9A
        // (set) Token: 0x06000569 RID: 1385 RVA: 0x000049A1 File Offset: 0x00002BA1
        public static int kernelEnlarge { get; set; } = 0;

        // Token: 0x1700019B RID: 411
        // (get) Token: 0x0600056A RID: 1386 RVA: 0x000049A9 File Offset: 0x00002BA9
        // (set) Token: 0x0600056B RID: 1387 RVA: 0x000049B0 File Offset: 0x00002BB0
        public static int kenelSpitArea { get; set; } = 2500;

        // Token: 0x1700019C RID: 412
        // (get) Token: 0x0600056C RID: 1388 RVA: 0x000049B8 File Offset: 0x00002BB8
        // (set) Token: 0x0600056D RID: 1389 RVA: 0x000049BF File Offset: 0x00002BBF
        public static int acrosomeArea { get; set; } = 3000;

        // Token: 0x1700019D RID: 413
        // (get) Token: 0x0600056E RID: 1390 RVA: 0x000049C7 File Offset: 0x00002BC7
        // (set) Token: 0x0600056F RID: 1391 RVA: 0x000049CE File Offset: 0x00002BCE
        public static int acrosomeSplitNum { get; set; } = 15;

        // Token: 0x1700019E RID: 414
        // (get) Token: 0x06000570 RID: 1392 RVA: 0x000049D6 File Offset: 0x00002BD6
        // (set) Token: 0x06000571 RID: 1393 RVA: 0x000049DD File Offset: 0x00002BDD
        public static int vacuolesErodeNum { get; set; } = 1;

        // Token: 0x1700019F RID: 415
        // (get) Token: 0x06000572 RID: 1394 RVA: 0x000049E5 File Offset: 0x00002BE5
        // (set) Token: 0x06000573 RID: 1395 RVA: 0x000049EC File Offset: 0x00002BEC
        public static double AIScoreT { get; set; } = 0.8;

        // Token: 0x170001A0 RID: 416
        // (get) Token: 0x06000574 RID: 1396 RVA: 0x000049F4 File Offset: 0x00002BF4
        // (set) Token: 0x06000575 RID: 1397 RVA: 0x000049FB File Offset: 0x00002BFB
        public static double middlepieceV { get; set; } = 0.0;

        // Token: 0x170001A1 RID: 417
        // (get) Token: 0x06000576 RID: 1398 RVA: 0x00004A03 File Offset: 0x00002C03
        // (set) Token: 0x06000577 RID: 1399 RVA: 0x00004A0A File Offset: 0x00002C0A
        public static double kernelStdDev { get; set; } = 12.0;

        // Token: 0x06000578 RID: 1400 RVA: 0x0001E734 File Offset: 0x0001C934
        /// <summary>
        /// 获取矩形区域
        /// </summary>
        /// <param name="sp"></param>
        /// <param name="imgWidth"></param>
        /// <param name="imgHeight"></param>
        /// <param name="x"></param>
        /// <param name="y"></param>
        /// <param name="width"></param>
        /// <param name="height"></param>
        public static void getBox(Model.Sperm sp, double imgWidth, double imgHeight, out double x, out double y, out double width, out double height)
        {
            x = (y = (width = (height = 0.0)));
            bool flag = sp == null;
            if (!flag)
            {
                x = imgWidth * (double)sp.xmin - 20.0 * imgWidth / (double)TFmodel.common.resolutionRatio.Width;
                x = ((x < 0.0) ? 0.0 : x);

                // 记录第一个精子的getBox计算过程
                if (sp.spermindex == 1)
                {
                    LogHelper.WriteErrLog($"原项目getBox: 精子{sp.spermindex}, 原始边界({sp.xmin:F3},{sp.ymin:F3})-({sp.xmax:F3},{sp.ymax:F3})");
                    LogHelper.WriteErrLog($"原项目getBox: 图像尺寸({imgWidth:F1}x{imgHeight:F1}), 分辨率比例({TFmodel.common.resolutionRatio.Width}x{TFmodel.common.resolutionRatio.Height})");
                }
                width = imgWidth * (double)(sp.xmax - sp.xmin) + 40.0 * imgWidth / (double)TFmodel.common.resolutionRatio.Width;
                bool flag2 = x + width >= imgWidth;
                if (flag2)
                {
                    width = imgWidth - x - 1.0;
                }
                y = (double)((int)(imgHeight * (double)sp.ymin)) - 20.0 * imgHeight / (double)TFmodel.common.resolutionRatio.Height;
                y = ((y < 0.0) ? 0.0 : y);
                height = imgHeight * (double)(sp.ymax - sp.ymin) + 40.0 * imgHeight / (double)TFmodel.common.resolutionRatio.Height;
                bool flag3 = y + height >= imgHeight;
                if (flag3)
                {
                    height = imgHeight - y - 1.0;
                }

                // 记录第一个精子的最终getBox结果
                if (sp.spermindex == 1)
                {
                    LogHelper.WriteErrLog($"原项目getBox结果: 精子{sp.spermindex}, 最终坐标({x:F1},{y:F1}), 尺寸({width:F1}x{height:F1})");
                }
            }
        }


        // Token: 0x040002F3 RID: 755
        public static double minDefectArea = 25.0;

        // Token: 0x040002F4 RID: 756
        public static double minorDefectArea = 20.0;

        // Token: 0x040002F5 RID: 757
        public static double kernelFitness = 0.84;

        // Token: 0x040002F6 RID: 758
        public static double defectsDepth = 2.0;

        // Token: 0x040002F7 RID: 759
        public static double defectsNum = 3.0;

        // Token: 0x040002F8 RID: 760
        public static double hugeDefectArea = 400.0;

        // Token: 0x040002F9 RID: 761
        private static int count = 0;

        // Token: 0x040002FA RID: 762
        private static Mat element5x3 = CvInvoke.GetStructuringElement(ElementShape.Rectangle, new Size(5, 3), new Point(-1, -1));

        // Token: 0x040002FB RID: 763
        private static Mat element3x5 = CvInvoke.GetStructuringElement(ElementShape.Rectangle, new Size(3, 5), new Point(-1, -1));

        // Token: 0x040002FC RID: 764
        private static Mat element3x3 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(3, 3), new Point(-1, -1));

        // Token: 0x040002FD RID: 765
        private static Mat element4x4 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(4, 4), new Point(-1, -1));

        // Token: 0x040002FE RID: 766
        private static Mat element3x3rect = CvInvoke.GetStructuringElement(ElementShape.Rectangle, new Size(3, 3), new Point(-1, -1));

        // Token: 0x040002FF RID: 767
        private static Mat element3x3cross = CvInvoke.GetStructuringElement(ElementShape.Cross, new Size(3, 3), new Point(-1, -1));

        // Token: 0x04000300 RID: 768
        private static Mat element5x5 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(5, 5), new Point(-1, -1));

        // Token: 0x04000301 RID: 769
        private static Mat element6x6 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(6, 6), new Point(-1, -1));

        // Token: 0x04000302 RID: 770
        private static Mat element5x5cross = CvInvoke.GetStructuringElement(ElementShape.Cross, new Size(5, 5), new Point(-1, -1));

        // Token: 0x04000303 RID: 771
        private static Mat element5x5rect = CvInvoke.GetStructuringElement(ElementShape.Rectangle, new Size(5, 5), new Point(-1, -1));

        // Token: 0x04000304 RID: 772
        private static Mat element7x5 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(7, 3), new Point(-1, -1));

        // Token: 0x04000305 RID: 773
        private static Mat element7x7 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(7, 7), new Point(-1, -1));

        // Token: 0x04000306 RID: 774
        private static Mat element9x9 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(9, 9), new Point(-1, -1));

        // Token: 0x04000307 RID: 775
        private static Mat element11x11 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(11, 11), new Point(-1, -1));

        // Token: 0x04000308 RID: 776
        private static Mat element11x11cross = CvInvoke.GetStructuringElement(ElementShape.Cross, new Size(11, 11), new Point(-1, -1));

        // Token: 0x04000309 RID: 777
        private static Mat element13x13 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(13, 13), new Point(-1, -1));

        // Token: 0x0400030A RID: 778
        private static Mat element17x17 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(17, 17), new Point(-1, -1));

        // Token: 0x0400030B RID: 779
        private static Mat element21x21 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(21, 21), new Point(-1, -1));

        // Token: 0x0400030C RID: 780
        private static Mat element21x21rect = CvInvoke.GetStructuringElement(ElementShape.Rectangle, new Size(21, 21), new Point(-1, -1));

        // Token: 0x0400030D RID: 781
        private static Mat element25x25 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(25, 25), new Point(-1, -1));

        // Token: 0x0400030E RID: 782
        private static Mat element31x31 = CvInvoke.GetStructuringElement(ElementShape.Ellipse, new Size(31, 31), new Point(-1, -1));
    }
}
