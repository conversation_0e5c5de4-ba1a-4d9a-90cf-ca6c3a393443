﻿using MySql.Data.MySqlClient;
using SpermFormAnalysis.Model;
using SpermFormAnalysis.utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Data.SqlClient;
using System.Data.SQLite;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.DataHelper
{
    public static class SpermServices
    {
        private static string connectionString = @"Data Source=" + Application.StartupPath + @"\database\morsperm.db;Initial Catalog=sqlite;Integrated Security=True; =10";
        public static Sperm GetObject(int id)
        {
            Sperm obj = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from Sperm where id=@id", conn);
                cmd.Parameters.Add(new SQLiteParameter("@id", id));
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        obj = new Sperm();
                        obj.id = Convert.ToInt32(dr["id"]);
                        obj.spermindex = Convert.ToInt32(dr["spermindex"]);
                        obj.sampleId = Convert.ToString(dr["sampleId"]);
                        obj.photoId = Convert.ToInt32(dr["photoId"]);
                        obj.xmin = Convert.ToSingle(dr["xmin"]);
                        obj.xmax = Convert.ToSingle(dr["xmax"]);
                        obj.ymin = Convert.ToSingle(dr["ymin"]);
                        obj.ymax = Convert.ToSingle(dr["ymax"]);
                        obj.classid = Convert.ToInt32(dr["classid"]);
                        obj.clsname = Convert.ToString(dr["clsname"]);
                        obj.score = Convert.ToSingle(dr["score"]);
                        obj.imageSource = Convert.ToString(dr["imageSource"]);

                        bool flag2 = !Convert.IsDBNull(dr["vop_acrosome"]);
                        if (flag2)
                        {
                            obj.vop_acrosome = stringToPointArray(Convert.ToString(dr["vop_acrosome"])).ToArray();
                        }
                        bool flag3 = !Convert.IsDBNull(dr["vop_kernel"]);
                        if (flag3)
                        {
                            obj.vop_kernel = stringToPointArray(Convert.ToString(dr["vop_kernel"])).ToArray();
                        }
                        bool flag4 = !Convert.IsDBNull(dr["vop_sperm"]);
                        if (flag4)
                        {
                            obj.vop_sperm = stringToPointArray(Convert.ToString(dr["vop_sperm"])).ToArray();
                        }
                        bool flag5 = !Convert.IsDBNull(dr["vop_middlepiece"]);
                        if (flag5)
                        {
                            obj.vop_middlepiece = stringToPointArray(Convert.ToString(dr["vop_middlepiece"])).ToArray();
                        }
                        bool flag6 = !Convert.IsDBNull(dr["vvop_vacuoles"]);
                        if (flag6)
                        {
                            obj.vvop_vacuoles = stringToListPointArray(Convert.ToString(dr["vvop_vacuoles"]));
                        }
                        bool flag7 = !Convert.IsDBNull(dr["acrosomeArea"]);
                        if (flag7)
                        {
                            obj.acrosomeArea = Convert.ToDouble(dr["acrosomeArea"]);
                        }
                        bool flag8 = !Convert.IsDBNull(dr["kernelArea"]);
                        if (flag8)
                        {
                            obj.kernelArea = Convert.ToDouble(dr["kernelArea"]);
                        }
                        bool flag9 = !Convert.IsDBNull(dr["spermArea"]);
                        if (flag9)
                        {
                            obj.spermArea = Convert.ToDouble(dr["spermArea"]);
                        }
                        bool flag10 = !Convert.IsDBNull(dr["spermGirth"]);
                        if (flag10)
                        {
                            obj.spermGirth = Convert.ToDouble(dr["spermGirth"]);
                        }
                        bool flag11 = !Convert.IsDBNull(dr["longAxis"]);
                        if (flag11)
                        {
                            obj.longAxis = Convert.ToDouble(dr["longAxis"]);
                        }
                        bool flag12 = !Convert.IsDBNull(dr["shortAxis"]);
                        if (flag12)
                        {
                            obj.shortAxis = Convert.ToDouble(dr["shortAxis"]);
                        }
                        bool flag13 = !Convert.IsDBNull(dr["center_x"]);
                        if (flag13)
                        {
                            obj.center_x = Convert.ToDouble(dr["center_x"]);
                        }
                        bool flag14 = !Convert.IsDBNull(dr["center_y"]);
                        if (flag14)
                        {
                            obj.center_y = Convert.ToDouble(dr["center_y"]);
                        }
                        bool flag15 = !Convert.IsDBNull(dr["angle"]);
                        if (flag15)
                        {
                            obj.angle = Convert.ToDouble(dr["angle"]);
                        }
                        bool flag16 = !Convert.IsDBNull(dr["center_x"]);
                        if (flag16)
                        {
                            obj.center = new PointF((float)obj.center_x, (float)obj.center_y);
                        }
                        bool flag17 = !Convert.IsDBNull(dr["ellipsRatio"]);
                        if (flag17)
                        {
                            obj.ellipsRatio = Convert.ToDouble(dr["ellipsRatio"]);
                        }
                        bool flag18 = !Convert.IsDBNull(dr["ellipseCV"]);
                        if (flag18)
                        {
                            obj.ellipseCV = Convert.ToDouble(dr["ellipseCV"]);
                        }
                        bool flag19 = !Convert.IsDBNull(dr["ellipseCV1"]);
                        if (flag19)
                        {
                            obj.ellipseCV1 = Convert.ToDouble(dr["ellipseCV1"]);
                        }
                        bool flag20 = !Convert.IsDBNull(dr["ellipseCV1Range"]);
                        if (flag20)
                        {
                            obj.ellipseCV1Range = Convert.ToDouble(dr["ellipseCV1Range"]);
                        }
                        bool flag21 = !Convert.IsDBNull(dr["acrosomeRatio"]);
                        if (flag21)
                        {
                            obj.acrosomeRatio = Convert.ToDouble(dr["acrosomeRatio"]);
                        }
                        bool flag22 = !Convert.IsDBNull(dr["areaCV"]);
                        if (flag22)
                        {
                            obj.areaCV = Convert.ToDouble(dr["areaCV"]);
                        }
                        bool flag23 = !Convert.IsDBNull(dr["girthCV"]);
                        if (flag23)
                        {
                            obj.girthCV = Convert.ToDouble(dr["girthCV"]);
                        }
                        bool flag24 = !Convert.IsDBNull(dr["SymmetryRatio"]);
                        if (flag24)
                        {
                            obj.SymmetryRatio = Convert.ToDouble(dr["SymmetryRatio"]);
                        }
                        bool flag25 = !Convert.IsDBNull(dr["SymmetryRatioLongAxis"]);
                        if (flag25)
                        {
                            obj.SymmetryRatioLongAxis = Convert.ToDouble(dr["SymmetryRatioLongAxis"]);
                        }
                        bool flag26 = !Convert.IsDBNull(dr["tailLength"]);
                        if (flag26)
                        {
                            obj.tailLength = Convert.ToDouble(dr["tailLength"]);
                        }
                        bool flag27 = !Convert.IsDBNull(dr["tailAngle"]);
                        if (flag27)
                        {
                            obj.tailAngle = Convert.ToDouble(dr["tailAngle"]);
                        }
                        bool flag28 = !Convert.IsDBNull(dr["ERCArea"]);
                        if (flag28)
                        {
                            obj.ERCArea = Convert.ToDouble(dr["ERCArea"]);
                        }
                        bool flag29 = !Convert.IsDBNull(dr["ERCWidth"]);
                        if (flag29)
                        {
                            obj.ERCWidth = Convert.ToDouble(dr["ERCWidth"]);
                        }
                        bool flag30 = !Convert.IsDBNull(dr["ERCHeight"]);
                        if (flag30)
                        {
                            obj.ERCHeight = Convert.ToDouble(dr["ERCHeight"]);
                        }
                        bool flag31 = !Convert.IsDBNull(dr["R"]);
                        if (flag31)
                        {
                            obj.R = Convert.ToDouble(dr["R"]);
                        }
                        bool flag32 = !Convert.IsDBNull(dr["G"]);
                        if (flag32)
                        {
                            obj.G = Convert.ToDouble(dr["G"]);
                        }
                        bool flag33 = !Convert.IsDBNull(dr["B"]);
                        if (flag33)
                        {
                            obj.B = Convert.ToDouble(dr["B"]);
                        }
                        bool flag34 = !Convert.IsDBNull(dr["RBRatio"]);
                        if (flag34)
                        {
                            obj.RBRatio = Convert.ToDouble(dr["RBRatio"]);
                        }
                        bool flag35 = !Convert.IsDBNull(dr["valid"]);
                        if (flag35)
                        {
                            obj.valid = Convert.ToBoolean(dr["valid"]);
                        }
                        bool flag36 = !Convert.IsDBNull(dr["ellipseRatioValid"]);
                        if (flag36)
                        {
                            obj.ellipseRatioValid = Convert.ToInt32(dr["ellipseRatioValid"]);
                        }
                        bool flag37 = !Convert.IsDBNull(dr["spermAreaValid"]);
                        if (flag37)
                        {
                            obj.spermAreaValid = Convert.ToInt32(dr["spermAreaValid"]);
                        }
                        bool flag38 = !Convert.IsDBNull(dr["ellipseCVValid"]);
                        if (flag38)
                        {
                            obj.ellipseCVValid = Convert.ToInt32(dr["ellipseCVValid"]);
                        }
                        bool flag39 = !Convert.IsDBNull(dr["SymmetryRatioValid"]);
                        if (flag39)
                        {
                            obj.SymmetryRatioValid = Convert.ToInt32(dr["SymmetryRatioValid"]);
                        }
                        bool flag40 = !Convert.IsDBNull(dr["SymmetryRatioLongAxisValid"]);
                        if (flag40)
                        {
                            obj.SymmetryRatioLongAxisValid = Convert.ToInt32(dr["SymmetryRatioLongAxisValid"]);
                        }
                        bool flag41 = !Convert.IsDBNull(dr["girthCVValid"]);
                        if (flag41)
                        {
                            obj.girthCVValid = Convert.ToInt32(dr["girthCVValid"]);
                        }
                        bool flag42 = !Convert.IsDBNull(dr["areaCVValid"]);
                        if (flag42)
                        {
                            obj.areaCVValid = Convert.ToInt32(dr["areaCVValid"]);
                        }
                        bool flag43 = !Convert.IsDBNull(dr["acrosomeRatioValid"]);
                        if (flag43)
                        {
                            obj.acrosomeRatioValid = Convert.ToInt32(dr["acrosomeRatioValid"]);
                        }
                        bool flag44 = !Convert.IsDBNull(dr["longAxisValid"]);
                        if (flag44)
                        {
                            obj.longAxisValid = Convert.ToInt32(dr["longAxisValid"]);
                        }
                        bool flag45 = !Convert.IsDBNull(dr["shortAxisValid"]);
                        if (flag45)
                        {
                            obj.shortAxisValid = Convert.ToInt32(dr["shortAxisValid"]);
                        }
                        bool flag46 = !Convert.IsDBNull(dr["spermGirthValid"]);
                        if (flag46)
                        {
                            obj.spermGirthValid = Convert.ToInt32(dr["spermGirthValid"]);
                        }
                        bool flag47 = !Convert.IsDBNull(dr["kernelAreaValid"]);
                        if (flag47)
                        {
                            obj.kernelAreaValid = Convert.ToInt32(dr["kernelAreaValid"]);
                        }
                        bool flag48 = !Convert.IsDBNull(dr["acrosomeAreaValid"]);
                        if (flag48)
                        {
                            obj.acrosomeAreaValid = Convert.ToInt32(dr["acrosomeAreaValid"]);
                        }
                        bool flag49 = !Convert.IsDBNull(dr["isNormal"]);
                        if (flag49)
                        {
                            obj.isNormal = Convert.ToInt32(dr["isNormal"]);
                        }
                        bool flag50 = !Convert.IsDBNull(dr["tailValid"]);
                        if (flag50)
                        {
                            obj.tailValid = Convert.ToBoolean(dr["tailValid"]);
                        }
                        bool flag51 = !Convert.IsDBNull(dr["vacuolesValid"]);
                        if (flag51)
                        {
                            obj.vacuolesValid = Convert.ToBoolean(dr["vacuolesValid"]);
                        }
                        bool flag52 = !Convert.IsDBNull(dr["vacuolesNumValid"]);
                        if (flag52)
                        {
                            obj.vacuolesNumValid = Convert.ToBoolean(dr["vacuolesNumValid"]);
                        }
                        bool flag53 = !Convert.IsDBNull(dr["bigVacuolesNumValid"]);
                        if (flag53)
                        {
                            obj.bigVacuolesNumValid = Convert.ToBoolean(dr["bigVacuolesNumValid"]);
                        }
                        bool flag54 = !Convert.IsDBNull(dr["kernelVacuolesNumValid"]);
                        if (flag54)
                        {
                            obj.kernelVacuolesNumValid = Convert.ToBoolean(dr["kernelVacuolesNumValid"]);
                        }
                        bool flag55 = !Convert.IsDBNull(dr["vacuolesNum"]);
                        if (flag55)
                        {
                            obj.vacuolesNum = Convert.ToInt32(dr["vacuolesNum"]);
                        }
                        bool flag56 = !Convert.IsDBNull(dr["bigVaucolesNum"]);
                        if (flag56)
                        {
                            obj.bigVaucolesNum = Convert.ToInt32(dr["bigVaucolesNum"]);
                        }
                        bool flag57 = !Convert.IsDBNull(dr["acrosomeVacuolesNum"]);
                        if (flag57)
                        {
                            obj.acrosomeVacuolesNum = Convert.ToInt32(dr["acrosomeVacuolesNum"]);
                        }
                        bool flag58 = !Convert.IsDBNull(dr["kernelVacuolesNum"]);
                        if (flag58)
                        {
                            obj.kernelVacuolesNum = Convert.ToInt32(dr["kernelVacuolesNum"]);
                        }
                        bool flag59 = !Convert.IsDBNull(dr["acrosomeVacuolesArea"]);
                        if (flag59)
                        {
                            obj.acrosomeVacuolesArea = Convert.ToDouble(dr["acrosomeVacuolesArea"]);
                        }
                        bool flag60 = !Convert.IsDBNull(dr["middlePieceValid"]);
                        if (flag60)
                        {
                            obj.middlePieceValid = Convert.ToBoolean(dr["middlePieceValid"]);
                        }
                        bool flag61 = !Convert.IsDBNull(dr["middlepieceAngleValid"]);
                        if (flag61)
                        {
                            obj.middlepieceAngleValid = Convert.ToBoolean(dr["middlepieceAngleValid"]);
                        }
                        bool flag62 = !Convert.IsDBNull(dr["middlepieceWidthValid"]);
                        if (flag62)
                        {
                            obj.middlepieceWidthValid = Convert.ToBoolean(dr["middlepieceWidthValid"]);
                        }
                        bool flag63 = !Convert.IsDBNull(dr["middlepieceAngle"]);
                        if (flag63)
                        {
                            obj.middlepieceAngle = Convert.ToDouble(dr["middlepieceAngle"]);
                        }
                        bool flag64 = !Convert.IsDBNull(dr["middlepieceWidth"]);
                        if (flag64)
                        {
                            obj.middlepieceWidth = Convert.ToDouble(dr["middlepieceWidth"]);
                        }
                        bool flag65 = !Convert.IsDBNull(dr["MDPnum"]);
                        if (flag65)
                        {
                            obj.MDPnum = Convert.ToInt32(dr["MDPnum"]);
                        }
                        bool flag66 = !Convert.IsDBNull(dr["middlePieceType"]);
                        if (flag66)
                        {
                            obj.middlePieceType = Convert.ToInt32(dr["middlePieceType"]);
                        }
                        bool flag67 = !Convert.IsDBNull(dr["shapeType"]);
                        if (flag67)
                        {
                            obj.shapeType = Convert.ToInt32(dr["shapeType"]);
                        }
                        bool flag68 = !Convert.IsDBNull(dr["acrosomeType"]);
                        if (flag68)
                        {
                            obj.acrosomeType = Convert.ToInt32(dr["acrosomeType"]);
                        }
                        bool flag69 = !Convert.IsDBNull(dr["kernelType"]);
                        if (flag69)
                        {
                            obj.kernelType = Convert.ToInt32(dr["kernelType"]);
                        }
                        bool flag70 = !Convert.IsDBNull(dr["acrosome_uniformity"]);
                        if (flag70)
                        {
                            obj.acrosome_uniformity = Convert.ToDouble(dr["acrosome_uniformity"]);
                        }
                        bool flag71 = !Convert.IsDBNull(dr["kernel_uniformity"]);
                        if (flag71)
                        {
                            obj.kernel_uniformity = Convert.ToDouble(dr["kernel_uniformity"]);
                        }
                        bool flag72 = !Convert.IsDBNull(dr["fieldId"]);
                        if (flag72)
                        {
                            obj.fieldId = Convert.ToInt32(dr["fieldId"]);
                        }
                        bool flag73 = !Convert.IsDBNull(dr["DFI"]);
                        if (flag73)
                        {
                            obj.DFI = Convert.ToDouble(dr["DFI"]);
                        }
                        bool flag74 = !Convert.IsDBNull(dr["outer_x"]);
                        if (flag74)
                        {
                            obj.outer_x = Convert.ToDouble(dr["outer_x"]);
                        }
                        bool flag75 = !Convert.IsDBNull(dr["outer_y"]);
                        if (flag75)
                        {
                            obj.outer_y = Convert.ToDouble(dr["outer_y"]);
                        }
                        bool flag76 = !Convert.IsDBNull(dr["outer_width"]);
                        if (flag76)
                        {
                            obj.outer_width = Convert.ToDouble(dr["outer_width"]);
                        }
                        bool flag77 = !Convert.IsDBNull(dr["outer_height"]);
                        if (flag77)
                        {
                            obj.outer_height = Convert.ToDouble(dr["outer_height"]);
                        }
                        bool flag78 = !Convert.IsDBNull(dr["outer_angle"]);
                        if (flag78)
                        {
                            obj.outer_angle = Convert.ToDouble(dr["outer_angle"]);
                        }
                        bool flag79 = !Convert.IsDBNull(dr["inner_x"]);
                        if (flag79)
                        {
                            obj.inner_x = Convert.ToDouble(dr["inner_x"]);
                        }
                        bool flag80 = !Convert.IsDBNull(dr["inner_y"]);
                        if (flag80)
                        {
                            obj.inner_y = Convert.ToDouble(dr["inner_y"]);
                        }
                        bool flag81 = !Convert.IsDBNull(dr["inner_width"]);
                        if (flag81)
                        {
                            obj.inner_width = Convert.ToDouble(dr["inner_width"]);
                        }
                        bool flag82 = !Convert.IsDBNull(dr["inner_height"]);
                        if (flag82)
                        {
                            obj.inner_height = Convert.ToDouble(dr["inner_height"]);
                        }
                        bool flag83 = !Convert.IsDBNull(dr["inner_angle"]);
                        if (flag83)
                        {
                            obj.inner_angle = Convert.ToDouble(dr["inner_angle"]);
                        }
                        obj.spermId = string.Concat(new object[]
                        {
                            obj.sampleId,
                            ".",
                            obj.fieldId.ToString("00"),
                            ".",
                            obj.spermindex
                        });
                    }
                }
                conn.Close();
            }
            return obj;
        }

        public static ObservableCollection<Sperm> GetObjects(int photoId)
        {
            string cmdText = "select * from Sperm where photoId = "+ photoId;
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sperm> GetObjects(string sampleId)
        {
            string cmdText = "select * from Sperm where sampleId='"+ sampleId + "'";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sperm> GetNewsObjects(string sampleId, string fieldid)
        {
            string cmdText = "select * from sperm where valid=1 and sampleId='" + sampleId + "' and fieldid=" + fieldid;
            return GetModelObjects(cmdText);
        }


        public static ObservableCollection<Sperm> AddNewSperms(ObservableCollection<Sperm> allsperms, ObservableCollection<Sperm> newsperms)
        {
            foreach (Sperm item in newsperms)
            {
                allsperms.Add(item);
            }
            return allsperms;
        }

        public static ObservableCollection<Sperm> GetnormalSperms(string sampleId)
        {
            string cmdText = "select * from sperm where valid=1 and isNormal=1 and sampleId='" + sampleId + "'  order by photoid,spermindex asc";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sperm> GetabnormalSperms(string sampleId)
        {
            string cmdText = "select * from sperm where valid=1 and isNormal=0 and sampleId='" + sampleId + "' order by fieldid,spermindex asc";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sperm> GetsubclinicalSperms(string sampleId)
        {
            string cmdText = "select * from sperm where valid=1 and isNormal=2 and sampleId='" + sampleId + "' order by fieldid,spermindex asc";
            return GetModelObjects(cmdText);
        }


        /// <summary>
        /// 人工干预修改精子
        /// </summary>
        /// <param name="sprm"></param>
        /// <returns></returns>
        public static bool UpdateHandlerSperm(Sperm sprm)
        {
            int num = 0;
            string cmdText = "update sperm set isNormal=@isNormal,shapeType=@shapeType,acrosomeType=@acrosomeType,kernelType=@kernelType,middlePieceType=@middlePieceType where id=@id";
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                cmd.Parameters.AddWithValue("@id", sprm.id);
                cmd.Parameters.AddWithValue("@isNormal", sprm.isNormal);
                cmd.Parameters.AddWithValue("@shapeType", sprm.shapeType);
                cmd.Parameters.AddWithValue("@acrosomeType", sprm.acrosomeType);
                cmd.Parameters.AddWithValue("@kernelType", sprm.kernelType);
                cmd.Parameters.AddWithValue("@middlePieceType", sprm.middlePieceType);
                conn.Open();
                try
                {
                    num += cmd.ExecuteNonQuery();
                }
                catch
                {
                }
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }

        public static ObservableCollection<Sperm> GetSpermsByType(DateTime dateFrom, DateTime dateTo, int headShape, int acrosomeType, int kernelType, string inspectItem)
        {
            ObservableCollection<Sperm> observableCollection = new ObservableCollection<Sperm>();
            string text = dateFrom.ToString("yyyy-MM-dd HH:mm:ss");
            string text2 = dateTo.AddDays(1.0).ToString("yyyy-MM-dd HH:mm:ss");
            string text3 = (headShape > 0) ? (" and sperm.shapeType&" + headShape + ">0") : ((headShape == 0) ? " and sperm.shapeType=0" : "");
            string text4 = (acrosomeType == 32) ? " and sperm.acrosomeType=32" : ((acrosomeType < 0) ? "" : (" and sperm.acrosomeType&" + acrosomeType + "> 0"));
            string text5 = (kernelType == 32) ? " and sperm.kernelType=32" : ((kernelType < 0) ? "" : (" and sperm.kernelType&" + kernelType + "> 0"));
            string cmdText = string.Concat(new string[]
            {
                "select sperm.* FROM sperm,sampleinfo where sampleinfo.inspectItem='",
                inspectItem,
                "' and sperm.sampleId=sampleinfo.sampleId and sampleinfo.sendtime>='",
                text,
                "' and sampleinfo.sendtime<'",
                text2,
                "'",
                text3,
                text4,
                text5
            });
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sperm> GetAbnormalSpermsByType(int headShape, int acrosomeType, int kernelType, string inspectItem, string sampleId)
        {
            ObservableCollection<Sperm> observableCollection = new ObservableCollection<Sperm>();
            string text = (headShape > 0) ? (" and sperm.shapeType&" + headShape + ">0") : ((headShape == 0) ? " and sperm.shapeType=0" : "");
            string text2 = (acrosomeType == 32) ? " and sperm.acrosomeType=32" : ((acrosomeType < 0) ? "" : (" and sperm.acrosomeType&" + acrosomeType + "> 0"));
            string text3 = (kernelType == 32) ? " and (sperm.kernelType=32 || sperm.kernelType=1 || sperm.kernelType=2 || sperm.kernelType=4)" : ((kernelType < 0) ? "" : (" and sperm.kernelType&" + kernelType + "> 0"));
            string cmdText = string.Concat(new string[]
            {
                "select sperm.* FROM sperm,sampleinfo where valid=1 and isNormal=0 and sampleinfo.sampleId='",
                sampleId,
                "' and sampleinfo.inspectItem='",
                inspectItem,
                "' and sperm.sampleId=sampleinfo.sampleId ",
                text,
                text2,
                text3,
                " order by sperm.fieldid,sperm.spermindex asc"
            });
            return GetModelObjects(cmdText);
        }

        public static long CreateObject(Sperm obj)
        {
            try
            {
                int num = 0;
                long num2 = -1L;

                string cmdText = "INSERT INTO sperm (photoId, sampleId, xmin, xmax, ymin, ymax, classid, clsname, score, imageSource, vop_acrosome, vop_kernel, vop_sperm, acrosomeArea, kernelArea, spermArea, spermGirth, longAxis, shortAxis, center_x, center_y, angle, ellipsRatio, acrosomeRatio, areaCV, girthCV, ellipseCV, SymmetryRatio, valid, ellipseRatioValid, spermAreaValid, ellipseCVValid, SymmetryRatioValid, girthCVValid, areaCVValid, acrosomeRatioValid, longAxisValid, shortAxisValid, spermGirthValid, kernelAreaValid, acrosomeAreaValid, isNormal, SymmetryRatioLongAxis, SymmetryRatioLongAxisValid, vacuolesNum, bigVaucolesNum, acrosomeVacuolesNum, kernelVacuolesNum, acrosomeVacuolesArea, vvop_vacuoles, vacuolesValid, middlepieceWidth, middlepieceAngle, middlepieceValid, vop_middlepiece, vacuolesNumValid, bigVacuolesNumValid, kernelVacuolesNumValid, middlepieceAngleValid, middlepieceWidthValid, fieldId, spermindex, shapeType, acrosomeType, kernelType, acrosome_uniformity, kernel_uniformity, middlePieceType, outer_x, outer_y, outer_width, outer_height, outer_angle, inner_x, inner_y, inner_width, inner_height, inner_angle, DFI, ellipseCV1, ellipseCV1Range, tailLength, tailAngle, ERCArea, ERCWidth, ERCHeight, R, G, B, RBRatio, MDPnum,isWrite) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?);";
                using (SQLiteConnection conn = new SQLiteConnection(connectionString))
                {
                    SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                    cmd.Parameters.AddWithValue("@photoId", obj.photoId);
                    cmd.Parameters.AddWithValue("@sampleId", obj.sampleId);
                    cmd.Parameters.AddWithValue("@xmin", obj.xmin);
                    cmd.Parameters.AddWithValue("@xmax", obj.xmax);
                    cmd.Parameters.AddWithValue("@ymin", obj.ymin);
                    cmd.Parameters.AddWithValue("@ymax", obj.ymax);
                    cmd.Parameters.AddWithValue("@classid", obj.classid);
                    cmd.Parameters.AddWithValue("@clsname", obj.clsname);
                    cmd.Parameters.AddWithValue("@score", obj.score);
                    cmd.Parameters.AddWithValue("@imageSource", obj.imageSource);
                    cmd.Parameters.AddWithValue("@vop_acrosome", pointArrayToString(obj.vop_acrosome));
                    cmd.Parameters.AddWithValue("@vop_kernel", pointArrayToString(obj.vop_kernel));
                    cmd.Parameters.AddWithValue("@vop_sperm", pointArrayToString(obj.vop_sperm));
                    cmd.Parameters.AddWithValue("@acrosomeArea", obj.acrosomeArea);
                    cmd.Parameters.AddWithValue("@kernelArea", obj.kernelArea);
                    cmd.Parameters.AddWithValue("@spermArea", obj.spermArea);
                    cmd.Parameters.AddWithValue("@spermGirth", obj.spermGirth);
                    cmd.Parameters.AddWithValue("@longAxis", obj.longAxis);
                    cmd.Parameters.AddWithValue("@shortAxis", obj.shortAxis);
                    cmd.Parameters.AddWithValue("@center_x", obj.center_x);
                    cmd.Parameters.AddWithValue("@center_y", obj.center_y);
                    cmd.Parameters.AddWithValue("@angle", obj.angle);
                    cmd.Parameters.AddWithValue("@ellipsRatio", obj.ellipsRatio);
                    cmd.Parameters.AddWithValue("@acrosomeRatio", obj.acrosomeRatio);
                    cmd.Parameters.AddWithValue("@areaCV", obj.areaCV);
                    cmd.Parameters.AddWithValue("@girthCV", obj.girthCV);
                    cmd.Parameters.AddWithValue("@ellipseCV", obj.ellipseCV);
                    cmd.Parameters.AddWithValue("@SymmetryRatio", obj.SymmetryRatio);
                    cmd.Parameters.AddWithValue("@valid", obj.valid);
                    cmd.Parameters.AddWithValue("@ellipseRatioValid", obj.ellipseRatioValid);
                    cmd.Parameters.AddWithValue("@spermAreaValid", obj.spermAreaValid);
                    cmd.Parameters.AddWithValue("@ellipseCVValid", obj.ellipseCVValid);
                    cmd.Parameters.AddWithValue("@SymmetryRatioValid", obj.SymmetryRatioValid);
                    cmd.Parameters.AddWithValue("@girthCVValid", obj.girthCVValid);
                    cmd.Parameters.AddWithValue("@areaCVValid", obj.areaCVValid);
                    cmd.Parameters.AddWithValue("@acrosomeRatioValid", obj.acrosomeRatioValid);
                    cmd.Parameters.AddWithValue("@longAxisValid", obj.longAxisValid);
                    cmd.Parameters.AddWithValue("@shortAxisValid", obj.shortAxisValid);
                    cmd.Parameters.AddWithValue("@spermGirthValid", obj.spermGirthValid);
                    cmd.Parameters.AddWithValue("@kernelAreaValid", obj.kernelAreaValid);
                    cmd.Parameters.AddWithValue("@acrosomeAreaValid", obj.acrosomeAreaValid);
                    cmd.Parameters.AddWithValue("@isNormal", obj.isNormal);
                    cmd.Parameters.AddWithValue("@SymmetryRatioLongAxis", obj.SymmetryRatioLongAxis);
                    cmd.Parameters.AddWithValue("@SymmetryRatioLongAxisValid", obj.SymmetryRatioLongAxisValid);
                    cmd.Parameters.AddWithValue("@vacuolesNum", obj.vacuolesNum);
                    cmd.Parameters.AddWithValue("@bigVaucolesNum", obj.bigVaucolesNum);
                    cmd.Parameters.AddWithValue("@acrosomeVacuolesNum", obj.acrosomeVacuolesNum);
                    cmd.Parameters.AddWithValue("@kernelVacuolesNum", obj.kernelVacuolesNum);
                    cmd.Parameters.AddWithValue("@acrosomeVacuolesArea", obj.acrosomeVacuolesArea);
                    cmd.Parameters.AddWithValue("@vvop_vacuoles", listPointArrayToString(obj.vvop_vacuoles));
                    cmd.Parameters.AddWithValue("@vacuolesValid", obj.vacuolesValid);
                    cmd.Parameters.AddWithValue("@middlepieceWidth", obj.middlepieceWidth);
                    cmd.Parameters.AddWithValue("@middlepieceAngle", obj.middlepieceAngle);
                    cmd.Parameters.AddWithValue("@middlePieceValid", obj.middlePieceValid);
                    cmd.Parameters.AddWithValue("@vop_middlepiece", pointArrayToString(obj.vop_middlepiece));
                    cmd.Parameters.AddWithValue("@vacuolesNumValid", obj.vacuolesNumValid);
                    cmd.Parameters.AddWithValue("@bigVacuolesNumValid", obj.bigVacuolesNumValid);
                    cmd.Parameters.AddWithValue("@kernelVacuolesNumValid", obj.kernelVacuolesNumValid);
                    cmd.Parameters.AddWithValue("@middlepieceAngleValid", obj.middlepieceAngleValid);
                    cmd.Parameters.AddWithValue("@middlepieceWidthValid", obj.middlepieceWidthValid);
                    cmd.Parameters.AddWithValue("@fieldId", obj.fieldId);
                    cmd.Parameters.AddWithValue("@spermindex", obj.spermindex);
                    cmd.Parameters.AddWithValue("@shapeType", obj.shapeType);
                    cmd.Parameters.AddWithValue("@acrosomeType", obj.acrosomeType);
                    cmd.Parameters.AddWithValue("@kernelType", obj.kernelType);
                    cmd.Parameters.AddWithValue("@acrosome_uniformity", obj.acrosome_uniformity);
                    cmd.Parameters.AddWithValue("@kernel_uniformity", obj.kernel_uniformity);
                    cmd.Parameters.AddWithValue("@middlePieceType", obj.middlePieceType);   
                    cmd.Parameters.AddWithValue("@outer_x", obj.outer_x);
                    cmd.Parameters.AddWithValue("@outer_y", obj.outer_y);
                    cmd.Parameters.AddWithValue("@outer_width", obj.outer_width);
                    cmd.Parameters.AddWithValue("@outer_height", obj.outer_height);
                    cmd.Parameters.AddWithValue("@outer_angle", obj.outer_angle);
                    cmd.Parameters.AddWithValue("@inner_x", obj.inner_x);
                    cmd.Parameters.AddWithValue("@inner_y", obj.inner_y);
                    cmd.Parameters.AddWithValue("@inner_width", obj.inner_width);
                    cmd.Parameters.AddWithValue("@inner_height", obj.inner_height);
                    cmd.Parameters.AddWithValue("@inner_angle", obj.inner_angle);
                    cmd.Parameters.AddWithValue("@DFI", obj.DFI);
                    cmd.Parameters.AddWithValue("@ellipseCV1", obj.ellipseCV1);
                    cmd.Parameters.AddWithValue("@ellipseCV1Range", obj.ellipseCV1Range);
                    cmd.Parameters.AddWithValue("@tailLength", obj.tailLength);
                    cmd.Parameters.AddWithValue("@tailAngle", obj.tailAngle);
                    cmd.Parameters.AddWithValue("@ERCArea", obj.ERCArea);
                    cmd.Parameters.AddWithValue("@ERCWidth", obj.ERCWidth);
                    cmd.Parameters.AddWithValue("@ERCHeight", obj.ERCHeight);
                    cmd.Parameters.AddWithValue("@R", obj.R);
                    cmd.Parameters.AddWithValue("@G", obj.G);
                    cmd.Parameters.AddWithValue("@B", obj.B);
                    cmd.Parameters.AddWithValue("@RBRatio", obj.RBRatio);
                    cmd.Parameters.AddWithValue("@MDPnum", obj.MDPnum);
                    cmd.Parameters.AddWithValue("@isWrite", obj.isWrite);

                    conn.Open();
                    num += cmd.ExecuteNonQuery();
                    num2 = conn.LastInsertRowId;
                    //try
                    //{

                    //}
                    //catch
                    //{
                    //}
                    conn.Close();
                }
                bool flag = num == 0;
                long result;
                if (flag)
                {
                    result = -1L;
                }
                else
                {
                    result = num2;
                }
                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"精子分析异常:{ex}");
                throw;
            }


        }

        private static ObservableCollection<Sperm> GetModelObjects(string cmdText)
        {
            ObservableCollection<Sperm> lists = new ObservableCollection<Sperm>();
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                conn.BusyTimeout = 3;
                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        Sperm obj = new Sperm();
                        obj.id = Convert.ToInt32(dr["id"]);
                        obj.spermindex = Convert.ToInt32(dr["spermindex"]);
                        obj.sampleId = Convert.ToString(dr["sampleId"]);
                        obj.photoId = Convert.ToInt32(dr["photoId"]);
                        obj.xmin = Convert.ToSingle(dr["xmin"]);
                        obj.xmax = Convert.ToSingle(dr["xmax"]);
                        obj.ymin = Convert.ToSingle(dr["ymin"]);
                        obj.ymax = Convert.ToSingle(dr["ymax"]);
                        obj.classid = Convert.ToInt32(dr["classid"]);
                        obj.clsname = Convert.ToString(dr["clsname"]);
                        obj.score = Convert.ToSingle(dr["score"]);
                        obj.imageSource = Convert.ToString(dr["imageSource"]);

                        bool flag2 = !Convert.IsDBNull(dr["vop_acrosome"]);
                        if (flag2)
                        {
                            obj.vop_acrosome = stringToPointArray(Convert.ToString(dr["vop_acrosome"])).ToArray();
                        }
                        bool flag3 = !Convert.IsDBNull(dr["vop_kernel"]);
                        if (flag3)
                        {
                            obj.vop_kernel = stringToPointArray(Convert.ToString(dr["vop_kernel"])).ToArray();
                        }
                        bool flag4 = !Convert.IsDBNull(dr["vop_sperm"]);
                        if (flag4)
                        {
                            obj.vop_sperm = stringToPointArray(Convert.ToString(dr["vop_sperm"])).ToArray();
                        }
                        bool flag5 = !Convert.IsDBNull(dr["vop_middlepiece"]);
                        if (flag5)
                        {
                            obj.vop_middlepiece = stringToPointArray(Convert.ToString(dr["vop_middlepiece"])).ToArray();
                        }
                        bool flag6 = !Convert.IsDBNull(dr["vvop_vacuoles"]);
                        if (flag6)
                        {
                            obj.vvop_vacuoles = stringToListPointArray(Convert.ToString(dr["vvop_vacuoles"]));
                        }
                        bool flag7 = !Convert.IsDBNull(dr["acrosomeArea"]);
                        if (flag7)
                        {
                            obj.acrosomeArea = Convert.ToDouble(dr["acrosomeArea"]);
                        }
                        bool flag8 = !Convert.IsDBNull(dr["kernelArea"]);
                        if (flag8)
                        {
                            obj.kernelArea = Convert.ToDouble(dr["kernelArea"]);
                        }
                        bool flag9 = !Convert.IsDBNull(dr["spermArea"]);
                        if (flag9)
                        {
                            obj.spermArea = Convert.ToDouble(dr["spermArea"]);
                        }
                        bool flag10 = !Convert.IsDBNull(dr["spermGirth"]);
                        if (flag10)
                        {
                            obj.spermGirth = Convert.ToDouble(dr["spermGirth"]);
                        }
                        bool flag11 = !Convert.IsDBNull(dr["longAxis"]);
                        if (flag11)
                        {
                            obj.longAxis = Convert.ToDouble(dr["longAxis"]);
                        }
                        bool flag12 = !Convert.IsDBNull(dr["shortAxis"]);
                        if (flag12)
                        {
                            obj.shortAxis = Convert.ToDouble(dr["shortAxis"]);
                        }
                        bool flag13 = !Convert.IsDBNull(dr["center_x"]);
                        if (flag13)
                        {
                            obj.center_x = Convert.ToDouble(dr["center_x"]);
                        }
                        bool flag14 = !Convert.IsDBNull(dr["center_y"]);
                        if (flag14)
                        {
                            obj.center_y = Convert.ToDouble(dr["center_y"]);
                        }
                        bool flag15 = !Convert.IsDBNull(dr["angle"]);
                        if (flag15)
                        {
                            obj.angle = Convert.ToDouble(dr["angle"]);
                        }
                        bool flag16 = !Convert.IsDBNull(dr["center_x"]);
                        if (flag16)
                        {
                            obj.center = new PointF((float)obj.center_x, (float)obj.center_y);
                        }
                        bool flag17 = !Convert.IsDBNull(dr["ellipsRatio"]);
                        if (flag17)
                        {
                            obj.ellipsRatio = Convert.ToDouble(dr["ellipsRatio"]);
                        }
                        bool flag18 = !Convert.IsDBNull(dr["ellipseCV"]);
                        if (flag18)
                        {
                            obj.ellipseCV = Convert.ToDouble(dr["ellipseCV"]);
                        }
                        bool flag19 = !Convert.IsDBNull(dr["ellipseCV1"]);
                        if (flag19)
                        {
                            obj.ellipseCV1 = Convert.ToDouble(dr["ellipseCV1"]);
                        }
                        bool flag20 = !Convert.IsDBNull(dr["ellipseCV1Range"]);
                        if (flag20)
                        {
                            obj.ellipseCV1Range = Convert.ToDouble(dr["ellipseCV1Range"]);
                        }
                        bool flag21 = !Convert.IsDBNull(dr["acrosomeRatio"]);
                        if (flag21)
                        {
                            obj.acrosomeRatio = Convert.ToDouble(dr["acrosomeRatio"]);
                        }
                        bool flag22 = !Convert.IsDBNull(dr["areaCV"]);
                        if (flag22)
                        {
                            obj.areaCV = Convert.ToDouble(dr["areaCV"]);
                        }
                        bool flag23 = !Convert.IsDBNull(dr["girthCV"]);
                        if (flag23)
                        {
                            obj.girthCV = Convert.ToDouble(dr["girthCV"]);
                        }
                        bool flag24 = !Convert.IsDBNull(dr["SymmetryRatio"]);
                        if (flag24)
                        {
                            obj.SymmetryRatio = Convert.ToDouble(dr["SymmetryRatio"]);
                        }
                        bool flag25 = !Convert.IsDBNull(dr["SymmetryRatioLongAxis"]);
                        if (flag25)
                        {
                            obj.SymmetryRatioLongAxis = Convert.ToDouble(dr["SymmetryRatioLongAxis"]);
                        }
                        bool flag26 = !Convert.IsDBNull(dr["tailLength"]);
                        if (flag26)
                        {
                            obj.tailLength = Convert.ToDouble(dr["tailLength"]);
                        }
                        bool flag27 = !Convert.IsDBNull(dr["tailAngle"]);
                        if (flag27)
                        {
                            obj.tailAngle = Convert.ToDouble(dr["tailAngle"]);
                        }
                        bool flag28 = !Convert.IsDBNull(dr["ERCArea"]);
                        if (flag28)
                        {
                            obj.ERCArea = Convert.ToDouble(dr["ERCArea"]);
                        }
                        bool flag29 = !Convert.IsDBNull(dr["ERCWidth"]);
                        if (flag29)
                        {
                            obj.ERCWidth = Convert.ToDouble(dr["ERCWidth"]);
                        }
                        bool flag30 = !Convert.IsDBNull(dr["ERCHeight"]);
                        if (flag30)
                        {
                            obj.ERCHeight = Convert.ToDouble(dr["ERCHeight"]);
                        }
                        bool flag31 = !Convert.IsDBNull(dr["R"]);
                        if (flag31)
                        {
                            obj.R = Convert.ToDouble(dr["R"]);
                        }
                        bool flag32 = !Convert.IsDBNull(dr["G"]);
                        if (flag32)
                        {
                            obj.G = Convert.ToDouble(dr["G"]);
                        }
                        bool flag33 = !Convert.IsDBNull(dr["B"]);
                        if (flag33)
                        {
                            obj.B = Convert.ToDouble(dr["B"]);
                        }
                        bool flag34 = !Convert.IsDBNull(dr["RBRatio"]);
                        if (flag34)
                        {
                            obj.RBRatio = Convert.ToDouble(dr["RBRatio"]);
                        }
                        bool flag35 = !Convert.IsDBNull(dr["valid"]);
                        if (flag35)
                        {
                            obj.valid = Convert.ToBoolean(dr["valid"]);
                        }
                        bool flag36 = !Convert.IsDBNull(dr["ellipseRatioValid"]);
                        if (flag36)
                        {
                            obj.ellipseRatioValid = Convert.ToInt32(dr["ellipseRatioValid"]);
                        }
                        bool flag37 = !Convert.IsDBNull(dr["spermAreaValid"]);
                        if (flag37)
                        {
                            obj.spermAreaValid = Convert.ToInt32(dr["spermAreaValid"]);
                        }
                        bool flag38 = !Convert.IsDBNull(dr["ellipseCVValid"]);
                        if (flag38)
                        {
                            obj.ellipseCVValid = Convert.ToInt32(dr["ellipseCVValid"]);
                        }
                        bool flag39 = !Convert.IsDBNull(dr["SymmetryRatioValid"]);
                        if (flag39)
                        {
                            obj.SymmetryRatioValid = Convert.ToInt32(dr["SymmetryRatioValid"]);
                        }
                        bool flag40 = !Convert.IsDBNull(dr["SymmetryRatioLongAxisValid"]);
                        if (flag40)
                        {
                            obj.SymmetryRatioLongAxisValid = Convert.ToInt32(dr["SymmetryRatioLongAxisValid"]);
                        }
                        bool flag41 = !Convert.IsDBNull(dr["girthCVValid"]);
                        if (flag41)
                        {
                            obj.girthCVValid = Convert.ToInt32(dr["girthCVValid"]);
                        }
                        bool flag42 = !Convert.IsDBNull(dr["areaCVValid"]);
                        if (flag42)
                        {
                            obj.areaCVValid = Convert.ToInt32(dr["areaCVValid"]);
                        }
                        bool flag43 = !Convert.IsDBNull(dr["acrosomeRatioValid"]);
                        if (flag43)
                        {
                            obj.acrosomeRatioValid = Convert.ToInt32(dr["acrosomeRatioValid"]);
                        }
                        bool flag44 = !Convert.IsDBNull(dr["longAxisValid"]);
                        if (flag44)
                        {
                            obj.longAxisValid = Convert.ToInt32(dr["longAxisValid"]);
                        }
                        bool flag45 = !Convert.IsDBNull(dr["shortAxisValid"]);
                        if (flag45)
                        {
                            obj.shortAxisValid = Convert.ToInt32(dr["shortAxisValid"]);
                        }
                        bool flag46 = !Convert.IsDBNull(dr["spermGirthValid"]);
                        if (flag46)
                        {
                            obj.spermGirthValid = Convert.ToInt32(dr["spermGirthValid"]);
                        }
                        bool flag47 = !Convert.IsDBNull(dr["kernelAreaValid"]);
                        if (flag47)
                        {
                            obj.kernelAreaValid = Convert.ToInt32(dr["kernelAreaValid"]);
                        }
                        bool flag48 = !Convert.IsDBNull(dr["acrosomeAreaValid"]);
                        if (flag48)
                        {
                            obj.acrosomeAreaValid = Convert.ToInt32(dr["acrosomeAreaValid"]);
                        }
                        bool flag49 = !Convert.IsDBNull(dr["isNormal"]);
                        if (flag49)
                        {
                            obj.isNormal = Convert.ToInt32(dr["isNormal"]);
                        }
                        bool flag50 = !Convert.IsDBNull(dr["tailValid"]);
                        if (flag50)
                        {
                            obj.tailValid = Convert.ToBoolean(dr["tailValid"]);
                        }
                        bool flag51 = !Convert.IsDBNull(dr["vacuolesValid"]);
                        if (flag51)
                        {
                            obj.vacuolesValid = Convert.ToBoolean(dr["vacuolesValid"]);
                        }
                        bool flag52 = !Convert.IsDBNull(dr["vacuolesNumValid"]);
                        if (flag52)
                        {
                            obj.vacuolesNumValid = Convert.ToBoolean(dr["vacuolesNumValid"]);
                        }
                        bool flag53 = !Convert.IsDBNull(dr["bigVacuolesNumValid"]);
                        if (flag53)
                        {
                            obj.bigVacuolesNumValid = Convert.ToBoolean(dr["bigVacuolesNumValid"]);
                        }
                        bool flag54 = !Convert.IsDBNull(dr["kernelVacuolesNumValid"]);
                        if (flag54)
                        {
                            obj.kernelVacuolesNumValid = Convert.ToBoolean(dr["kernelVacuolesNumValid"]);
                        }
                        bool flag55 = !Convert.IsDBNull(dr["vacuolesNum"]);
                        if (flag55)
                        {
                            obj.vacuolesNum = Convert.ToInt32(dr["vacuolesNum"]);
                        }
                        bool flag56 = !Convert.IsDBNull(dr["bigVaucolesNum"]);
                        if (flag56)
                        {
                            obj.bigVaucolesNum = Convert.ToInt32(dr["bigVaucolesNum"]);
                        }
                        bool flag57 = !Convert.IsDBNull(dr["acrosomeVacuolesNum"]);
                        if (flag57)
                        {
                            obj.acrosomeVacuolesNum = Convert.ToInt32(dr["acrosomeVacuolesNum"]);
                        }
                        bool flag58 = !Convert.IsDBNull(dr["kernelVacuolesNum"]);
                        if (flag58)
                        {
                            obj.kernelVacuolesNum = Convert.ToInt32(dr["kernelVacuolesNum"]);
                        }
                        bool flag59 = !Convert.IsDBNull(dr["acrosomeVacuolesArea"]);
                        if (flag59)
                        {
                            obj.acrosomeVacuolesArea = Convert.ToDouble(dr["acrosomeVacuolesArea"]);
                        }
                        bool flag60 = !Convert.IsDBNull(dr["middlePieceValid"]);
                        if (flag60)
                        {
                            obj.middlePieceValid = Convert.ToBoolean(dr["middlePieceValid"]);
                        }
                        bool flag61 = !Convert.IsDBNull(dr["middlepieceAngleValid"]);
                        if (flag61)
                        {
                            obj.middlepieceAngleValid = Convert.ToBoolean(dr["middlepieceAngleValid"]);
                        }
                        bool flag62 = !Convert.IsDBNull(dr["middlepieceWidthValid"]);
                        if (flag62)
                        {
                            obj.middlepieceWidthValid = Convert.ToBoolean(dr["middlepieceWidthValid"]);
                        }
                        bool flag63 = !Convert.IsDBNull(dr["middlepieceAngle"]);
                        if (flag63)
                        {
                            obj.middlepieceAngle = Convert.ToDouble(dr["middlepieceAngle"]);
                        }
                        bool flag64 = !Convert.IsDBNull(dr["middlepieceWidth"]);
                        if (flag64)
                        {
                            obj.middlepieceWidth = Convert.ToDouble(dr["middlepieceWidth"]);
                        }
                        bool flag65 = !Convert.IsDBNull(dr["MDPnum"]);
                        if (flag65)
                        {
                            obj.MDPnum = Convert.ToInt32(dr["MDPnum"]);
                        }
                        bool flag66 = !Convert.IsDBNull(dr["middlePieceType"]);
                        if (flag66)
                        {
                            obj.middlePieceType = Convert.ToInt32(dr["middlePieceType"]);
                        }
                        bool flag67 = !Convert.IsDBNull(dr["shapeType"]);
                        if (flag67)
                        {
                            obj.shapeType = Convert.ToInt32(dr["shapeType"]);
                        }
                        bool flag68 = !Convert.IsDBNull(dr["acrosomeType"]);
                        if (flag68)
                        {
                            obj.acrosomeType = Convert.ToInt32(dr["acrosomeType"]);
                        }
                        bool flag69 = !Convert.IsDBNull(dr["kernelType"]);
                        if (flag69)
                        {
                            obj.kernelType = Convert.ToInt32(dr["kernelType"]);
                        }
                        bool flag70 = !Convert.IsDBNull(dr["acrosome_uniformity"]);
                        if (flag70)
                        {
                            obj.acrosome_uniformity = Convert.ToDouble(dr["acrosome_uniformity"]);
                        }
                        bool flag71 = !Convert.IsDBNull(dr["kernel_uniformity"]);
                        if (flag71)
                        {
                            obj.kernel_uniformity = Convert.ToDouble(dr["kernel_uniformity"]);
                        }
                        bool flag72 = !Convert.IsDBNull(dr["fieldId"]);
                        if (flag72)
                        {
                            obj.fieldId = Convert.ToInt32(dr["fieldId"]);
                        }
                        bool flag73 = !Convert.IsDBNull(dr["DFI"]);
                        if (flag73)
                        {
                            obj.DFI = Convert.ToDouble(dr["DFI"]);
                        }
                        bool flag74 = !Convert.IsDBNull(dr["outer_x"]);
                        if (flag74)
                        {
                            obj.outer_x = Convert.ToDouble(dr["outer_x"]);
                        }
                        bool flag75 = !Convert.IsDBNull(dr["outer_y"]);
                        if (flag75)
                        {
                            obj.outer_y = Convert.ToDouble(dr["outer_y"]);
                        }
                        bool flag76 = !Convert.IsDBNull(dr["outer_width"]);
                        if (flag76)
                        {
                            obj.outer_width = Convert.ToDouble(dr["outer_width"]);
                        }
                        bool flag77 = !Convert.IsDBNull(dr["outer_height"]);
                        if (flag77)
                        {
                            obj.outer_height = Convert.ToDouble(dr["outer_height"]);
                        }
                        bool flag78 = !Convert.IsDBNull(dr["outer_angle"]);
                        if (flag78)
                        {
                            obj.outer_angle = Convert.ToDouble(dr["outer_angle"]);
                        }
                        bool flag79 = !Convert.IsDBNull(dr["inner_x"]);
                        if (flag79)
                        {
                            obj.inner_x = Convert.ToDouble(dr["inner_x"]);
                        }
                        bool flag80 = !Convert.IsDBNull(dr["inner_y"]);
                        if (flag80)
                        {
                            obj.inner_y = Convert.ToDouble(dr["inner_y"]);
                        }
                        bool flag81 = !Convert.IsDBNull(dr["inner_width"]);
                        if (flag81)
                        {
                            obj.inner_width = Convert.ToDouble(dr["inner_width"]);
                        }
                        bool flag82 = !Convert.IsDBNull(dr["inner_height"]);
                        if (flag82)
                        {
                            obj.inner_height = Convert.ToDouble(dr["inner_height"]);
                        }
                        bool flag83 = !Convert.IsDBNull(dr["inner_angle"]);
                        if (flag83)
                        {
                            obj.inner_angle = Convert.ToDouble(dr["inner_angle"]);
                        }
                        obj.spermId = string.Concat(new object[]
                        {
                            obj.sampleId,
                            ".",
                            obj.fieldId.ToString("00"),
                            ".",
                            obj.spermindex
                        });
                        bool flag84 = !Convert.IsDBNull(dr["isWrite"]);
                        if(flag84)
                        {
                            obj.isWrite = Convert.ToInt32(dr["isWrite"]);
                        }    
                        lists.Add(obj);
                    }
                }
                conn.Close();
            }
            return lists;
        }

        public static bool DelSperm(string sampleId)
        {
            int num = 0;
            string cmdText = "delete from sperm where sampleid=@id";
            using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
            {
                using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                {
                    mySqlCommand.Parameters.AddWithValue("@id", sampleId);
                    mySqlConnection.Open();
                    try
                    {
                        num += mySqlCommand.ExecuteNonQuery();
                    }
                    catch
                    {
                    }
                }
            }
            bool flag = num == 0;
            return !flag;
        }

        public static bool DelOneSperm(int sampleId)
        {
            int num = 0;
            string cmdText = "delete from sperm where id=@id";
            using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
            {
                using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                {
                    mySqlCommand.Parameters.AddWithValue("@id", sampleId);
                    mySqlConnection.Open();
                    try
                    {
                        num += mySqlCommand.ExecuteNonQuery();
                    }
                    catch
                    {
                    }
                }
            }
            bool flag = num == 0;
            return !flag;
        }
        public static List<Point> stringToPointArray(string ptAarryString)
        {
            List<Point> list = new List<Point>();
            bool flag = ptAarryString != "";
            if (flag)
            {
                string[] array = ptAarryString.Split(new char[]
                {
                    ' '
                });
                for (int i = 0; i < array.Count<string>() - 1; i++)
                {
                    Point item = default(Point);
                    string[] array2 = array[i].Split(new char[]
                    {
                        ','
                    });
                    item.X = Convert.ToInt32(array2[0]);
                    item.Y = Convert.ToInt32(array2[1]);
                    list.Add(item);
                }
            }
            return list;
        }

        public static List<Point[]> stringToListPointArray(string ptAarryString)
        {
            List<Point[]> list = new List<Point[]>();
            bool flag = ptAarryString != "";
            if (flag)
            {
                string[] array = ptAarryString.Split(new char[]
                {
                    '|'
                });
                for (int i = 0; i < array.Count<string>() - 1; i++)
                {
                    List<Point> list2 = stringToPointArray(array[i]);
                    list.Add(list2.ToArray());
                }
            }
            return list;
        }

        public static string pointArrayToString(Point[] ptAarry)
        {
            string text = "";
            bool flag = ptAarry != null;
            if (flag)
            {
                foreach (Point point in ptAarry)
                {
                    text = string.Concat(new object[]
                    {
                        text,
                        point.X,
                        ",",
                        point.Y,
                        " "
                    });
                }
            }
            return text;
        }

        // Token: 0x06000038 RID: 56 RVA: 0x0000D124 File Offset: 0x0000B324
        public static string listPointArrayToString(List<Point[]> lstPtArray)
        {
            string text = "";
            bool flag = lstPtArray != null;
            if (flag)
            {
                foreach (Point[] ptAarry in lstPtArray)
                {
                    text = text + pointArrayToString(ptAarry) + "|";
                }
            }
            return text;
        }
    }
}
