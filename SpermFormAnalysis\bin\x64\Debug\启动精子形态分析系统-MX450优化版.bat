@echo off
title 精子形态分析系统 - MX450显卡优化启动器
color 0A

echo ========================================
echo    精子形态分析系统 - MX450显卡优化版
echo ========================================
echo.

echo [1/4] 检测GPU配置文件...
if exist "gpu_config.ini" (
    echo     ✓ 找到配置文件: gpu_config.ini
    
    REM 读取配置文件
    for /f "tokens=2 delims==" %%a in ('findstr "ForceUseCPU=" gpu_config.ini') do set ForceUseCPU=%%a
    for /f "tokens=2 delims==" %%a in ('findstr "GPUMemoryLimit=" gpu_config.ini') do set GPUMemoryLimit=%%a
    
    echo     - 强制CPU模式: %ForceUseCPU%
    echo     - GPU内存限制: %GPUMemoryLimit%MB
) else (
    echo     ! 未找到配置文件，使用默认MX450设置
    set ForceUseCPU=false
    set GPUMemoryLimit=1400
)

echo.
echo [2/4] 配置TensorFlow环境变量...

REM 基础TensorFlow设置
set TF_CPP_MIN_LOG_LEVEL=2
echo     ✓ 设置日志级别: %TF_CPP_MIN_LOG_LEVEL%

if /i "%ForceUseCPU%"=="true" (
    echo     ✓ 启用CPU模式
    set CUDA_VISIBLE_DEVICES=
) else (
    echo     ✓ 启用GPU优化模式
    set TF_FORCE_GPU_ALLOW_GROWTH=true
    set TF_GPU_MEMORY_LIMIT=%GPUMemoryLimit%
    echo     ✓ GPU内存增长: %TF_FORCE_GPU_ALLOW_GROWTH%
    echo     ✓ GPU内存限制: %TF_GPU_MEMORY_LIMIT%MB
)

echo.
echo [3/4] 检查程序文件...
if exist "SpermFormAnalysis.exe" (
    echo     ✓ 程序文件存在
) else (
    echo     ✗ 程序文件不存在！
    pause
    exit /b 1
)

echo.
echo [4/4] 启动程序...
echo     正在启动精子形态分析系统...
echo     GPU配置已优化，适配MX450显卡
echo.

REM 启动程序
start "" "SpermFormAnalysis.exe"

echo     ✓ 程序已启动
echo.
echo 提示：
echo - 如果尾巴信息仍无法显示，请修改gpu_config.ini
echo - 将ForceUseCPU设置为true可强制使用CPU模式
echo - 程序日志保存在Log文件夹中
echo.
echo 按任意键关闭此窗口...
pause >nul
