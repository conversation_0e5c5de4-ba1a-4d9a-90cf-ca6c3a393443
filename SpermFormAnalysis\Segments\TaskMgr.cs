﻿using Emgu.CV;
using Emgu.CV.Structure;
using MySqlX.XDevAPI.Common;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.Model;
using SpermFormAnalysis.utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Media;

namespace SpermFormAnalysis.Segments
{
    public static class TaskMgr
    {
        // Token: 0x14000010 RID: 16
        // (add) Token: 0x0600058F RID: 1423 RVA: 0x000228C0 File Offset: 0x00020AC0
        // (remove) Token: 0x06000590 RID: 1424 RVA: 0x000228F4 File Offset: 0x00020AF4
        //[DebuggerBrowsable(DebuggerBrowsableState.Never)]
        public static event TaskMgr.handleProcessedPhotoChanged RaiseProcessedPhotoChangedEvent;

        // Token: 0x14000011 RID: 17
        // (add) Token: 0x06000591 RID: 1425 RVA: 0x00022928 File Offset: 0x00020B28
        // (remove) Token: 0x06000592 RID: 1426 RVA: 0x0002295C File Offset: 0x00020B5C
        //[DebuggerBrowsable(DebuggerBrowsableState.Never)]
        public static event TaskMgr.handleProcesssingPhotoChanged RaiseProcessingPhotoChangedEvent;

        // Token: 0x06000593 RID: 1427 RVA: 0x00022990 File Offset: 0x00020B90
        public static void refreshTaskList(string sampleid)
        {
            ObservableCollection<Model.Samplephoto> mrftaskList = SamplephotoServices.GetMRFTaskList(sampleid);
            using (IEnumerator<Model.Samplephoto> enumerator = mrftaskList.GetEnumerator())
            {
                while (enumerator.MoveNext())
                {
                    Model.Samplephoto task = enumerator.Current;
                    bool flag = TaskMgr.taskList.FirstOrDefault((Model.Samplephoto t) => t.id == task.id) == null;
                    if (flag)
                    {
                        TaskMgr.taskList.Add(task);
                    }
                }
            }
        }

        // Token: 0x06000594 RID: 1428 RVA: 0x00022A1C File Offset: 0x00020C1C
        public static void runSegment(object sampleid)
        {
            try
            {
                object obj = TaskMgr.locker;
                lock (obj)
                {
                    TaskMgr.refreshTaskList((string)sampleid);
                    while (TaskMgr.taskList.Count > 0)
                    {
                        bool flag2 = TaskMgr.isExiting;
                        if (flag2)
                        {
                            break;
                        }
                        Task<List<Model.Sperm>> task2 = null;
                        Model.Samplephoto task = TaskMgr.taskList[0];
                        if (DisplayUI)
                        {
                            bool flag3 = TaskMgr.RaiseProcessingPhotoChangedEvent != null;
                            if (flag3)
                            {
                                TaskMgr.RaiseProcessingPhotoChangedEvent(task);
                            }
                        }

                        //clsLog.writeLog(task.imageSource + "分析中...");
                        ObservableCollection<Model.Sampleinfo> sampleInfo = SampleinfoServices.GetSampleInfo(task.sampleId);

                        Image<Bgr, byte> image = new Image<Bgr, byte>(task.imageSource);
                        bool flag4 = sampleInfo.Count > 0;
                        if (flag4)
                        {
                            string inspectItem = sampleInfo[0].inspectItem;
                            if (!(inspectItem == "DFI"))
                            {
                                if (!(inspectItem == "MAT"))
                                {
                                    if (inspectItem == "MRF")
                                    {
                                        task2 = new Task<List<Model.Sperm>>(() => MRFSegment.doSegment(task.imageSource));
                                    }
                                }
                            }
                        }
                        bool flag5 = task2 != null;
                        if (flag5)
                        {
                            try
                            {
                                task2.Start();
                                Task.WaitAll(new Task[]
                                {
                                task2
                                });
                                List<Model.Sperm> result = task2.Result;
                                result.RemoveAll((Model.Sperm p) => p == null);
                                //bool flag6 = SpermServices.DelSperm(task.sampleId);
                                bool flag7 = sampleInfo[0].inspectItem == "MRF" && Basic.bTailused;
                                if (flag7)
                                {
                                    List<clsTailProcess.tailResult> list = clsTailProcess.doTailSegProcess_single(result, task.imageSource);
                                    //LogHelper.WriteErrLog("调试：精子结果篇历" + list.Count);
                                    for (int i = 0; i < list.Count; i++)
                                    {
                                        try
                                        {
                                            result[i].vop_middlepiece = list[i].points.ToArray();
                                            result[i].middlepieceAngle = list[i].middlepieceAngle;
                                            result[i].middlepieceWidth = list[i].middlepieceWidth * 0.055803571428571425;
                                            result[i].tailLength = (double)list[i].length * 0.055803571428571425;
                                            result[i].tailAngle = list[i].tailAngle;
                                            result[i].ERCArea = list[i].ERCArea * 0.0031140385841836732;
                                            result[i].ERCWidth = list[i].ERCWidth * 0.055803571428571425;
                                            result[i].ERCHeight = list[i].ERCHeight * 0.055803571428571425;
                                            result[i].MDPnum = list[i].MDPnum;
                                            Model.Mrfrules mrf_rules = MRFSegment.mrf_validrules.FirstOrDefault((Model.Mrfrules p) => p.Usefield == "middlepieceAngle");
                                            Model.Mrfrules mrf_rules2 = MRFSegment.mrf_validrules.FirstOrDefault((Model.Mrfrules p) => p.Usefield == "middlepieceWidth");
                                            double num = (mrf_rules != null) ? mrf_rules.Max_value : 40.0;
                                            double num2 = (mrf_rules2 != null) ? mrf_rules2.Max_value : 1.5;
                                            bool flag8 = result[i].middlepieceAngle > num && (result[i].middlePieceType & 1) <= 0;
                                            if (flag8)
                                            {
                                                result[i].middlePieceType++;
                                                result[i].isNormal = 0;
                                            }
                                            bool flag9 = result[i].spermArea > 0.0 && result[i].ERCArea / result[i].spermArea >= 0.33333333333333331 && (result[i].middlePieceType & 4) <= 0 && (result[i].middlePieceType & 32) <= 0;
                                            if (flag9)
                                            {
                                                result[i].middlePieceType += 4;
                                                result[i].isNormal = 0;
                                            }
                                            else
                                            {
                                                bool flag10 = (result[i].ERCArea > 0.2 || Math.Max(result[i].middlepieceWidth, result[i].ERCWidth) > num2) && (result[i].middlePieceType & 2) <= 0 && (result[i].middlePieceType & 4) <= 0 && (result[i].middlePieceType & 32) <= 0;
                                                if (flag10)
                                                {
                                                    result[i].middlePieceType += 2;
                                                    result[i].isNormal = 0;
                                                }
                                            }
                                            Model.Mrfrules mrf_rules3 = MRFSegment.mrf_validrules.FirstOrDefault((Model.Mrfrules p) => p.Usefield == "tailAngle");
                                            double num3 = (mrf_rules3 != null) ? mrf_rules3.Min_value : 60.0;
                                            bool flag11 = result[i].tailAngle < num3 && (result[i].middlePieceType & 64) <= 0;
                                            if (flag11)
                                            {
                                                result[i].middlePieceType += 64;
                                                result[i].isNormal = 0;
                                            }
                                            Model.Mrfrules mrf_rules4 = MRFSegment.mrf_validrules.FirstOrDefault((Model.Mrfrules p) => p.Usefield == "tailLength");
                                            double num4 = (mrf_rules4 != null) ? mrf_rules4.Min_value : 40.0;
                                            bool flag12 = result[i].tailLength >= num4;
                                            if (flag12)
                                            {
                                                result[i].tailValid = true;
                                            }
                                            bool flag13 = result[i].tailLength < num4 && result[i].tailLength > 0.0 && list[i].boxnum > 0;
                                            if (flag13)
                                            {
                                                bool flag14 = list[i].borderestimate == 0 && (result[i].middlePieceType & 16) <= 0 && (result[i].middlePieceType & 32) <= 0;
                                                if (flag14)
                                                {
                                                    result[i].middlePieceType += 16;
                                                    result[i].isNormal = 0;
                                                    result[i].tailValid = true;
                                                }
                                            }
                                            else
                                            {
                                                bool flag15 = list[i].boxnum == 0;
                                                if (flag15)
                                                {
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            LogHelper.WriteErrLog($"图片分析异常1:{ex}");
                                        }
                                    }
                                }
                                TaskMgr.addSperm2db(result, task, image);
                                bool flag16 = SamplephotoServices.UpdatePhotoProcessed(task, 1);
                                //clsLog.writeLog(task.imageSource + "分析完成！");
                                SampleinfoServices.UpdateSampleProcessed(sampleInfo[0], 1);
                                TaskMgr.taskList.Remove(task);
                                if (DisplayUI)
                                {
                                    bool flag17 = TaskMgr.RaiseProcessedPhotoChangedEvent != null;
                                    if (flag17)
                                    {
                                        TaskMgr.RaiseProcessedPhotoChangedEvent(task);
                                    }
                                }

                            }
                            catch (Exception ex)
                            {
                                LogHelper.WriteErrLog($"图片分析异常2:{ex}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"图片分析异常3:{ex}");
            }

        }


        // Token: 0x06000595 RID: 1429 RVA: 0x00023270 File Offset: 0x00021470
        public static void runSegmentTask(string sampleid)
        {
            DisplayUI = true;
            Thread thread = new Thread(new ParameterizedThreadStart(TaskMgr.runSegment));
            thread.Start(sampleid);
        }
        public static void runSegmentTask(string sampleid,bool displayUI)
        {
            DisplayUI = displayUI;
            Thread thread = new Thread(new ParameterizedThreadStart(TaskMgr.runSegment));
            thread.Start(sampleid);
        }

        private static bool DisplayUI = true;

        // Token: 0x06000596 RID: 1430 RVA: 0x00023298 File Offset: 0x00021498
        public static void addSperm2db(List<Model.Sperm> sperms, Model.Samplephoto samplephoto, Image<Bgr, byte> image)
        {
            LogHelper.WriteErrLog("调试："+ sperms.Count);
            foreach (Model.Sperm sperm in sperms)
            {
                try
                {
                    bool flag = sperm != null;
                    if (flag)
                    {
                        sperm.sampleId = samplephoto.sampleId;
                        sperm.photoId = samplephoto.id;
                        sperm.fieldId = samplephoto.block;
                        sperm.isWrite = 1;
                        sperm.id = Convert.ToInt32(SpermServices.CreateObject(sperm));
                        bool valid = sperm.valid;
                        //if (valid)
                        //{
                        //    TaskMgr.saveSperm(sperm, image);  保存精子图，目前不需要
                        //}
                    }
                }
                catch
                {
                    throw;
                }
            }
        }

        public static void addSperm2db(List<Model.Sperm> sperms, Model.Samplephoto samplephoto)
        {
            foreach (Model.Sperm sperm in sperms)
            {
                try
                {
                    bool flag = sperm != null;
                    if (flag)
                    {
                        sperm.sampleId = samplephoto.sampleId;
                        sperm.photoId = samplephoto.id;
                        sperm.fieldId = samplephoto.block;
                        sperm.isWrite = 1;
                        sperm.id = Convert.ToInt32(SpermServices.CreateObject(sperm));//创建精子
                        //LogHelper.WriteErrLog("调试：创建精子" + sperm.id);
                        bool valid = sperm.valid;
                        //if (valid)
                        //{
                        //	TaskMgr.saveSperm(sperm, image);
                        //}
                    }
                }
                catch
                {
                    throw;
                }
            }
        }



        // Token: 0x06000597 RID: 1431 RVA: 0x00023354 File Offset: 0x00021554
        //private static void saveSperm(Model.Sperm sprm, Image<Bgr, byte> imgDFIc)
        //{
        //    double num;
        //    double num2;
        //    double num3;
        //    double num4;
        //    clsDfiSegment.getBox(sprm, (double)imgDFIc.Width, (double)imgDFIc.Height, out num, out num2, out num3, out num4);
        //    Rectangle rect = new Rectangle((int)num, (int)num2, (int)num3, (int)num4);
        //    Image<Bgr, byte> image = imgDFIc.GetSubRect(rect).Clone();
        //    string fullPath = Path.GetFullPath(sprm.imageSource);
        //    string text = fullPath.Substring(0, fullPath.LastIndexOf("\\")) + "\\sperms";
        //    string fileName = string.Concat(new object[]
        //    {
        //        text,
        //        "\\",
        //        sprm.sampleId,
        //        "_",
        //        sprm.fieldId,
        //        "_",
        //        sprm.spermindex,
        //        ".jpg"
        //    });
        //    TaskMgr.createDirA(text);
        //    image.Save(fileName);
        //    image.Dispose();
        //}

        // Token: 0x06000598 RID: 1432 RVA: 0x00023440 File Offset: 0x00021640
        public static void createDirA(string dir)
        {
            bool flag = !Directory.Exists(dir);
            if (flag)
            {
                Directory.CreateDirectory(dir);
            }
        }

        // Token: 0x0400032B RID: 811
        public static bool isExiting = false;

        // Token: 0x0400032C RID: 812
        private static object locker = new object();

        // Token: 0x0400032D RID: 813
        //public static clsSpermMySqlDb spermdb = new clsSpermMySqlDb();

        // Token: 0x0400032E RID: 814
        private static ObservableCollection<Model.Samplephoto> taskList = new ObservableCollection<Model.Samplephoto>();

        // Token: 0x02000072 RID: 114
        // (Invoke) Token: 0x0600059B RID: 1435
        public delegate void handleProcessedPhotoChanged(Model.Samplephoto sp);

        // Token: 0x02000073 RID: 115
        // (Invoke) Token: 0x0600059F RID: 1439
        public delegate void handleProcesssingPhotoChanged(Model.Samplephoto sp);
    }
}
