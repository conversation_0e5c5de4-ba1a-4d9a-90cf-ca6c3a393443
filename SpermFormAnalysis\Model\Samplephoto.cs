﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.Model
{
    public class Samplephoto
    {
        public int id { get; set; }

        public string sampleId { get; set; }

        public string type { get; set; }

        public int number { get; set; }

        public int block { get; set; }

        public string format { get; set; }

        public string imageSource { get; set; }

        public string marker { get; set; }

        public int sperm_num { get; set; }

        public short processed { get; set; }

        public int normal_num { get; set; }

        public int abnormal_num { get; set; }

        /// <summary>
        /// 外扩字段
        /// </summary>
        public string fieldName { get; set; }

        /// <summary>
        /// 图片对象
        /// </summary>
        //public BitmapImage bitmapImageSource { get; set; }

        ///图片上的精子
        public ObservableCollection<Sperm> detailsList { get; set; }
    }
}
