﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks.Parallel</name>
  </assembly>
  <members>
    <member name="T:System.Threading.Tasks.Parallel">
      <summary>Unterstützt parallele Schleifen und Bereiche.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Action{System.Int32})">
      <summary>Führt eine for-Schleife (For in Visual Basic) aus, in der Iterationen parallel ausgeführt werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Action{System.Int32,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt eine for (For in Visual Basic)-Schleife aus, in der Iterationen parallel ausgeführt werden können und der Status der Schleife beobachtet und bearbeitet werden kann.  </summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int32,System.Int32,System.Func{``0},System.Func{System.Int32,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Führt eine for-Schleife (For in Visual Basic) mit lokalen Threaddaten aus, in der Iterationen parallel ausgeführt und der Status der Schleife beobachtet und bearbeitet werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Action{System.Int32})">
      <summary>Führt eine for (For in Visual Basic)-Schleife aus, in der Iterationen parallel ausgeführt und Schleifenoptionen konfiguriert werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Action{System.Int32,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt eine for (For in Visual Basic)-Schleife aus, in der Iterationen parallel ausgeführt und Schleifenoptionen konfiguriert werden können und wo der Status der Schleife beobachtet und bearbeitet werden kann.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Func{``0},System.Func{System.Int32,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Führt eine for (For in Visual Basic)-Schleife mit lokalen Threaddaten aus, in der Iterationen parallel ausgeführt, Schleifenoptionen konfiguriert und der Status der Schleife beobachtet und bearbeitet werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Action{System.Int64})">
      <summary>Führt eine for-Schleife (For in Visual Basic) mit 64-Bit-Indizes aus, in der Iterationen parallel ausgeführt werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Action{System.Int64,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt eine for-Schleife (For in Visual Basic) mit 64-Bit-Indizes aus, in der Iterationen parallel ausgeführt werden können und der Zustand der Schleife überwacht und bearbeitet werden kann.</summary>
      <returns>Eine <see cref="T:System.Threading.Tasks.ParallelLoopResult" />-Struktur, die Informationen darüber enthält, welcher Teil der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int64,System.Int64,System.Func{``0},System.Func{System.Int64,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Führt eine for (For in Visual Basic)-Schleife mit 64-Bit-Indizes sowie lokalen Threaddaten aus, in denen Iterationen parallel ausgeführt werden können, und der Status der Schleife kann beobachtet und bearbeitet werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Action{System.Int64})">
      <summary>Führt eine for (For in Visual Basic)-Schleife mit 64-Bit-Indizes aus, in der Iterationen parallel ausgeführt und Schleifenoptionen konfiguriert werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Action{System.Int64,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt eine for (For in Visual Basic)-Schleife mit 64-Bit-Indizes aus, in der Iterationen parallel ausgeführt und Schleifenoptionen konfiguriert werden können, und der Status der Schleife kann beobachtet und bearbeitet werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Func{``0},System.Func{System.Int64,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Führt eine for-Schleife (For in Visual Basic) mit 64-Bit Indizes und threadlokalen Daten aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar, und der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="fromInclusive">Der Startindex, einschließend.</param>
      <param name="toExclusive">Der Endindex, ausschließend.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="localInit">Der Funktionsdelegat, der den Ausgangszustand der lokalen Daten für jeden Thread zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der eine abschließende Aktion für den lokalen Zustand jedes Threads ausführt.</param>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.OrderablePartitioner{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> aus. Iterationen können parallel ablaufen, und der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der sortierbare Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The <see cref="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized" /> property in the source orderable partitioner returns false.-or-Any methods in the source orderable partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.OrderablePartitioner{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten für einen <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der sortierbare Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.OrderablePartitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> aus. Iterationen können parallel ablaufen, Schleifenoptionen sind konfigurierbar, und der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der sortierbare Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is  null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The <see cref="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> orderable partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.OrderablePartitioner{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit 64-Bit-Indizes und threadlokalen Daten für einen <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der sortierbare Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> or <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null  partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) für ein <see cref="T:System.Collections.Concurrent.Partitioner" />-Objekt aus, in dem Iterationen parallel ausgeführt werden können.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is  null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.-or-The <see cref="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)" /> method in the <paramref name="source" /> partitioner does not return the correct number of partitions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.Concurrent.Partitioner" /> aus. Iterationen können parallel ablaufen, und der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-A method in the <paramref name="source" /> partitioner returns null.-or-The <see cref="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)" /> method in the <paramref name="source" /> partitioner does not return the correct number of partitions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.Partitioner{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten für einen <see cref="T:System.Collections.Concurrent.Partitioner" /> aus. Iterationen können parallel ablaufen. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.Concurrent.Partitioner" /> aus. Iterationen können parallel ausgeführt werden, Schleifenoptionen sind konfigurierbar.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.Concurrent.Partitioner" /> aus. Iterationen können parallel ablaufen, Schleifenoptionen sind konfigurierbar, und der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten für einen <see cref="T:System.Collections.Concurrent.Partitioner" /> aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Der Partitionierer, der die ursprüngliche Datenquelle enthält.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Elemente in <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit einem <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit 64-Bit-Indizes für einen <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ausgeführt werden. Der Schleifenzustand kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten für einen <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten für einen <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) für ein <see cref="T:System.Collections.IEnumerable" />-Objekt aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) für ein <see cref="T:System.Collections.IEnumerable" />-Objekt aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit 64-Bit-Indizes für einen <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten für einen <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Führt einen foreach-Vorgang (For Each in Visual Basic) mit threadlokalen Daten und 64-Bit-Indizes für einen <see cref="T:System.Collections.IEnumerable" /> aus. Iterationen können parallel ablaufen. Schleifenoptionen sind konfigurierbar. Der Schleifenstatus kann beobachtet und manipuliert werden.</summary>
      <returns>Eine Struktur, die Informationen darüber enthält, welcher Abschnitt der Schleife abgeschlossen ist.</returns>
      <param name="source">Eine auflistbare Datenquelle.</param>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="localInit">Der Funktionsdelegat, der den ursprünglichen Zustand der lokalen Daten für jede Aufgabe zurückgibt.</param>
      <param name="body">Der Delegat, der einmal pro Iteration aufgerufen wird.</param>
      <param name="localFinally">Der Delegat, der die endgültige Aktion beim lokalen Status jeder Aufgabe ausführt.</param>
      <typeparam name="TSource">Der Typ der Daten in der Quelle.</typeparam>
      <typeparam name="TLocal">Der Typ der threadlokalen Daten.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.Invoke(System.Action[])">
      <summary>Führt jede der bereitgestellten Aktionen aus (möglicherweise parallel).</summary>
      <param name="actions">Ein Array auszuführender <see cref="T:System.Action" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="actions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that is thrown when any action in the <paramref name="actions" /> array throws an exception.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="actions" /> array contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.Invoke(System.Threading.Tasks.ParallelOptions,System.Action[])">
      <summary>Führt die einzelnen bereitgestellten Aktionen (nach Möglichkeit parallel) aus, es sei denn, der Vorgang wird vom Benutzer abgebrochen.</summary>
      <param name="parallelOptions">Ein Objekt, das das Verhalten dieses Vorgangs konfiguriert.</param>
      <param name="actions">Ein Array auszuführender Aktionen.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> is set.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="actions" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that is thrown when any action in the <paramref name="actions" /> array throws an exception.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="actions" /> array contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ParallelLoopResult">
      <summary>Gibt den Status der Ausführung einer <see cref="T:System.Threading.Tasks.Parallel" />-Schleife an.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopResult.IsCompleted">
      <summary>Ruft ab, ob die Schleife bis zum Abschluss ausgeführt wurde, das heißt, dass alle Iterationen der Schleife ausgeführt wurden und keine Anforderung eines vorzeitigen Abbruchs an die Schleife gesendet wurde.</summary>
      <returns>True, wenn die Schleife zum Abschluss ausgeführt wurde, andernfalls false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopResult.LowestBreakIteration">
      <summary>Ruft den Indes der niedrigsten Iteration ab, von der <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> aufgerufen wurde.</summary>
      <returns>Gibt eine ganze Zahl zurück, die die niedrigste Iteration darstellt, aus der die Break-Anweisung aufgerufen wurde.</returns>
    </member>
    <member name="T:System.Threading.Tasks.ParallelLoopState">
      <summary>Ermöglicht Iterationen von parallelen Schleifen die Interaktion mit anderen Iterationen.Eine Instanz dieser Klasse wird von der <see cref="T:System.Threading.Tasks.Parallel" />-Klasse für jede Schleife bereitgestellt. Sie können keine Instanzen im Code erstellen.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ParallelLoopState.Break">
      <summary>Gibt an, dass die <see cref="T:System.Threading.Tasks.Parallel" />-Schleife die Ausführung von Iterationen nach der aktuellen Iteration beenden soll, sobald dies dem System möglich ist. </summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" />-Methode wurde bereits zuvor aufgerufen.<see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> und <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> können von Iterationen derselben Schleife nicht in Kombination verwendet werden.</exception>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.IsExceptional">
      <summary>Ruft einen Wert ab, der angibt, ob eine Iteration der Schleife eine Ausnahme ausgelöst hat, die nicht durch diese Iteration behandelt wurde. </summary>
      <returns>true, wenn eine nicht behandelte Ausnahme ausgelöst wurde, andernfalls false.  </returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.IsStopped">
      <summary>Ruft einen Wert ab, der angibt, ob eine Iteration der Schleife die <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" />-Methode aufgerufen hat. </summary>
      <returns>true, wenn die Schleife beendet wurde, indem in einer Iteration die <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" />-Methode aufgerufen wurde, andernfalls false. </returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.LowestBreakIteration">
      <summary>Ruft die niedrigste Iteration der Schleife ab, aus der <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> aufgerufen wurde. </summary>
      <returns>Die niedrigste Iteration, aus der <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> aufgerufen wurde.Bei einer <see cref="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0})" />-Schleife basiert der Wert auf einem intern generierten Index.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.ShouldExitCurrentIteration">
      <summary>Ruft einen Wert ab, der angibt, ob die aktuelle Iteration der Schleife basierend auf Anforderungen dieser oder anderer Iterationen beendet werden soll.</summary>
      <returns>true, wenn die aktuelle Iteration beendet werden soll, andernfalls false. </returns>
    </member>
    <member name="M:System.Threading.Tasks.ParallelLoopState.Stop">
      <summary>Gibt an, dass die <see cref="T:System.Threading.Tasks.Parallel" />-Schleife die Ausführung beenden soll, sobald dies dem System möglich ist.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> -Methode wurde bereits aufgerufen.<see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> und <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> können von Iterationen derselben Schleife nicht in Kombination verwendet werden.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ParallelOptions">
      <summary>Speichert Optionen, die die Ausführung von Methoden für die <see cref="T:System.Threading.Tasks.Parallel" />-Klasse konfigurieren.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ParallelOptions.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Threading.Tasks.ParallelOptions" />-Klasse.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.CancellationToken">
      <summary>Ruft das <see cref="T:System.Threading.CancellationToken" /> ab, das dieser <see cref="T:System.Threading.Tasks.ParallelOptions" />-Instanz zugeordnet ist, oder legt dieses fest.</summary>
      <returns>Das Token, das dieser Instanz zugeordnet ist.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.MaxDegreeOfParallelism">
      <summary>Ruft die maximale Anzahl der gleichzeitigen Aufgaben fest, die von dieser Instanz <see cref="T:System.Threading.Tasks.ParallelOptions" /> aktiviert werden.</summary>
      <returns>Eine ganze Zahl, die den maximalen Parallelitätsgrad darstellt.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Eigenschaft wird von null oder auf einen Wert festgelegt, der kleiner als -1 ist.</exception>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.TaskScheduler">
      <summary>Ruft das <see cref="T:System.Threading.Tasks.TaskScheduler" /> ab, das dieser <see cref="T:System.Threading.Tasks.ParallelOptions" />-Instanz zugeordnet ist, oder legt dieses fest.Durch Festlegen dieser Eigenschaft auf NULL wird angegeben, dass der aktuelle Taskplaner verwendet werden soll.</summary>
      <returns>Der Taskplaner, der dieser Instanz zugeordnet ist.</returns>
    </member>
  </members>
</doc>