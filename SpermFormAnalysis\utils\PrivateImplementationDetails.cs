﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.utils
{
    [CompilerGenerated]
    internal sealed class PrivateImplementationDetails
    {
        // Token: 0x06000C61 RID: 3169 RVA: 0x0003D604 File Offset: 0x0003B804
        internal static uint ComputeStringHash(string s)
        {
            uint num = 0;
            if (s != null)
            {
                num = 2166136261U;
                for (int i = 0; i < s.Length; i++)
                {
                    num = ((uint)s[i] ^ num) * 16777619U;
                }
            }
            return num;
        }

    }
}
