﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.utils
{
    public static class Crypto
    {
        // Token: 0x020000BF RID: 191
        public static class DES
        {
            // Token: 0x06000A2B RID: 2603 RVA: 0x00032390 File Offset: 0x00030590
            public static string Encode(string data)
            {
                byte[] bytes = Encoding.ASCII.GetBytes("6678180@");
                byte[] bytes2 = Encoding.ASCII.GetBytes("qq.comhw");
                DESCryptoServiceProvider descryptoServiceProvider = new DESCryptoServiceProvider();
                int keySize = descryptoServiceProvider.KeySize;
                MemoryStream memoryStream = new MemoryStream();
                CryptoStream cryptoStream = new CryptoStream(memoryStream, descryptoServiceProvider.CreateEncryptor(bytes, bytes2), CryptoStreamMode.Write);
                StreamWriter streamWriter = new StreamWriter(cryptoStream);
                streamWriter.Write(data);
                streamWriter.Flush();
                cryptoStream.FlushFinalBlock();
                streamWriter.Flush();
                return Convert.ToBase64String(memoryStream.GetBuffer(), 0, (int)memoryStream.Length);
            }

            // Token: 0x06000A2C RID: 2604 RVA: 0x0003242C File Offset: 0x0003062C
            public static string Decode(string data)
            {
                byte[] bytes = Encoding.ASCII.GetBytes("6678180@");
                byte[] bytes2 = Encoding.ASCII.GetBytes("qq.comhw");
                byte[] buffer;
                try
                {
                    buffer = Convert.FromBase64String(data);
                }
                catch
                {
                    return null;
                }
                DESCryptoServiceProvider descryptoServiceProvider = new DESCryptoServiceProvider();
                MemoryStream stream = new MemoryStream(buffer);
                CryptoStream stream2 = new CryptoStream(stream, descryptoServiceProvider.CreateDecryptor(bytes, bytes2), CryptoStreamMode.Read);
                StreamReader streamReader = new StreamReader(stream2);
                return streamReader.ReadToEnd();
            }

            // Token: 0x06000A2D RID: 2605 RVA: 0x000324B4 File Offset: 0x000306B4
            public static string MD5(string Text)
            {
                byte[] bytes = Encoding.Default.GetBytes(Text);
                string result;
                try
                {
                    MD5CryptoServiceProvider md5CryptoServiceProvider = new MD5CryptoServiceProvider();
                    byte[] array = md5CryptoServiceProvider.ComputeHash(bytes);
                    string text = "";
                    foreach (byte b in array)
                    {
                        bool flag = b < 16;
                        if (flag)
                        {
                            text = text + "0" + b.ToString("X");
                        }
                        else
                        {
                            text += b.ToString("X");
                        }
                    }
                    result = text.ToLower();
                }
                catch
                {
                    throw;
                }
                return result;
            }

            // Token: 0x0400053F RID: 1343
            private const string KEY_64 = "6678180@";

            // Token: 0x04000540 RID: 1344
            private const string IV_64 = "qq.comhw";
        }
    }
}
