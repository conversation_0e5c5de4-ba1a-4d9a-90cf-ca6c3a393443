using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Microsoft.Win32;
using SpermAnalysisWPF.Model;

namespace SpermAnalysisWPF.Windows
{
    public partial class ImageViewWindow : Window
    {
        private List<Sperm> spermResults;
        private string imagePath;
        private BitmapImage originalImage;
        private double currentZoom = 1.0;
        private List<UIElement> annotationElements = new List<UIElement>();

        public ImageViewWindow(List<Sperm> results, string imageFilePath)
        {
            InitializeComponent();
            spermResults = results ?? new List<Sperm>();
            imagePath = imageFilePath;
            
            LoadImage();
            LoadSpermData();
            UpdateStatistics();
            DrawAnnotations();
        }

        private void LoadImage()
        {
            try
            {
                if (File.Exists(imagePath))
                {
                    originalImage = new BitmapImage();
                    originalImage.BeginInit();
                    originalImage.UriSource = new Uri(imagePath);
                    originalImage.EndInit();
                    
                    MainImage.Source = originalImage;
                    ImageCanvas.Width = originalImage.PixelWidth;
                    ImageCanvas.Height = originalImage.PixelHeight;
                    
                    StatusText.Text = $"图像已加载: {System.IO.Path.GetFileName(imagePath)}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载图像失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSpermData()
        {
            // 为精子编号
            for (int i = 0; i < spermResults.Count; i++)
            {
                spermResults[i].spermindex = i + 1;
            }
            
            SpermDataGrid.ItemsSource = spermResults;
        }

        private void UpdateStatistics()
        {
            int totalCount = spermResults.Count;
            int normalCount = spermResults.Count(s => s.isNormal == 1);
            int abnormalCount = totalCount - normalCount;
            double normalRate = totalCount > 0 ? (double)normalCount / totalCount * 100 : 0;

            TotalCountText.Text = $"总精子数: {totalCount}";
            NormalCountText.Text = $"正常精子: {normalCount}";
            AbnormalCountText.Text = $"异常精子: {abnormalCount}";
            NormalRateText.Text = $"正常率: {normalRate:F1}%";
        }

        private void DrawAnnotations()
        {
            ClearAnnotations();

            if (originalImage == null || spermResults == null) return;

            foreach (var sperm in spermResults)
            {
                DrawSpermAnnotation(sperm);
            }
        }

        private void DrawSpermAnnotation(Sperm sperm)
        {
            // 参考原项目的绘制逻辑
            double imageWidth = originalImage.PixelWidth;
            double imageHeight = originalImage.PixelHeight;

            // 绘制检测框
            if (ShowDetectionBox.IsChecked == true)
            {
                var rect = new Rectangle
                {
                    Width = sperm.xmax - sperm.xmin,
                    Height = sperm.ymax - sperm.ymin,
                    Stroke = sperm.isNormal == 1 ? Brushes.Green : Brushes.Red,
                    StrokeThickness = 2,
                    Fill = Brushes.Transparent
                };

                Canvas.SetLeft(rect, sperm.xmin);
                Canvas.SetTop(rect, sperm.ymin);
                ImageCanvas.Children.Add(rect);
                annotationElements.Add(rect);
            }

            // 绘制精子轮廓
            if (ShowSegmentation.IsChecked == true && !string.IsNullOrEmpty(sperm.vop_sperm))
            {
                DrawContour(sperm.vop_sperm, Brushes.Blue, 1);
            }

            // 绘制顶体轮廓
            if (ShowSegmentation.IsChecked == true && !string.IsNullOrEmpty(sperm.vop_acrosome))
            {
                DrawContour(sperm.vop_acrosome, Brushes.Yellow, 1);
            }

            // 绘制核轮廓
            if (ShowSegmentation.IsChecked == true && !string.IsNullOrEmpty(sperm.vop_kernel))
            {
                DrawContour(sperm.vop_kernel, Brushes.Orange, 1);
            }

            // 绘制标签
            if (ShowLabels.IsChecked == true)
            {
                var label = new TextBlock
                {
                    Text = $"{sperm.spermindex}",
                    Foreground = sperm.isNormal == 1 ? Brushes.Green : Brushes.Red,
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    Background = Brushes.White
                };

                Canvas.SetLeft(label, sperm.xmin);
                Canvas.SetTop(label, sperm.ymin - 20);
                ImageCanvas.Children.Add(label);
                annotationElements.Add(label);
            }
        }

        private void DrawContour(string contourData, Brush brush, double thickness)
        {
            if (string.IsNullOrEmpty(contourData)) return;

            try
            {
                var points = ParseContourPoints(contourData);
                if (points.Count < 3) return;

                var polygon = new Polygon
                {
                    Points = new PointCollection(points),
                    Stroke = brush,
                    StrokeThickness = thickness,
                    Fill = Brushes.Transparent
                };

                ImageCanvas.Children.Add(polygon);
                annotationElements.Add(polygon);
            }
            catch (Exception ex)
            {
                // 忽略轮廓解析错误
            }
        }

        private List<Point> ParseContourPoints(string contourData)
        {
            var points = new List<Point>();
            
            if (string.IsNullOrEmpty(contourData)) return points;

            var pointStrings = contourData.Split(';');
            foreach (var pointString in pointStrings)
            {
                var coords = pointString.Split(',');
                if (coords.Length == 2 && 
                    double.TryParse(coords[0], out double x) && 
                    double.TryParse(coords[1], out double y))
                {
                    points.Add(new Point(x, y));
                }
            }

            return points;
        }

        private void ClearAnnotations()
        {
            foreach (var element in annotationElements)
            {
                ImageCanvas.Children.Remove(element);
            }
            annotationElements.Clear();
        }

        private void ZoomInBtn_Click(object sender, RoutedEventArgs e)
        {
            currentZoom *= 1.2;
            ApplyZoom();
        }

        private void ZoomOutBtn_Click(object sender, RoutedEventArgs e)
        {
            currentZoom /= 1.2;
            ApplyZoom();
        }

        private void ResetZoomBtn_Click(object sender, RoutedEventArgs e)
        {
            currentZoom = 1.0;
            ApplyZoom();
        }

        private void ApplyZoom()
        {
            if (originalImage != null)
            {
                ImageCanvas.Width = originalImage.PixelWidth * currentZoom;
                ImageCanvas.Height = originalImage.PixelHeight * currentZoom;
                
                var transform = new ScaleTransform(currentZoom, currentZoom);
                ImageCanvas.RenderTransform = transform;
                
                ZoomText.Text = $"缩放: {currentZoom * 100:F0}%";
            }
        }

        private void ShowDetectionBox_Changed(object sender, RoutedEventArgs e)
        {
            DrawAnnotations();
        }

        private void ShowSegmentation_Changed(object sender, RoutedEventArgs e)
        {
            DrawAnnotations();
        }

        private void ShowLabels_Changed(object sender, RoutedEventArgs e)
        {
            DrawAnnotations();
        }

        private void SpermDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SpermDataGrid.SelectedItem is Sperm selectedSperm)
            {
                ShowSpermDetails(selectedSperm);
                HighlightSperm(selectedSperm);
            }
        }

        private void ShowSpermDetails(Sperm sperm)
        {
            var details = $"精子 #{sperm.spermindex}\n" +
                         $"面积: {sperm.spermArea:F2}\n" +
                         $"长轴: {sperm.longAxis:F2}\n" +
                         $"短轴: {sperm.shortAxis:F2}\n" +
                         $"椭圆比: {sperm.ellipsRatio:F2}\n" +
                         $"顶体面积: {sperm.acrosomeArea:F2}\n" +
                         $"顶体比例: {sperm.acrosomeRatio:F2}\n" +
                         $"状态: {(sperm.isNormal == 1 ? "正常" : "异常")}";

            DetailText.Text = details;
        }

        private void HighlightSperm(Sperm sperm)
        {
            // 可以添加高亮显示选中精子的逻辑
        }

        private void ExportBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PNG图像|*.png|JPEG图像|*.jpg",
                    DefaultExt = "png",
                    FileName = $"分析结果_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    ExportAnnotatedImage(saveDialog.FileName);
                    MessageBox.Show("图像导出成功！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportAnnotatedImage(string fileName)
        {
            // 导出带标注的图像
            var renderBitmap = new RenderTargetBitmap(
                (int)ImageCanvas.Width, (int)ImageCanvas.Height, 96, 96, PixelFormats.Pbgra32);
            renderBitmap.Render(ImageCanvas);

            var encoder = new PngBitmapEncoder();
            encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

            using (var stream = new FileStream(fileName, FileMode.Create))
            {
                encoder.Save(stream);
            }
        }
    }

    // 转换器类
    public class NormalStatusConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int status)
            {
                return status == 1 ? "正常" : "异常";
            }
            return "未知";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
