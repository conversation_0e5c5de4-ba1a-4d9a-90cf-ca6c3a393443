@echo off
title MX450显卡专用启动器
color 0E

cls
echo.
echo    ╔══════════════════════════════════════════════════════════╗
echo    ║                MX450显卡专用启动器                        ║
echo    ║              精子形态分析系统                              ║
echo    ╚══════════════════════════════════════════════════════════╝
echo.
echo    您的显卡：NVIDIA GeForce MX450 (2GB)
echo    问题：GPU内存不足导致尾巴信息无法显示
echo    解决方案：强制CPU模式 + 环境优化
echo.
echo ════════════════════════════════════════════════════════════
echo.

echo [1/5] 环境检查...
if not exist "SpermFormAnalysis.exe" (
    echo    ✗ 错误：找不到程序文件
    pause
    exit /b 1
)
echo    ✓ 程序文件正常

echo.
echo [2/5] 强制CPU模式配置...
REM 设置最强的CPU强制模式
set CUDA_VISIBLE_DEVICES=-1
set TF_CPP_MIN_LOG_LEVEL=2
set TF_FORCE_GPU_ALLOW_GROWTH=false
set NVIDIA_VISIBLE_DEVICES=none
set TF_DEVICE_MIN_SYS_MEMORY_IN_MB=4096
set OMP_NUM_THREADS=4
set TF_NUM_INTEROP_THREADS=4
set TF_NUM_INTRAOP_THREADS=4
echo    ✓ CPU模式环境变量已设置

echo.
echo [3/5] 创建MX450专用配置...
(
echo # MX450显卡专用配置文件
echo # 此配置强制使用CPU模式，解决显存不足问题
echo [GPU设置]
echo ForceUseCPU=true
echo GPUMemoryOptimized=false  
echo GPUMemoryLimit=0
echo EnableTailProcessing=true
echo AutoDetectGPU=false
echo.
echo [性能设置]
echo # CPU模式性能优化
echo CPUThreads=4
echo ProcessTimeout=300
echo.
echo [故障排除]
echo # 如果仍有问题：
echo # 1. 确保关闭了其他占用GPU的程序
echo # 2. 重启电脑后再试
echo # 3. 检查Log文件夹中的详细日志
) > gpu_config.ini
echo    ✓ MX450专用配置文件已创建

echo.
echo [4/5] 系统优化建议...
echo    建议在启动前：
echo    • 关闭浏览器中的硬件加速
echo    • 关闭其他可能使用GPU的程序
echo    • 确保系统内存充足（建议8GB+）
echo    ✓ 优化建议已显示

echo.
echo [5/5] 启动程序...
echo ════════════════════════════════════════════════════════════
echo                        重要提示
echo ════════════════════════════════════════════════════════════
echo.
echo  🔥 CPU模式性能说明：
echo     • 尾巴信息处理时间：2-5分钟（比GPU模式慢）
echo     • 请耐心等待，不要重复点击
echo     • 建议一次只处理一个样本
echo.
echo  📋 测试步骤：
echo     1. 上传精子图像文件
echo     2. 点击"开始分析"并等待完成
echo     3. 点击"实验查询"
echo     4. 检查尾巴信息是否正常显示
echo.
echo  📁 如果仍有问题，请查看：
echo     • Log文件夹中的日志文件
echo     • 错误信息截图
echo.
echo ════════════════════════════════════════════════════════════
echo.

echo 正在启动精子形态分析系统（MX450优化版）...
start "" "SpermFormAnalysis.exe"

echo ✓ 程序已启动！
echo.
echo 💡 提示：此窗口可以关闭，不影响程序运行
echo.
echo 按任意键关闭此窗口...
pause >nul
