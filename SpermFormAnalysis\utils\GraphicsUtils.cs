﻿using System;
using System.Collections.Generic;
using System.Drawing.Drawing2D;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.utils
{
    class GraphicsUtils
    {
        /// <summary>
        /// 主色调
        /// </summary>
        public static Color MainColor = Color.FromArgb(235, 243, 255);

        public static Pen DimGrayPen1 = new Pen(Color.DimGray);
        public static Pen Pen111333333_15 = new Pen(Color.FromArgb(155, 111, 111, 111), 4);

        public static Brush MainBrush = new SolidBrush(MainColor);
        public static Brush WhiteBrush = new SolidBrush(Color.White);

        public static Brush DimGrayBrush = new SolidBrush(Color.DimGray);


        public static Brush Brush111333333 = new SolidBrush(Color.FromArgb(111, 33, 33, 33));

        public static Font Font10B = new Font("微软雅黑", 10F, FontStyle.Bold);
        public static Font Font9 = new Font("微软雅黑", 9F);
        public static Font Font12 = new Font("微软雅黑", 12F);
        public static Font Font12B = new Font("微软雅黑", 12F, FontStyle.Bold);



        public static GraphicsPath GetRectangleGp(int x, int y, int width, int height, int r)
        {
            int l = 2 * r;
            int rigth_x = x + width;
            int bottom_y = y + height;
            GraphicsPath gp = new GraphicsPath();
            gp.AddLine(new Point(x + r, y), new Point(rigth_x - r, y));
            gp.AddArc(new Rectangle(rigth_x - l, y, l, l), 270F, 90F);
            gp.AddLine(new Point(rigth_x, y + r), new Point(rigth_x, bottom_y - r));
            gp.AddArc(new Rectangle(rigth_x - l, bottom_y - l, l, l), 0F, 90F);
            gp.AddLine(new Point(rigth_x - r, bottom_y), new Point(x + r, bottom_y));
            gp.AddArc(new Rectangle(x, bottom_y - l, l, l), 90F, 90F);
            gp.AddLine(new Point(x, bottom_y - r), new Point(x, y + r));
            gp.AddArc(new Rectangle(x, y, l, l), 180F, 90F);
            return gp;
        }

        public static void FillRectangleGp(int x, int y, int width, int height, int r, Color color, Graphics graphics)
        {
            GraphicsPath gp = GetRectangleGp(x, y, width, height, r);
            Brush brush = new SolidBrush(color);
            graphics.FillPath(brush, gp);
            brush.Dispose();
            gp.Dispose();
        }

    }
}
