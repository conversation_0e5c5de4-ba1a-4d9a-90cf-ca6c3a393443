﻿using Emgu.CV.Structure;
using SpermFormAnalysis.utils;
using Sunny.UI.Win32;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms.VisualStyles;
using System.Windows.Forms;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using System.Windows.Media;
using System.Windows;
using System.Drawing;
using SpermFormAnalysis.Segments;
using System.Windows.Ink;
using System.Windows.Forms.DataVisualization.Charting;

namespace SpermFormAnalysis.common
{
    public static class Drawing
    {
        // Token: 0x1400001F RID: 31
        // (add) Token: 0x06000A63 RID: 2659 RVA: 0x00032FA4 File Offset: 0x000311A4
        // (remove) Token: 0x06000A64 RID: 2660 RVA: 0x00032FD8 File Offset: 0x000311D8
        public static event Drawing.HandleSelectedSpermChanged RaiseSelectedSpermChangedEvent;

        // Token: 0x14000020 RID: 32
        // (add) Token: 0x06000A65 RID: 2661 RVA: 0x0003300C File Offset: 0x0003120C
        // (remove) Token: 0x06000A66 RID: 2662 RVA: 0x00033040 File Offset: 0x00031240
        public static event Drawing.HandleDeletedSperm RaiseDeletedSpermEvent;

        // Token: 0x14000021 RID: 33
        // (add) Token: 0x06000A67 RID: 2663 RVA: 0x00033074 File Offset: 0x00031274
        // (remove) Token: 0x06000A68 RID: 2664 RVA: 0x000330A8 File Offset: 0x000312A8
        public static event Drawing.HandleSetNormalSperm RaiseSetNormalSpermEvent;

        // Token: 0x14000022 RID: 34
        // (add) Token: 0x06000A69 RID: 2665 RVA: 0x000330DC File Offset: 0x000312DC
        // (remove) Token: 0x06000A6A RID: 2666 RVA: 0x00033110 File Offset: 0x00031310
        public static event Drawing.HandleSetAbnormalSperm RaiseSetAbnormalSpermEvent;


        // Token: 0x06000A74 RID: 2676 RVA: 0x000339C8 File Offset: 0x00031BC8
        public static string GetToolTip(Model.Sperm sperm)
        {
            bool flag = sperm.isNormal == 1;
            string text;
            if (flag)
            {
                text = "正常精子";
            }
            else
            {
                bool flag2 = sperm.isNormal == 2;
                if (flag2)
                {
                    text = "亚临床:";
                }
                else
                {
                    text = "异常精子:";
                }
                text += (((sperm.shapeType & 1) > 0) ? (((sperm.shapeType & 4096) <= 0) ? (Environment.NewLine + "锥形") : (Environment.NewLine + "亚临床锥")) : "");
                text += (((sperm.shapeType & 4) > 0) ? (Environment.NewLine + "小头") : "");
                text += (((sperm.shapeType & 8) > 0) ? (Environment.NewLine + "大头") : "");
                text += (((sperm.shapeType & 16) > 0) ? (((sperm.shapeType & 4096) <= 0) ? (Environment.NewLine + "圆头") : (Environment.NewLine + "亚临床圆")) : "");
                text += (((sperm.shapeType & 32) > 0) ? (((sperm.shapeType & 4096) <= 0) ? (Environment.NewLine + "梨形") : (Environment.NewLine + "亚临床梨")) : "");
                text += (((sperm.shapeType & 64) > 0) ? (Environment.NewLine + "不定形") : "");
                text += (((sperm.shapeType & 128) > 0) ? (Environment.NewLine + "矮") : "");
                text += (((sperm.shapeType & 256) > 0) ? (Environment.NewLine + "高") : "");
                text += (((sperm.shapeType & 2) > 0) ? (Environment.NewLine + "瘦头") : "");
                text += (((sperm.shapeType & 512) > 0) ? (Environment.NewLine + "胖头") : "");
                text += (((sperm.shapeType & 1024) > 0) ? (Environment.NewLine + "不规则") : "");
                bool flag3 = text.IndexOf("亚临床") < 0 && text.IndexOf("矮") < 0 && text.IndexOf("高") < 0 && text.IndexOf("胖") < 0 && text.IndexOf("瘦") < 0 && text.IndexOf("不规则") < 0 && (sperm.shapeType & 4096) > 0;
                if (flag3)
                {
                    text = text + Environment.NewLine + "亚临床";
                }
                int acrosomeType = sperm.acrosomeType;
                text += (((acrosomeType & 8) > 0) ? (Environment.NewLine + "顶体多空泡") : "");
                text += (((acrosomeType & 4) > 0) ? (Environment.NewLine + "超大顶体") : "");
                text += (((acrosomeType & 2) > 0) ? (Environment.NewLine + "小顶体") : "");
                text += (((acrosomeType & 1) > 0) ? (Environment.NewLine + "超小顶体") : "");
                text += (((acrosomeType & 16) > 0) ? (Environment.NewLine + "顶体不均匀 ") : "");
                int kernelType = sperm.kernelType;
                text += (((kernelType & 8) > 0) ? (Environment.NewLine + "核区空泡") : "");
                text += (((kernelType & 16) > 0) ? (Environment.NewLine + "核区不均匀") : "");
                int middlePieceType = sperm.middlePieceType;
                text += (((middlePieceType & 1) > 0) ? (Environment.NewLine + "中段插入异常") : "");
                text += (((middlePieceType & 2) > 0) ? (Environment.NewLine + "中段粗大") : "");
                text += (((middlePieceType & 4) > 0) ? (Environment.NewLine + "过量胞质残留") : "");
                text += (((middlePieceType & 8) > 0) ? (Environment.NewLine + "无尾") : "");
                text += (((middlePieceType & 16) > 0) ? (Environment.NewLine + "短尾") : "");
                text += (((middlePieceType & 32) > 0) ? (Environment.NewLine + "卷尾") : "");
            }
            return text;
        }

        public static void DrawEllips(Graphics g, float width, float height, List<Model.Sperm> sperms)
        {
            bool flag = width != 0.0 && sperms.Count > 0;
            if (flag)
            {
                string imageSource = sperms[0].imageSource;
                //System.Windows.Point point = new System.Windows.Point(0, 0);
                for (int i = 0; i < sperms.Count; i++)
                {
                    bool valid = sperms[i].valid;
                    if (valid)
                    {
                        double num = width / Basic.imageSize.Width;
                        double num2 = height / Basic.imageSize.Height;
                        double num3;
                        double num4;
                        double num5;
                        double num6;
                        //clsDfiSegment.getBox(sperms[i], Basic.imageSize.Width, Basic.imageSize.Height, out num3, out num4, out num5, out num6);
                        //System.Windows.Point center = default(System.Windows.Point);
                        //center.X = (num3 + point.X + sperms[i].outer_x) * num;
                        //center.Y = (num4 + point.Y + sperms[i].outer_y) * num2;
                        SolidColorBrush color = (sperms[i].isNormal == 1) ? System.Windows.Media.Brushes.ForestGreen : ((sperms[i].isNormal == 2) ? System.Windows.Media.Brushes.Yellow : System.Windows.Media.Brushes.Red);
                        //未写完
                        //Drawing.addEllipse(center, sperms[i].outer_height * num2, sperms[i].outer_width * num, sperms[i].outer_angle, ellipName, grd, m_img_analysis, color, sperms[i], stroke);
                    }

                }
            }
        }
        public static void DrawPolygons(float p_x, float p_y, Graphics g, float width, float height, List<Model.Sperm> sperms)
        {
            bool flag = width != 0.0 && sperms.Count > 0;
            if (flag)
            {
                string imageSource = sperms[0].imageSource;
                //System.Windows.Point point = m_img_analysis.TranslatePoint(new System.Windows.Point(0.0, 0.0), grd);
                //System.Windows.Point point = new System.Windows.Point(0, 0);
                System.Drawing.PointF point = new PointF(p_x, p_y);
                for (int i = 0; i < sperms.Count; i++)
                {
                    bool valid = sperms[i].valid;
                    if (valid)
                    {
                        ObservableCollection<System.Drawing.Point[]> observableCollection = new ObservableCollection<System.Drawing.Point[]>();
                        observableCollection.Add(sperms[i].vop_acrosome.ToArray());
                        observableCollection.Add(sperms[i].vop_kernel.ToArray());
                        for (int j = 0; j < 2; j++)
                        {
                            System.Drawing.Point[] array = observableCollection[j];

                            bool flag3 = array != null;
                            if (array != null && array.Length != 0)
                            {
                                flag3 = true;
                            }
                            else
                            {
                                flag3 = false;
                            }
                            if (flag3)
                            {
                                System.Drawing.Point[] array2 = array;
                                System.Drawing.PointF[] points = new System.Drawing.PointF[array.Length];//多边形坐标点集合
                                double num;
                                double num2;
                                double num3;
                                double num4;
                                clsMrfSegment.getBox(sperms[i], (double)Basic.imageSize.Width, (double)Basic.imageSize.Height, out num, out num2, out num3, out num4);
                                // 记录第一个精子的坐标转换信息
                                if (i == 0 && j == 0 && array2.Length > 0)
                                {
                                    LogHelper.WriteErrLog($"原项目坐标转换: 精子{sperms[i].spermindex}, 偏移({num:F1},{num2:F1}), 显示尺寸({width:F1}x{height:F1}), 基准分辨率({Basic.imageSize.Width}x{Basic.imageSize.Height})");
                                    LogHelper.WriteErrLog($"原项目第一个轮廓点: 原始({array2[0].X},{array2[0].Y}), 画布偏移({point.X:F1},{point.Y:F1})");
                                }

                                for (int k = 0; k < array2.Count<System.Drawing.Point>(); k++)
                                {
                                    System.Drawing.PointF value = default(System.Drawing.PointF);
                                    value.X = (float)((num + (double)array2[k].X) * width / (double)Basic.imageSize.Width + point.X);
                                    value.Y = (float)((num2 + (double)array2[k].Y) * height / (double)Basic.imageSize.Height + point.Y);
                                    points[k] = value;

                                    // 记录第一个精子第一个轮廓点的转换结果
                                    if (i == 0 && j == 0 && k == 0)
                                    {
                                        LogHelper.WriteErrLog($"原项目转换结果: 第一个点({value.X:F1},{value.Y:F1})");
                                    }
                                    //polygon.Points.Add(value);
                                }
                                //(j == 0) ? System.Windows.Media.Brushes.Yellow : ((j == 1) ? System.Windows.Media.Brushes.Blue : System.Windows.Media.Brushes.White)
                                switch (j)
                                {
                                    case 0:
                                        g.DrawPolygon(System.Drawing.Pens.Yellow, points);
                                        break;
                                    case 1:
                                        g.DrawPolygon(System.Drawing.Pens.Blue, points);
                                        break;
                                    default:
                                        g.DrawPolygon(System.Drawing.Pens.White, points);
                                        break;
                                }

                                //polygon.MouseLeftButtonDown += Drawing.rectangle_MouseDown;
                                //polygon.Name = "poly_" + sperms[i].id;
                                //polygon.Cursor = Cursors.Hand;
                                //polygon.ContextMenu = Drawing.setContextMenu(sperms[i].id);
                                //grd.Children.Add(polygon);
                            }
                        }
                    }
                }
            }
        }

        public static void DrawVacuoles(float p_x, float p_y, Graphics gra, float width, float height, List<Model.Sperm> sperms)
        {
            bool flag = width != 0.0 && sperms.Count > 0;
            if (flag)
            {
                string imageSource = sperms[0].imageSource;
                System.Drawing.PointF point = new System.Drawing.PointF(p_x, p_y);
                for (int i = 0; i < sperms.Count; i++)
                {
                    bool valid = sperms[i].valid;
                    if (valid)
                    {
                        bool flag2 = sperms[i].vvop_vacuoles != null;
                        if (flag2)
                        {
                            List<System.Drawing.Point[]> vvop_vacuoles = sperms[i].vvop_vacuoles;
                            System.Drawing.PointF value = default(System.Drawing.PointF);
                            double num;
                            double num2;
                            double num3;
                            double num4;
                            clsMrfSegment.getBox(sperms[i], (float)Basic.imageSize.Width, (float)Basic.imageSize.Height, out num, out num2, out num3, out num4);

                            for (int j = 0; j < vvop_vacuoles.Count; j++)
                            {
                                string name = string.Concat(new string[]
                                {
                                    "vacuo_",
                                    sperms[i].photoId.ToString(),
                                    "_",
                                    sperms[i].id.ToString(),
                                    "_",
                                    j.ToString()
                                });
                                //bool flag3 = (Polygon)grd.FindName(name) == null;//应该是判断是否有绘制过多边形，已绘制就不绘制了。
                                bool flag3 = true;

                                if (flag3)
                                {
                                    System.Windows.Media.Brush darkRed = System.Windows.Media.Brushes.DarkRed;

                                    //gra.DrawPolygon(System.Drawing.Pens.DarkRed,)
                                    //                           Polygon polygon = new Polygon
                                    //{
                                    //                               StrokeThickness = stroke,
                                    //                               Stroke = darkRed
                                    //                           };
                                    System.Drawing.Point[] array = vvop_vacuoles[j];
                                    bool flag4 = array != null;
                                    if (flag4)
                                    {
                                        System.Drawing.Point[] array2 = array;
                                        System.Drawing.PointF[] points = new System.Drawing.PointF[array.Length];//多边形坐标点集合
                                        for (int k = 0; k < array2.Count<System.Drawing.Point>(); k++)
                                        {
                                            value.X = (float)((num + (float)array2[k].X) * width / (float)Basic.imageSize.Width + point.X);
                                            value.Y = (float)((num2 + (float)array2[k].Y) * height / (float)Basic.imageSize.Height + point.Y);
                                            points[k] = value;
                                            //polygon.Points.Add(value);
                                        }
                                        gra.DrawPolygon(System.Drawing.Pens.DarkRed, points);
                                        //polygon.MouseLeftButtonDown += Drawing.rectangle_MouseDown;
                                        //polygon.Cursor = Cursors.Hand;
                                        //polygon.Name = string.Concat(new object[]
                                        //{
                                        //    "vacu_",
                                        //    j,
                                        //    "_",
                                        //    sperms[i].id
                                        //});
                                        //polygon.ContextMenu = Drawing.setContextMenu(sperms[i].id);
                                        //grd.Children.Add(polygon);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        public static void DrawTails(float p_x, float p_y, Graphics gra, float width, float height, List<Model.Sperm> sperms)
        {
            bool flag = width != 0.0 && sperms.Count > 0;
            if (flag)
            {
                string imageSource = sperms[0].imageSource;
                //System.Windows.Point point = new System.Windows.Point(0, 0);
                System.Drawing.PointF point = new PointF(p_x, p_y);
                for (int i = 0; i < sperms.Count; i++)
                {

                    bool valid = sperms[i].valid;
                    if (valid)
                    {
                        bool flag2 = sperms[i].vop_middlepiece != null;
                        if (flag2)
                        {
                            System.Drawing.Point[] vop_middlepiece = sperms[i].vop_middlepiece.ToArray();

                            // 记录第一个精子的尾巴数据
                            if (sperms[i].spermindex == 1)
                            {
                                LogHelper.WriteErrLog($"原项目尾巴数据: 精子{sperms[i].spermindex}");
                                LogHelper.WriteErrLog($"  vop_middlepiece点数: {vop_middlepiece.Length}");
                                LogHelper.WriteErrLog($"  MDPnum: {sperms[i].MDPnum}");
                                LogHelper.WriteErrLog($"  偏移量: ({p_x:F1},{p_y:F1})");
                                LogHelper.WriteErrLog($"  图像尺寸: {width}x{height}");
                                LogHelper.WriteErrLog($"  Basic.imageSize: {Basic.imageSize.Width}x{Basic.imageSize.Height}");
                                if (vop_middlepiece.Length > 0)
                                {
                                    LogHelper.WriteErrLog($"  前3个点: ({vop_middlepiece[0].X},{vop_middlepiece[0].Y})");
                                    if (vop_middlepiece.Length > 1)
                                        LogHelper.WriteErrLog($"           ({vop_middlepiece[1].X},{vop_middlepiece[1].Y})");
                                    if (vop_middlepiece.Length > 2)
                                        LogHelper.WriteErrLog($"           ({vop_middlepiece[2].X},{vop_middlepiece[2].Y})");
                                }
                            }
                            Random random = new Random();
                            int num = random.Next(0, 20);
                            int num2 = random.Next(0, 128);
                            int num3 = random.Next(100, 200);
                            for (int j = 0; j < vop_middlepiece.Length - 1; j++)
                            {
                                int num4 = 2;
                                double opacity = 0.5;
                                bool flag3 = j < sperms[i].MDPnum;
                                System.Windows.Media.Brush stroke2;

                                var px1 = (float)(vop_middlepiece[j].X * width / Basic.imageSize.Width + point.X);
                                var py1 = (float)(vop_middlepiece[j].Y * width / Basic.imageSize.Width + point.Y);
                                var px2 = (float)(vop_middlepiece[j + 1].X * width / Basic.imageSize.Width + point.X);
                                var py2 = (float)(vop_middlepiece[j + 1].Y * width / Basic.imageSize.Width + point.Y);

                                if (flag3)
                                {

                                    // var stroke33 = new  SolidColorBrush(System.Windows.Media.Color.FromRgb(15, 187, 18));
                                    System.Drawing.Pen d = new System.Drawing.Pen(System.Drawing.Color.FromArgb(15, 187, 18));
                                    gra.DrawLine(d, px1, py1, px2, py2);

                                    num4 = 3;
                                    opacity = 0.8;
                                }
                                else
                                {
                                    //var stroke33 = new System.Drawing.Brush(System.Windows.Media.Color.FromRgb((byte)num, (byte)num2, (byte)num3));
                                    System.Drawing.Pen d = new System.Drawing.Pen(System.Drawing.Color.FromArgb((byte)num, (byte)num2, (byte)num3));
                                    gra.DrawLine(d, px1, py1, px2, py2);
                                }

                                //Line line = new Line
                                //{
                                //    StrokeThickness = (double)num4,
                                //    Opacity = opacity,
                                //    Stroke = stroke2
                                //};
                                //line.X1 = (double)vop_middlepiece[j].X * m_img_analysis.ActualWidth / (double)Basic.imageSize.Width + point.X;
                                //line.Y1 = (double)vop_middlepiece[j].Y * m_img_analysis.ActualHeight / (double)Basic.imageSize.Height + point.Y;
                                //line.X2 = (double)vop_middlepiece[j + 1].X * m_img_analysis.ActualWidth / (double)Basic.imageSize.Width + point.X;
                                //line.Y2 = (double)vop_middlepiece[j + 1].Y * m_img_analysis.ActualHeight / (double)Basic.imageSize.Height + point.Y;
                                //line.HorizontalAlignment = HorizontalAlignment.Left;
                                //line.VerticalAlignment = VerticalAlignment.Top;
                                //grd.Children.Add(line);
                            }
                        }
                    }
                    else { }
                }
            }

        }

        public static void DrawBoxs(float p_x, float p_y, Graphics gra, float width, float height, List<Model.Sperm> sperms)
        {
            System.Drawing.PointF point = new PointF(p_x, p_y);
            for (int i = 0; i < sperms.Count; i++)
            {
                bool flag2 = true;// item == "MRF";
                double num;
                double num2;
                double swidth;
                double sheight;
                clsMrfSegment.getBox(sperms[i], width, height, out num, out num2, out swidth, out sheight);

                Rectangle rect = new Rectangle((int)num, (int)num2, (int)swidth, (int)sheight);
                //Rect rect = new Rect(num + point.X, num2 + point.Y, swidth, sheight);
                string rtgName = "rtg_" + sperms[i].photoId.ToString() + "_" + sperms[i].id.ToString();
                string idName = "id_" + sperms[i].photoId.ToString() + "." + sperms[i].id.ToString();
                string id = sperms[i].fieldId.ToString("00") + "." + sperms[i].spermindex.ToString();
                System.Drawing.Brush color = (sperms[i].isNormal == 1) ? System.Drawing.Brushes.ForestGreen : ((sperms[i].isNormal == 2) ? System.Drawing.Brushes.Yellow : System.Drawing.Brushes.Red);
                SolidColorBrush lightYellow = System.Windows.Media.Brushes.LightYellow;
                gra.DrawString(id, new Font("黑体", 9), color, new PointF(p_x + (float)num + 2, p_y + (float)num2 + 2));
                bool valid = sperms[i].valid;
                //if (valid)
                //{
                //    Drawing.addBox(rect, rtgName, grdMap, color, sperms[i], stroke);
                //}
                //bool valid2 = sperms[i].valid;
                //if (valid2)
                //{
                //    Drawing.addIdtext(rect, idName, id, grdMap, m_img_analysis, color, fontsize, sperms[i]);
                //}
            }
        }

        public static void AddBox(Rectangle rect, SolidColorBrush color, Model.Sperm sperm)
        {

        }

        public static void AddIdText(Rectangle rect, string idName, string id, double width, double height, SolidColorBrush color, int fontsize, Model.Sperm sp)
        {

            //Label label = (Label)grd.FindName(idName);
            //bool flag = label == null;
            //if (flag)
            //{
            //    Label label2 = new Label();
            //    label2.Margin = new Thickness(rect.Left - 3.0, rect.Top - 4.0, m_img_analysis.ActualWidth - rect.Left - rect.Width, m_img_analysis.ActualHeight - rect.Top - rect.Height);
            //    label2.Width = rect.Width;
            //    label2.Height = rect.Height;
            //    label2.HorizontalAlignment = HorizontalAlignment.Left;
            //    label2.VerticalAlignment = VerticalAlignment.Top;
            //    label2.Foreground = color;
            //    label2.FontSize = (double)fontsize;
            //    label2.Cursor = Cursors.Hand;
            //    label2.Name = "lb_" + sp.id;
            //    label2.MouseLeftButtonDown += Drawing.rectangle_MouseDown;
            //    label2.ContextMenu = Drawing.setContextMenu(sp.id);
            //    label2.ToolTip = new ToolTip
            //    {
            //        Content = new TextBlock
            //        {
            //            Text = Drawing.GetToolTip(sp),
            //            TextWrapping = TextWrapping.Wrap
            //        }
            //    };
            //    label2.Content = id;
            //    grd.Children.Add(label2);
            //}
        }

        // Token: 0x020000CF RID: 207
        // (Invoke) Token: 0x06000A80 RID: 2688
        public delegate void HandleSelectedSpermChanged(long spermId);

        // Token: 0x020000D0 RID: 208
        // (Invoke) Token: 0x06000A84 RID: 2692
        public delegate void HandleDeletedSperm(long spermId);

        // Token: 0x020000D1 RID: 209
        // (Invoke) Token: 0x06000A88 RID: 2696
        public delegate void HandleSetNormalSperm(long spermId);

        // Token: 0x020000D2 RID: 210
        // (Invoke) Token: 0x06000A8C RID: 2700
        public delegate void HandleSetAbnormalSperm(long spermId);
    }
}
