===============================================
    精子形态分析系统 - MX450显卡用户使用说明
===============================================

您好！我们已经为您的NVIDIA GeForce MX450显卡优化了程序配置。

【问题描述】
在MX450等低端显卡上，精子形态分析计算完成后，点击"实验查询"时尾巴信息无法正常绘制，
这是由于GPU内存不足导致的TensorFlow模型运行失败。

【解决方案】
我们提供了两种启动方式：

方式1：使用优化启动器（推荐）
=====================================
双击运行：启动精子形态分析系统-MX450优化版.bat

这个启动器会：
✓ 自动检测您的GPU配置
✓ 设置适合MX450的内存限制
✓ 在启动前优化TensorFlow环境
✓ 显示详细的配置信息

方式2：直接启动程序
=====================================
双击运行：SpermFormAnalysis.exe

如果直接启动遇到问题，请使用方式1。

【配置文件说明】
=====================================
gpu_config.ini 文件包含以下设置：

ForceUseCPU=false          # 是否强制使用CPU
GPUMemoryOptimized=true    # 是否启用GPU内存优化  
GPUMemoryLimit=1400        # GPU内存限制(MB)
EnableTailProcessing=true  # 是否启用尾巴处理

【故障排除】
=====================================

问题1：尾巴信息仍然不显示
解决：修改gpu_config.ini，设置ForceUseCPU=true

问题2：程序运行很慢
原因：可能已切换到CPU模式，这是正常现象

问题3：出现内存不足错误
解决：降低GPUMemoryLimit值，如改为1200或1000

【不同情况的推荐设置】
=====================================

情况1：MX450 + 8GB系统内存
ForceUseCPU=false
GPUMemoryLimit=1400

情况2：MX450 + 4GB系统内存  
ForceUseCPU=true
GPUMemoryLimit=1000

情况3：经常出现错误
ForceUseCPU=true
GPUMemoryLimit=800

【日志文件】
=====================================
如果遇到问题，请查看Log文件夹中的日志文件：
Log\Log20250803.txt

关键信息：
- "GPU内存管理配置完成" - 配置成功
- "OOM when allocating tensor" - 内存不足
- "CPU模式运行成功" - 已切换到CPU模式

【技术支持】
=====================================
如果以上方法都无法解决问题，请提供：
1. 您的具体显卡型号和驱动版本
2. 系统内存大小
3. Log文件夹中的最新日志文件
4. gpu_config.ini文件内容

【更新记录】
=====================================
v1.0 - 初始版本，添加MX450显卡支持
v1.1 - 添加自动配置和启动器
v1.2 - 改进错误处理和用户体验

===============================================
祝您使用愉快！
===============================================
