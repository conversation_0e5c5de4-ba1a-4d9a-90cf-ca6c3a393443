﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Windows.Media.Media3D;
using NPOI.SS.Formula.Functions;
using SpermFormAnalysis.common;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.Model;
using SpermFormAnalysis.Segments;
using Sunny.UI;
using Sunny.UI.Win32;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace SpermFormAnalysis.PageForm
{
    public partial class CellDetail : UIForm
    {
        private int Curr_Pic_Index = -1;

        // 当前选中的小图的索引
        private int Curr_Detail_Index;

        // 页面模式： 全图：0；列表：1
        private int mode = 0;
        private string year_month;
        private Sampleinfo model;
        private List<Samplephoto> pictureInfos;
        private int curr_x;
        private int curr_y;
        private Point startPoint;  // 声明startPoint
        private Rectangle rect;  // 声明rect
        private CellDetail()
        {
            InitializeComponent();
            this.DoubleBuffered = true;
        }
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        public CellDetail(ref List<Samplephoto> pictureInfos, int p_index, string year_month, ref Sampleinfo model)
        {
            this.model = model;
            this.pictureInfos = pictureInfos;
            if (p_index >= pictureInfos.Count || p_index < 0)
            {
                this.Curr_Pic_Index = 0;
            }
            else
            {
                this.Curr_Pic_Index = p_index;
            }
            this.year_month = year_month;
            InitializeComponent();
            InitializeComponent2();
        }

        void InitializeComponent2()
        {
            //this.Cell_x.Style = Sunny.UI.UIStyle.LightGray;
            //this.Cell_Y.Style = Sunny.UI.UIStyle.LightGray;
            //this.Cell_Width.Style = Sunny.UI.UIStyle.LightGray;
            //this.Cell_Height.Style = Sunny.UI.UIStyle.LightGray;
            this.PreBtn.Style = Sunny.UI.UIStyle.Office2010Silver;
            this.NextBtn.Style = Sunny.UI.UIStyle.Office2010Silver;
        }

        private void ChangePic()
        {
            //if (pictureInfos[this.Curr_Pic_Index].detailsList == null)
            //{
            //    DataTable dt = dal.GetDataTable("select * from dfi_details" + this.year_month + " where p_id = " + pictureInfos[this.Curr_Pic_Index].Id + " and color >= 1", "dt");
            //    pictureInfos[this.Curr_Pic_Index].detailsList = new List<DFI_details>();
            //    foreach (DataRow row in dt.Rows)
            //    {
            //        pictureInfos[this.Curr_Pic_Index].detailsList.Add(new DFI_details()
            //        {
            //            id = Convert.ToInt32(row["id"].ToString()),
            //            x = Convert.ToInt32(row["x"].ToString()),
            //            y = Convert.ToInt32(row["y"].ToString()),
            //            width = Convert.ToInt32(row["width"].ToString()),
            //            height = Convert.ToInt32(row["height"].ToString()),
            //            color = Convert.ToInt32(row["color"].ToString()),
            //        });
            //    }
            //    dt.Dispose();
            //}

            //this.Red_count.Text = "红：" + pictureInfos[this.Curr_Pic_Index].RedNum;
            //this.Orange_count.Text = "橙：" + pictureInfos[this.Curr_Pic_Index].OrangeNum;
            //this.Yellow_count.Text = "黄：" + pictureInfos[this.Curr_Pic_Index].YellowNum;
            //this.YellowGreen_count.Text = "黄绿：" + pictureInfos[this.Curr_Pic_Index].KellyNum;
            //this.Green_count.Text = "绿：" + pictureInfos[this.Curr_Pic_Index].GreenNum;
            //this.ok_count.Text = "正常细胞数量：" + (pictureInfos[this.Curr_Pic_Index].GreenNum + pictureInfos[this.Curr_Pic_Index].KellyNum);
            //this.err_count.Text = "异常细胞数量：" + (pictureInfos[this.Curr_Pic_Index].RedNum + pictureInfos[this.Curr_Pic_Index].OrangeNum + pictureInfos[this.Curr_Pic_Index].YellowNum);
            //this.cell_total.Text = "细胞总数：" + (pictureInfos[this.Curr_Pic_Index].GreenNum + pictureInfos[this.Curr_Pic_Index].KellyNum +
            //    pictureInfos[this.Curr_Pic_Index].RedNum + pictureInfos[this.Curr_Pic_Index].OrangeNum + pictureInfos[this.Curr_Pic_Index].YellowNum);

            bool is_empty = pictureInfos.Count == 0;// || pictureInfos[this.Curr_Pic_Index].detailsList ==null || pictureInfos[this.Curr_Pic_Index].detailsList.Count == 0;
            this.PreBtn.Enabled = this.Curr_Pic_Index > 0;
            this.NextBtn.Enabled = this.Curr_Pic_Index < pictureInfos.Count - 1;
            this.Curr_Detail_Index = is_empty ? -1 : 0;
            if (!is_empty)
            {
                if (this.mode == 0)
                {
                    try
                    {
                        this.pictureInfos[Curr_Pic_Index].detailsList = SpermServices.GetObjects(this.pictureInfos[Curr_Pic_Index].id);
                        this.OriginalPicPanel.BackgroundImage = Image.FromFile(this.pictureInfos[Curr_Pic_Index].imageSource);

                        this.OriginalPicPanel.Invalidate();
                        ChangeCell();
                    }
                    catch (Exception e)
                    {
                        this.OriginalPicPanel.BackgroundImage = null;
                    }
                }
                else
                {

                }
            }

            this.OriginalPicPanel.Invalidate();
        }


        private void OriginalPicPanel_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;
            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighSpeed;

            this.pictureInfos[Curr_Pic_Index].detailsList = SpermServices.GetObjects(this.pictureInfos[Curr_Pic_Index].id);

            if (this.mode == 0)
            {
                var result = SpermServices.GetObjects(pictureInfos[this.Curr_Pic_Index].id).ToList();
                float Img_X = 0;
                float Img_Y = 0;
                Image img = Image.FromFile(pictureInfos[this.Curr_Pic_Index].imageSource);
                var ImgWidth = OriginalPicPanel.Width;
                var ImgHeight = OriginalPicPanel.Height;
                SpermFormAnalysis.common.Drawing.DrawTails(Img_X, Img_Y, g, ImgWidth, ImgHeight, result);//画尾巴
                SpermFormAnalysis.common.Drawing.DrawVacuoles(Img_X, Img_Y, g, ImgWidth, ImgHeight, result);//画空泡
                SpermFormAnalysis.common.Drawing.DrawPolygons(Img_X, Img_Y, g, ImgWidth, ImgHeight, result);//画精子
                SpermFormAnalysis.common.Drawing.DrawBoxs(Img_X, Img_Y, g, ImgWidth, ImgHeight, result);//画文字说明

                if (result.Count > 0 && result != null)
                {

                    for (int i = 0; i < result.Count; i++)
                    {
                        double num1;
                        double num2;
                        double width;
                        double height;
                        clsMrfSegment.getBox(result[i], ImgWidth, ImgHeight, out num1, out num2, out width, out height);
                        if (result[i].isWrite == 2)
                        {
                            var borderWidth = 1;
                            if (Curr_Detail_Index == i)
                            {
                                CurrSperm = pictureInfos[this.Curr_Pic_Index].detailsList[Curr_Detail_Index];
                                borderWidth = 4;
                            }
                            var rect = new Rectangle();
                            rect.X = (int)result[i].xmin;
                            rect.Y = (int)result[i].ymin;
                            rect.Width = (int)(result[i].xmax - result[i].xmin);
                            rect.Height = (int)(result[i].ymax - result[i].ymin);
                            if (result[i].isNormal == 1)
                            {
                                using (System.Drawing.Pen pen = new System.Drawing.Pen(System.Drawing.Color.Green, borderWidth))
                                {
                                    g.DrawRectangle(pen, rect);
                                }
                            }
                            else
                            {
                                using (System.Drawing.Pen pen = new System.Drawing.Pen(System.Drawing.Color.Red, borderWidth))
                                {
                                    g.DrawRectangle(pen, rect);
                                }
                            }
                        }
                        else
                        {
                            if (result[i].isNormal == 1)
                            {
                                g.DrawRectangle(Pens.Green, (float)(Img_X + num1), (float)(Img_Y + num2), (float)width, (float)height);
                            }
                            else
                            {
                                g.DrawRectangle(Pens.Red, (float)(Img_X + num1), (float)(Img_Y + num2), (float)width, (float)height);
                            }
                            if (this.Curr_Detail_Index != -1 /*&& pictureInfos[this.Curr_Pic_Index].detailsList.Count != 0*/&& Curr_Detail_Index == i)
                            {
                                g.DrawRectangle(result[i].isNormal == 1 ? new System.Drawing.Pen(System.Drawing.Color.Green, 4) : new System.Drawing.Pen(System.Drawing.Color.Red, 4)
                                    , (float)num1
                                    , (float)num2
                                    , (float)width
                                    , (float)height);
                            }
                        }
                    }

                }
                if (rect.X != 0 && rect.Y != 0)
                {
                    using (System.Drawing.Pen pen = new System.Drawing.Pen(System.Drawing.Color.Red, 2))
                    {
                        e.Graphics.DrawRectangle(pen, rect);
                    }
                }
            }
            else
            {

            }
        }

        private void OriginalPicPanel_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                // 选中
                int count = pictureInfos[Curr_Pic_Index].detailsList.Count;
                if (count == 0) return;
                var ImgWidth = OriginalPicPanel.Width;
                var ImgHeight = OriginalPicPanel.Height;
                for (int i = 0; i < count; i++)
                {
                    Sperm details = pictureInfos[Curr_Pic_Index].detailsList[i];
                    CurrSperm = pictureInfos[Curr_Pic_Index].detailsList[i];
                    if (details.isWrite != 2)
                    {
                        double num1;
                        double num2;
                        double width;
                        double height;
                        clsMrfSegment.getBox(details, ImgWidth, ImgHeight, out num1, out num2, out width, out height);
                        if (e.X > num1
                            && e.Y > num2
                            && e.X < (num1 + width)
                            && e.Y < (num2 + height)
                            )
                        {
                            if (this.Curr_Detail_Index == i) continue;
                            this.Curr_Detail_Index = i;
                            this.ChangeCell();
                            toolTip1.SetToolTip(OriginalPicPanel, common.Drawing.GetToolTip(details));
                            //OriginalPicPanel.too  Drawing.GetToolTip(details);
                            return;
                        }
                    }
                    else
                    {
                        if (e.X > details.xmin
                            && e.Y > details.ymin
                            && e.X < details.xmax
                            && e.Y < details.ymax
                            )
                        {
                            if (this.Curr_Detail_Index == i) continue;
                            this.Curr_Detail_Index = i;

                            this.ChangeCell();
                            toolTip1.SetToolTip(OriginalPicPanel, common.Drawing.GetToolTip(details));
                            //OriginalPicPanel.too  Drawing.GetToolTip(details);
                            return;
                        }
                    }

                }
            }
            else if (e.Button == MouseButtons.Right)
            {
                int count = pictureInfos[Curr_Pic_Index].detailsList.Count;
                Sperm tmp_detail = null;
                if (count != 0)
                {
                    for (int i = 0; i < count; i++)
                    {
                        Sperm details = pictureInfos[Curr_Pic_Index].detailsList[i];
                        CurrSperm = pictureInfos[Curr_Pic_Index].detailsList[i];
                        double num1;
                        double num2;
                        double width;
                        double height;
                        var ImgWidth = OriginalPicPanel.Width;
                        var ImgHeight = OriginalPicPanel.Height;
                        clsMrfSegment.getBox(details, ImgWidth, ImgHeight, out num1, out num2, out width, out height);

                        if (e.X > num1 * 0.7F
                            && e.Y > num2 * 0.7F
                            && e.X < (num1 + width) * 0.7F
                            && e.Y < (num2 + height) * 0.7F)
                        {
                            if (this.Curr_Detail_Index != i)
                            {
                                this.Curr_Detail_Index = i;
                                this.ChangeCell();
                                toolTip1.SetToolTip(OriginalPicPanel, common.Drawing.GetToolTip(details));
                                // 修改 （设为正常、设为异常、删除）
                                //uiContextMenuStrip1.Items[0].Text = details.isNormal == 0 ? "设为异常" : "设为正常";
                                //uiContextMenuStrip1.Items[1].Text = "删除";
                                this.OriginalPicPanel.ShowContextMenuStrip(uiContextMenuStrip1, (int)((num1 + width) * 0.7), (int)((num2 + height) * 0.7));
                                return;
                            }
                            else
                            {
                                tmp_detail = details;
                                continue;
                            }
                        }
                    }
                }
                if (tmp_detail != null)
                {
                    double num1;
                    double num2;
                    double width;
                    double height;
                    var ImgWidth = OriginalPicPanel.Width;
                    var ImgHeight = OriginalPicPanel.Height;
                    clsMrfSegment.getBox(tmp_detail, ImgWidth, ImgHeight, out num1, out num2, out width, out height);
                    //uiContextMenuStrip1.Items[0].Text = tmp_detail.isNormal == 0 ? "设为异常" : "设为正常";
                    //uiContextMenuStrip1.Items[1].Text = "删除";
                    //this.OriginalPicPanel.ShowContextMenuStrip(uiContextMenuStrip1, (int)((num1 + width) * 0.7), (int)((num2 + height) * 0.7));
                    return;
                }
                else
                {
                    // 添加 （添加正常、添加异常）
                    this.curr_x = e.X;
                    this.curr_y = e.Y;
                    //uiContextMenuStrip1.Items[0].Text = "添加正常";
                    //uiContextMenuStrip1.Items[1].Text = "添加异常";
                    //this.OriginalPicPanel.ShowContextMenuStrip(uiContextMenuStrip1, e.X, e.Y);
                    return;
                }
            }
        }

        bool is_user_operate = true;
        private void ChangeCell()
        {
            if (this.Curr_Detail_Index < 0) return;
            Sperm sperm = null;
            if (pictureInfos[this.Curr_Pic_Index].detailsList == null || pictureInfos[this.Curr_Pic_Index].detailsList.Count <= 0)
            {
                Cell_pic.Invalidate();
                return;
            }
            else
            {
                sperm = pictureInfos[this.Curr_Pic_Index].detailsList[this.Curr_Detail_Index];
            }

            is_user_operate = false;
            //this.Cell_x.Value = details.x;
            //this.Cell_Y.Value = details.y;
            //this.Cell_Width.Value = details.width;
            //this.Cell_Height.Value = details.height;
            //this.Cell_Color.SelectedIndex = details.color - 1;
            is_user_operate = true;
            this.OriginalPicPanel.Invalidate();
            this.Cell_pic.Invalidate();

            double acrosomeRatio_val = Math.Round(sperm.acrosomeRatio * 100, 2);
            //sperm_type.Text = sperm.isNormal == 0 ? "异常" : "正常";
            if (sperm.isNormal == 0)
            {
                radisNormal_abNormal.Checked = true;
                radisNormal_Normal.Checked = false;
            }
            else
            {
                radisNormal_Normal.Checked = true;
                radisNormal_abNormal.Checked = false;
            }
            //用来保存和修改的
            CurrSperm = sperm;
            isNormal = sperm.isNormal;
            shapeType = sperm.shapeType;
            acrosomeType = sperm.acrosomeType;
            kernelType = sperm.kernelType;
            middlePieceType = sperm.middlePieceType;
            rabtailTypes = sperm.middlePieceType;


            ///顶体
            rabacrosome1.Checked = ((sperm.acrosomeType & 1) > 0) ? true : false;
            rabacrosome2.Checked = ((sperm.acrosomeType & 2) > 0) ? true : false;
            rabacrosome4.Checked = ((sperm.acrosomeType & 4) > 0) ? true : false;
            rabacrosome8.Checked = ((sperm.acrosomeType & 8) > 0) ? true : false;
            rabacrosome16.Checked = ((sperm.acrosomeType & 16) > 0) ? true : false;
            rabacrosome64.Checked = ((sperm.acrosomeType & 32) > 0) ? true : false;
            bool acrotype_flag = (sperm.acrosomeType & 1) <= 0 && (sperm.acrosomeType & 2) <= 0 && (sperm.acrosomeType & 4) <= 0 && (sperm.acrosomeType & 8) <= 0 && (sperm.acrosomeType & 16) <= 0 && (sperm.acrosomeType & 32) <= 0;
            if (acrotype_flag)
            {
                rabacrosome.Checked = true;
                acrosomeType = 0;
                rab_acronormal.Checked = true;
                rab_acroabnormal.Checked = false;
            }
            else
            {
                rab_acroabnormal.Checked = true;
                rab_acronormal.Checked = false;
            }


            rabheadshape1.Checked = ((sperm.shapeType & 1) > 0) ? true : false;
            rabheadshape4.Checked = ((sperm.shapeType & 4) > 0) ? true : false;
            rabheadshape8.Checked = ((sperm.shapeType & 8) > 0) ? true : false;
            rabheadshape16.Checked = ((sperm.shapeType & 16) > 0) ? true : false;
            rabheadshape32.Checked = ((sperm.shapeType & 32) > 0) ? true : false;
            rabheadshape64.Checked = ((sperm.shapeType & 64) > 0) ? true : false;
            ///形状
            bool shapeType_flag = (sperm.shapeType & 1) <= 0 && (sperm.shapeType & 4) <= 0 && (sperm.shapeType & 8) <= 0 && (sperm.shapeType & 16) <= 0 && (sperm.shapeType & 32) <= 0 && (sperm.shapeType & 64) <= 0 && (sperm.shapeType & 4096) <= 0;
            if (shapeType_flag)
            {
                rab_headshape.Checked = true;
                shapeType = 0;
                rabbigsmall_nomal.Checked = true;
                rad_headtypenormal.Checked = true;
                rab_headtypeabnormal.Checked = false;
            }
            else
            {
                rab_headtypeabnormal.Checked = true;
                rad_headtypenormal.Checked = false;
                rabbigsmall_nomal.Checked = false;
            }

            ///核区
            chkkernelType8.Checked = ((sperm.kernelType & 8) > 0) ? true : false;
            nuclearVacuole = ((sperm.kernelType & 8) > 0) ? true : false;
            chkkernelType16.Checked = ((sperm.kernelType & 16) > 0) ? true : false;
            uneven = ((sperm.kernelType & 16) > 0) ? true : false;
            bool kerneltype_flag = (sperm.kernelType & 8) <= 0 && (sperm.kernelType & 16) <= 0;
            if (kerneltype_flag)
            {
                rabkerneltype_nomal.Checked = true;
                kernelType = 0;
                rabkerneltype_abnomal.Checked = false;
            }
            else
            {
                rabkerneltype_abnomal.Checked = true;
                rabkerneltype_nomal.Checked = false;
            }


            //中段
            chkmiddletype1.Checked = ((sperm.middlePieceType & 1) > 0) ? true : false;
            insertException = ((sperm.middlePieceType & 1) > 0) ? true : false;
            chkmiddleType2.Checked = ((sperm.middlePieceType & 2) > 0) ? true : false;
            bulky = ((sperm.middlePieceType & 2) > 0) ? true : false;
            chkmiddleType4.Checked = ((sperm.middlePieceType & 4) > 0) ? true : false;
            residualCapsule = ((sperm.middlePieceType & 4) > 0) ? true : false;

            bool middletype_flag = (sperm.middlePieceType & 1) <= 0 && (sperm.middlePieceType & 2) <= 0 && (sperm.middlePieceType & 4) <= 0;
            if (middletype_flag)
            {
                rabmiddlePieceType_nomal.Checked = true;
                middlePieceType = 0;
                rabmiddlePieceType_abnomal.Checked = false;
            }
            else
            {
                rabmiddlePieceType_abnomal.Checked = true;
                rabmiddlePieceType_nomal.Checked = false;
            }

            //尾段
            rabtailType8.Checked = ((sperm.middlePieceType & 8) > 0) ? true : false;
            rabtailType16.Checked = ((sperm.middlePieceType & 16) > 0) ? true : false;
            rabtailType32.Checked = ((sperm.middlePieceType & 32) > 0) ? true : false;
            rabtailType64.Checked = ((sperm.middlePieceType & 64) > 0) ? true : false;
            bool tailType_flag = (sperm.middlePieceType & 8) <= 0 && (sperm.middlePieceType & 16) <= 0 && (sperm.middlePieceType & 32) <= 0 && (sperm.middlePieceType & 64) <= 0;
            if (tailType_flag)
            {
                rabtailType_nomal.Checked = true;
                rabtailTypes = 0;
                rabtailType_abnomal.Checked = false;
                rabtailType.Checked = true;
            }
            else
            {
                rabtailType_abnomal.Checked = true;
                rabtailType_nomal.Checked = false;
            }

            txtlongAxis.Text = Math.Round(sperm.longAxis, 2).ToString();//高度
            txtshortAxis.Text = Math.Round(sperm.shortAxis, 2).ToString();//宽度
            txtellipsRatio.Text = Math.Round(sperm.ellipsRatio, 2).ToString();//高宽比
            txtacrosomeRatio.Text = Math.Round(sperm.acrosomeRatio, 2).ToString();//顶体比率
            txtellipseCV.Text = Math.Round(sperm.ellipseCV, 2).ToString();//椭圆拟合率
            txtgirthCV.Text = Math.Round(sperm.girthCV, 2).ToString();//周长拟合率
            txtSymmetryRatio.Text = Math.Round(sperm.SymmetryRatio, 2).ToString();//短轴对称度
            txtSymmetryRatioLongAxis.Text = Math.Round(sperm.SymmetryRatioLongAxis, 2).ToString();////长轴对称度
            txtacrosomeVacuolesNum.Text = sperm.acrosomeVacuolesNum.ToString();//空泡数
            txtkernelVacuolesNum.Text = sperm.kernelVacuolesNum.ToString();//核区空泡数
            txtacrosome_uniformity.Text = Math.Round(sperm.acrosome_uniformity).ToString();//顶体均匀度
            txtkernel_uniformity.Text = Math.Round(sperm.kernel_uniformity).ToString();//核区均匀度
            txtmiddlepieceAngle.Text = Math.Round(sperm.middlepieceAngle).ToString();//中段插入角
            txtmiddlepieceWidth.Text = Math.Round(sperm.middlepieceWidth).ToString();//中段宽度
            txttailLength.Text = Math.Round(sperm.tailLength).ToString();//尾部长度
            txttailAngle.Text = Math.Round(sperm.tailAngle, 2).ToString(); //尾部折弯角
            txtERCArea.Text = Math.Round(sperm.ERCArea, 2).ToString(); //ERC面积
            txtERCWidth.Text = Math.Round(sperm.ERCWidth, 2).ToString(); //ERC宽度
        }

        private void Cell_pic_Paint(object sender, PaintEventArgs e)
        {
            
            if (this.Curr_Detail_Index == -1 || this.pictureInfos[this.Curr_Pic_Index].detailsList == null || OriginalPicPanel.BackgroundImage == null) return;
            Graphics g = e.Graphics;
            int pic_width = this.Cell_pic.Width;
            int pic_height = this.Cell_pic.Height;
            Sperm details = null;
            if (pictureInfos[this.Curr_Pic_Index].detailsList == null || pictureInfos[this.Curr_Pic_Index].detailsList.Count <= 0)
            {
                Cell_pic.Image = null;
                return;
            }
            else
            {
                details = pictureInfos[this.Curr_Pic_Index].detailsList[this.Curr_Detail_Index];
            }
            //float Img_X = 0;
            //float Img_Y = 0;
            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
            Image img = Image.FromFile(pictureInfos[this.Curr_Pic_Index].imageSource);
            var ImgWidth = img.Width;
            var ImgHeight = img.Height;
            double num1;
            double num2;
            double swidth;
            double sheight;
            clsMrfSegment.getBox(details, ImgWidth, ImgHeight, out num1, out num2, out swidth, out sheight);
            if(details.isWrite==2)
            {
                double originalWidth = 1344;
                double originalHeight = 840;
                // 计算宽度和高度的比例
                double widthRatio = ImgWidth / originalWidth;
                double heightRatio = ImgHeight / originalHeight;
                var rect = new Rectangle();
                rect.X = (int)(details.xmin * widthRatio) ;
                rect.Y = (int)(details.ymin * heightRatio) ;
                rect.Width = (int)(((details.xmax - details.xmin)) * widthRatio);
                rect.Height = (int)((details.ymax - details.ymin) * heightRatio);
                if (Width >= Height)
                {
                    g.DrawImage(this.OriginalPicPanel.BackgroundImage, new Rectangle(0, 0, pic_width, pic_height), rect, GraphicsUnit.Pixel);
                }
                else
                {
                    int width = (int)(pic_width * Width / Height);
                    g.DrawImage(this.OriginalPicPanel.BackgroundImage, new Rectangle((pic_width - width) / 2, 0, pic_width-100, pic_height), rect, GraphicsUnit.Pixel);

                }
            }
            else
            {
                if (swidth >= sheight)
                {
                    int height = (int)(1.0 * pic_width * sheight / swidth);
                    g.DrawImage(this.OriginalPicPanel.BackgroundImage,
                        new Rectangle(0, /*(pic_width - height) / 2*/0, pic_width, pic_height),
                        (int)num1, (int)num2, (int)swidth, (int)sheight, GraphicsUnit.Pixel);
                }
                else
                {
                    int width = (int)(pic_width * swidth / sheight);
                    g.DrawImage(this.OriginalPicPanel.BackgroundImage,
                        new Rectangle((pic_width - width) / 2, 0, pic_width - 100, pic_height),
                        (int)num1, (int)num2, (int)swidth, (int)sheight, GraphicsUnit.Pixel);
                }
            }
        }

        private void CellDetail_Load(object sender, EventArgs e)
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
            this.MaximizeBox = false;                                     //禁用"最大化"按钮
            this.FormBorderStyle = FormBorderStyle.FixedDialog;           //设置窗体边框样式为对话框样式

            this.ChangePic();
        }

        private void PreBtn_Click(object sender, EventArgs e)
        {
            if (this.Curr_Pic_Index <= 0) return;
            this.Curr_Pic_Index--;
            ChangePic();
        }

        private void NextBtn_Click(object sender, EventArgs e)
        {
            if (this.Curr_Pic_Index >= pictureInfos.Count - 1) return;
            this.Curr_Pic_Index++;
            ChangePic();
        }

        Sperm CurrSperm = null;
        int isNormal = 0;
        int shapeType = 0;
        int acrosomeType = 0;
        int kernelType = 0;
        int middlePieceType = 0;
        int rabtailTypes = 0;

        private void btnsave_MouseClick(object sender, MouseEventArgs e)
        {
            int count = pictureInfos[Curr_Pic_Index].detailsList.Count;
            if (CurrSperm == null)
            {
                ShowErrorNotifier("请先选择一个精子！");
                return;
            }


        }

        private void btnsave_Click(object sender, EventArgs e)
        {
            rab_headshape_CheckedChanged(sender, e);//计算头部
            rabacrosome_CheckedChanged(sender, e);//计算顶体
            nuclearVacuole = false;
            uneven = false;
            chkkernelType8_Click(sender, e);//计算核区
            insertException = false;
            bulky = false;
            residualCapsule = false;
            chkmiddletype1_MouseClick(sender, null);//
            rabtailType_ValueChanged(sender, true);

            CurrSperm.isNormal = isNormal;
            CurrSperm.shapeType = shapeType;
            CurrSperm.acrosomeType = acrosomeType;
            CurrSperm.kernelType = kernelType;
            CurrSperm.middlePieceType = middlePieceType;

            bool flag2 = SpermServices.UpdateHandlerSperm(CurrSperm);

            flag2 = (flag2 && SamplephotoServices.UpdatePhotoProcessed(this.pictureInfos[Curr_Pic_Index], 1));
            flag2 = (flag2 && SampleinfoServices.UpdateSampleProcessed(this.model, 2));

            OriginalPicPanel.Invalidate();

            ShowSuccessNotifier("精子修改成功");
        }

        public void loadSpermImg()
        {

        }

        private void rab_headshape_CheckedChanged(object sender, EventArgs e)
        {
            shapeType = 0;
            if (rab_headshape.Checked)
            {

            }
            else if (rabheadshape32.Checked)
            {
                shapeType += 32;
            }
            else if (rabheadshape1.Checked)
            {
                shapeType += 1;
            }
            else if (rabheadshape16.Checked)
            {
                shapeType += 16;
            }
            else if (rabheadshape64.Checked)
            {
                shapeType += 64;
            }
            else if (rabheadshape4096.Checked)
            {
                shapeType += 4096;
            }
            else
            {

            }

            if (rabbigsmall_nomal.Checked)
            {

            }
            else if (rabheadshape8.Checked)
            {
                shapeType += 8;
            }
            else if (rabheadshape4.Checked)
            {
                shapeType += 4;
            }
            else
            {

            }
            if (shapeType == 0)
            {
                rad_headtypenormal.Checked = true;
                rab_headtypeabnormal.Checked = false;
            }
            else
            {
                rab_headtypeabnormal.Checked = true;
                rad_headtypenormal.Checked = false;
            }
            if (shapeType == 0 && acrosomeType == 0 && kernelType == 0 && middlePieceType == 0 && rabtailTypes == 0)
            {
                radisNormal_Normal.Checked = true;
                radisNormal_abNormal.Checked = false;
                isNormal = 1;
            }
            else
            {
                radisNormal_abNormal.Checked = true;
                radisNormal_Normal.Checked = false;
                isNormal = 0;
            }


        }

        private void rabacrosome_CheckedChanged(object sender, EventArgs e)
        {
            acrosomeType = 0;
            if (rabacrosome8.Checked)
            {
                acrosomeType += 8;
            }
            if (rabacrosome64.Checked)
            {
                acrosomeType += 64;
            }
            if (rabacrosome16.Checked)
            {
                acrosomeType += 16;
            }

            if (rabacrosome.Checked)
            {
                if (acrosomeType == 0)
                {
                    rab_acronormal.Checked = true;
                    rab_acroabnormal.Checked = false;
                }
                else
                {
                    rab_acroabnormal.Checked = true;
                    rab_acronormal.Checked = false;
                }
            }
            else if (rabacrosome2.Checked)
            {
                rab_acroabnormal.Checked = true;
                acrosomeType += 2;
            }
            else if (rabacrosome1.Checked)
            {
                rab_acroabnormal.Checked = true;
                acrosomeType += 1;
            }
            else if (rabacrosome4.Checked)
            {
                rab_acroabnormal.Checked = true;
                acrosomeType += 4;
            }
            else
            {
                if (acrosomeType == 0)
                {
                    rab_acronormal.Checked = true;
                    rab_acroabnormal.Checked = false;
                }
                else
                {
                    rab_acroabnormal.Checked = true;
                    rab_acronormal.Checked = false;
                }
            }

            if (acrosomeType == 0)
            {
                rab_acronormal.Checked = true;
                rab_acroabnormal.Checked = false;
            }
            else
            {
                rab_acroabnormal.Checked = true;
                rab_acronormal.Checked = false;
            }
            if (shapeType == 0 && acrosomeType == 0 && kernelType == 0 && middlePieceType == 0 && rabtailTypes == 0)
            {
                radisNormal_Normal.Checked = true;
                radisNormal_abNormal.Checked = false;
                isNormal = 1;
            }
            else
            {
                radisNormal_abNormal.Checked = true;
                radisNormal_Normal.Checked = false;
                isNormal = 0;
            }
        }

        private bool nuclearVacuole = false;
        private bool uneven = false;
        private void chkkernelType8_Click(object sender, EventArgs e)
        {
            kernelType = 0;
            if (chkkernelType8.Checked)
            {
                if (nuclearVacuole)
                {
                    nuclearVacuole = false;
                    uneven = false;
                    chkkernelType8.Checked = false;
                }
                if (!nuclearVacuole && chkkernelType8.Checked)
                {
                    nuclearVacuole = true;
                    uneven = false;
                    kernelType += 8;
                }

            }
            if (chkkernelType16.Checked)
            {
                if (uneven)
                {
                    uneven = false;
                    nuclearVacuole = false;
                    chkkernelType16.Checked = false;
                }
                if (!uneven && chkkernelType16.Checked)
                {
                    uneven = true;
                    nuclearVacuole = false;
                    kernelType += 16;
                }
            }
            if (!chkkernelType8.Checked && !chkkernelType16.Checked)
            {
                kernelType = 0;
            }

            if (kernelType == 0)
            {
                rabkerneltype_nomal.Checked = true;
                rabkerneltype_abnomal.Checked = false;
            }
            else
            {
                rabkerneltype_abnomal.Checked = true;
                rabkerneltype_nomal.Checked = false;
            }
            if (shapeType == 0 && acrosomeType == 0 && kernelType == 0 && middlePieceType == 0 && rabtailTypes == 0)
            {
                radisNormal_Normal.Checked = true;
                radisNormal_abNormal.Checked = false;
                isNormal = 1;
            }
            else
            {
                radisNormal_abNormal.Checked = true;
                radisNormal_Normal.Checked = false;
                isNormal = 0;
            }
        }
        private void rabtailType_ValueChanged(object sender, bool value)
        {
            rabtailTypes = 0;
            if (rabtailType.Checked)
            {
                rabtailType_nomal.Checked = true;
                rabtailType_abnomal.Checked = false;
            }
            else if (rabtailType8.Checked)
            {
                rabtailTypes += 8;
                middlePieceType += 8;
                rabtailType_abnomal.Checked = true;
                rabtailType_nomal.Checked = false;
            }
            else if (rabtailType16.Checked)
            {
                rabtailTypes += 16;
                middlePieceType += 16;
                rabtailType_abnomal.Checked = true;
                rabtailType_nomal.Checked = false;
            }
            else if (rabtailType32.Checked)
            {
                rabtailTypes += 32;
                middlePieceType += 32;
                rabtailType_abnomal.Checked = true;
                rabtailType_nomal.Checked = false;
            }
            else if (rabtailType64.Checked)
            {
                rabtailTypes += 64;
                middlePieceType += 64;
                rabtailType_abnomal.Checked = true;
                rabtailType_nomal.Checked = false;
            }
            if (shapeType == 0 && acrosomeType == 0 && kernelType == 0 && middlePieceType == 0 && rabtailTypes == 0)
            {
                radisNormal_Normal.Checked = true;
                radisNormal_abNormal.Checked = false;
                isNormal = 1;
            }
            else
            {
                radisNormal_abNormal.Checked = true;
                radisNormal_Normal.Checked = false;
                isNormal = 0;
            }
        }

        private void rabtailType_CheckedChanged(object sender, EventArgs e)
        {
            //tailType = 0;
            rabtailType_ValueChanged(sender, true);
        }

        private bool insertException = false;
        private bool bulky = false;
        private bool residualCapsule = false;

        private void chkmiddletype1_MouseClick(object sender, MouseEventArgs e)
        {
            middlePieceType = 0;
            if (chkmiddletype1.Checked)
            {
                if (insertException)
                {
                    insertException = false;
                    bulky = false;
                    residualCapsule = false;
                    chkmiddletype1.Checked = false;
                    middlePieceType = 0;
                }
                if (!insertException && chkmiddletype1.Checked)
                {
                    insertException = true;
                    bulky = false;
                    residualCapsule = false;
                    middlePieceType += 1;
                }
            }
            if (chkmiddleType2.Checked)
            {
                if (bulky)
                {
                    bulky = false;
                    residualCapsule = false;
                    insertException = false;
                    chkmiddleType2.Checked = false;
                    middlePieceType = 0;
                }
                if (!bulky && chkmiddleType2.Checked)
                {
                    bulky = true;
                    residualCapsule = false;
                    insertException = false;
                    middlePieceType += 2;
                }
            }
            if (chkmiddleType4.Checked)
            {
                if (residualCapsule)
                {
                    residualCapsule = false;
                    bulky = false;
                    insertException = false;
                    chkmiddleType4.Checked = false;
                    middlePieceType = 0;
                }
                if (!residualCapsule && chkmiddleType4.Checked)
                {
                    residualCapsule = true;
                    bulky = false;
                    insertException = false;
                    middlePieceType += 4;
                }
            }


            if (!chkmiddletype1.Checked && !chkmiddleType2.Checked && !chkmiddleType4.Checked)
            {
                rabmiddlePieceType_nomal.Checked = true;
                rabmiddlePieceType_abnomal.Checked = false;
            }
            else
            {
                rabmiddlePieceType_abnomal.Checked = true;
                rabmiddlePieceType_nomal.Checked = false;
            }

            if (shapeType == 0 && acrosomeType == 0 && kernelType == 0 && middlePieceType == 0 && rabtailTypes == 0)
            {
                radisNormal_Normal.Checked = true;
                radisNormal_abNormal.Checked = false;
                isNormal = 1;
            }
            else
            {
                radisNormal_abNormal.Checked = true;
                radisNormal_Normal.Checked = false;
                isNormal = 0;
            }
        }

        private void uiImageButton1_Click(object sender, EventArgs e)
        {
            if (!ShowAskDialog("确定删除？")) return;

            if (SpermServices.DelOneSperm(CurrSperm.id))
            {
                SampleinfoServices.UpdateSampleProcessed(this.model, 2);

                OriginalPicPanel.Invalidate();

                ShowSuccessNotifier("精子已删除！");
            }

        }
        /// <summary>
        /// 鼠标右键下按
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OriginalPicPanel_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                startPoint = e.Location;
                rect = new Rectangle(e.X, e.Y, 0, 0);
            }
        }

        private void OriginalPicPanel_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                rect = new Rectangle(startPoint.X, startPoint.Y, e.X - startPoint.X, e.Y - startPoint.Y);
                this.OriginalPicPanel.Invalidate();
            }
        }

        private void OriginalPicPanel_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                var save = ShowAskDialog("是否保存框选精子？", false);
                if (save)
                {
                    Sperm sperm = new Sperm();
                    sperm.sampleId = pictureInfos[this.Curr_Pic_Index].sampleId;
                    sperm.photoId = pictureInfos[this.Curr_Pic_Index].id;
                    sperm.imageSource = pictureInfos[this.Curr_Pic_Index].imageSource;
                    sperm.xmin = rect.X;
                    sperm.xmax = rect.X + rect.Width;
                    sperm.ymin = rect.Y;
                    sperm.ymax = rect.Y + rect.Height;
                    sperm.isWrite = 2;
                    sperm.isNormal = 0;
                    sperm.valid = true;
                    SpermServices.CreateObject(sperm);
                    rect = new Rectangle();

                    SampleinfoServices.UpdateSampleProcessed(this.model, 2);

                    this.OriginalPicPanel.Invalidate();
                    this.Cell_pic.Invalidate();
                }
                else
                {
                    rect = new Rectangle();
                    this.OriginalPicPanel.Invalidate();
                    this.Cell_pic.Invalidate();
                }
            }
        }
    }
}
