﻿using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.Model;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.PageForm
{
    public partial class UserManage : UIPage
    {
        public static Admin admin = new Admin();
        public static int flag = 0;//0表示添加   1表示编辑
        public UserManage()
        {
            InitializeComponent();
            dataGrid_Admin.AutoGenerateColumns = false;
            dataGrid_Admin.DataSource = AdminServices.GetObjects();
        }
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        private void addBtn_Click(object sender, EventArgs e)
        {
            flag = 0;
            AddUser u = new AddUser();
            u.ShowDialog();
            if (u.IsOK)
            {
                //检查是否有重复用户名添加
                var ad = AdminServices.GetAdmin(admin.Admin_ID);
                if (ad != null)
                {
                    ShowErrorDialog("已存在该用户，不可添加！");
                    return;
                }
                //添加
                AdminServices.CreateObject(admin);
                dataGrid_Admin.DataSource = AdminServices.GetObjects();
                ShowSuccessDialog("添加成功！");//做一些善后处理
            }
        }

        private void editBtn_Click(object sender, EventArgs e)
        {
            if (this.dataGrid_Admin.SelectedRows.Count <= 0)
            {
                ShowErrorDialog("请先选择要编辑的用户!");
                return;
            }
            flag = 1;
            admin = this.dataGrid_Admin.CurrentRow.DataBoundItem as Admin;
            AddUser u = new AddUser();
            u.ShowDialog();

            string admin_id = admin.Admin_ID;
            if (u.IsOK)
            {
                if (admin_id.Equals("Admin"))
                {
                    if (!admin.Admin_ID.Equals("Admin"))
                    {
                        ShowErrorDialog("Admin为默认超级管理员账户，不可修改!");
                        return;
                    }
                    if (admin.Admin_Level.Equals("普通操作员"))
                    {
                        ShowErrorDialog("Admin为默认超级管理员账户，不可修改权限!");
                        return;
                    }
                }
                AdminServices.UpdateObject(admin);
                dataGrid_Admin.DataSource = AdminServices.GetObjects();
                ShowSuccessDialog("修改成功！");//做一些善后处理
            }
        }

        private void delBtn_Click(object sender, EventArgs e)
        {
            if (this.dataGrid_Admin.SelectedRows.Count <= 0)
            {
                ShowErrorDialog("请先选择要删除的操作员!");
                return;
            }
            if (ShowAskDialog("确实删除该用户吗?"))
            {
                String Admin_ID = this.dataGrid_Admin.CurrentRow.Cells["Admin_ID"].Value.ToString();
                if (Admin_ID.Equals("Admin"))
                {
                    ShowErrorDialog("不能删除默认管理员账户！");
                    return;
                }
                AdminServices.DeleteObject(Admin_ID);
                ShowSuccessNotifier("删除成功！");
                dataGrid_Admin.DataSource = AdminServices.GetObjects();
            }
            else
            {
                return;
            }
        }

        private void dataGrid_Admin_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e == null || e.Value == null || !(sender is DataGridView))
                return;
            DataGridView view = (DataGridView)sender;
            object originalValue = e.Value;
            if (view.Columns[e.ColumnIndex].DataPropertyName == "Admin_Level")
            {
                int Admin_Level = Convert.ToInt32(originalValue);
                if (Admin_Level > 0)
                {
                    e.Value = "普通操作员";
                }
                else
                {
                    e.Value = "超级操作员";
                }
            }
        }
    }
}
