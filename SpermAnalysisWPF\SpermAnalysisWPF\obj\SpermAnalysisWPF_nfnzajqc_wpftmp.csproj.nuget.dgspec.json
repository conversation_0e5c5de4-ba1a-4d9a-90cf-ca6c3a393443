{"format": 1, "restore": {"D:\\code\\SpermFormAnalysis-界面优化版本0802\\SpermAnalysisWPF\\SpermAnalysisWPF\\SpermAnalysisWPF.csproj": {}}, "projects": {"D:\\code\\SpermFormAnalysis-界面优化版本0802\\SpermAnalysisWPF\\SpermAnalysisWPF\\SpermAnalysisWPF.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code\\SpermFormAnalysis-界面优化版本0802\\SpermAnalysisWPF\\SpermAnalysisWPF\\SpermAnalysisWPF.csproj", "projectName": "SpermAnalysisWPF", "projectPath": "D:\\code\\SpermFormAnalysis-界面优化版本0802\\SpermAnalysisWPF\\SpermAnalysisWPF\\SpermAnalysisWPF.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code\\SpermFormAnalysis-界面优化版本0802\\SpermAnalysisWPF\\SpermAnalysisWPF\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"EntityFramework": {"target": "Package", "version": "[6.4.4, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "NPOI": {"target": "Package", "version": "[2.6.0-rc-3, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.118, )"}, "System.Data.SQLite.EF6": {"target": "Package", "version": "[1.0.118, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}