2025-08-03 10:30:10,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, <PERSON><PERSON><PERSON> last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 68
2025-08-03 10:30:10,调试：10
2025-08-03 10:30:15,原图片数：2 当前选择的图片数1
2025-08-03 10:30:16,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:16,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:16,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:26,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 68
2025-08-03 10:30:26,调试：10
2025-08-03 10:30:30,原图片数：3 当前选择的图片数1
2025-08-03 10:30:31,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:31,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:31,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:41,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 68
2025-08-03 10:30:41,调试：10
2025-08-03 10:30:45,有错误我有执行吗？D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\bin\x64\Debug\Reports\2025\08\
2025-08-03 10:30:50,原项目尾巴数据: 精子1
2025-08-03 10:30:50,  vop_middlepiece点数: 0
2025-08-03 10:30:50,  MDPnum: 0
2025-08-03 10:30:50,  偏移量: (60.0,86.0)
2025-08-03 10:30:50,  图像尺寸: 1095x684
2025-08-03 10:30:50,  Basic.imageSize: 1920x1200
2025-08-03 10:30:50,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:50,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:50,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:50,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:50,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:50,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:50,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:30:50,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:30:50,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:30:50,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:50,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:50,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:50,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:50,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:30:50,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:30:50,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:50,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:30:50,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:30:52,原项目尾巴数据: 精子1
2025-08-03 10:30:52,  vop_middlepiece点数: 0
2025-08-03 10:30:52,  MDPnum: 0
2025-08-03 10:30:52,  偏移量: (60.0,86.0)
2025-08-03 10:30:52,  图像尺寸: 1095x684
2025-08-03 10:30:52,  Basic.imageSize: 1920x1200
2025-08-03 10:30:52,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:52,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:52,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:52,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:52,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:52,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:52,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:30:52,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:30:52,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:30:52,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:52,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:52,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:52,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:52,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:30:52,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:30:52,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:52,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:30:52,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:30:53,原项目尾巴数据: 精子1
2025-08-03 10:30:53,  vop_middlepiece点数: 0
2025-08-03 10:30:53,  MDPnum: 0
2025-08-03 10:30:53,  偏移量: (60.0,86.0)
2025-08-03 10:30:53,  图像尺寸: 1095x684
2025-08-03 10:30:53,  Basic.imageSize: 1920x1200
2025-08-03 10:30:53,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:53,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:53,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:53,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:53,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:53,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:53,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:30:53,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:30:53,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:30:53,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:53,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:30:53,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:30:53,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:53,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:30:53,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:30:53,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:30:53,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:30:53,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:31:59,原项目尾巴数据: 精子1
2025-08-03 10:31:59,  vop_middlepiece点数: 0
2025-08-03 10:31:59,  MDPnum: 0
2025-08-03 10:31:59,  偏移量: (60.0,86.0)
2025-08-03 10:31:59,  图像尺寸: 1095x684
2025-08-03 10:31:59,  Basic.imageSize: 1920x1200
2025-08-03 10:31:59,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:31:59,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:31:59,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:31:59,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:31:59,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:31:59,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:31:59,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:31:59,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:31:59,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:31:59,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:31:59,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:31:59,原项目getBox结果: 精子1, 最终坐标(473.9,458.6), 尺寸(138.7x65.2)
2025-08-03 10:31:59,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:31:59,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:31:59,原项目getBox结果: 精子1, 最终坐标(473.9,458.6), 尺寸(138.7x65.2)
2025-08-03 10:32:01,原项目尾巴数据: 精子1
2025-08-03 10:32:01,  vop_middlepiece点数: 0
2025-08-03 10:32:01,  MDPnum: 0
2025-08-03 10:32:01,  偏移量: (60.0,86.0)
2025-08-03 10:32:01,  图像尺寸: 1095x684
2025-08-03 10:32:01,  Basic.imageSize: 1920x1200
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(473.9,458.6), 尺寸(138.7x65.2)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(473.9,458.6), 尺寸(138.7x65.2)
2025-08-03 10:32:01,原项目尾巴数据: 精子1
2025-08-03 10:32:01,  vop_middlepiece点数: 0
2025-08-03 10:32:01,  MDPnum: 0
2025-08-03 10:32:01,  偏移量: (60.0,86.0)
2025-08-03 10:32:01,  图像尺寸: 1095x684
2025-08-03 10:32:01,  Basic.imageSize: 1920x1200
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(831.0,805.0), 尺寸(243.3x114.3)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(473.9,458.6), 尺寸(138.7x65.2)
2025-08-03 10:32:01,原项目getBox: 精子1, 原始边界(0.443,0.688)-(0.549,0.750)
2025-08-03 10:32:01,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:01,原项目getBox结果: 精子1, 最终坐标(473.9,458.6), 尺寸(138.7x65.2)
2025-08-03 10:32:02,原项目尾巴数据: 精子1
2025-08-03 10:32:02,  vop_middlepiece点数: 82
2025-08-03 10:32:02,  MDPnum: 50
2025-08-03 10:32:02,  偏移量: (60.0,86.0)
2025-08-03 10:32:02,  图像尺寸: 1095x684
2025-08-03 10:32:02,  Basic.imageSize: 1920x1200
2025-08-03 10:32:02,  前3个点: (1502,1077)
2025-08-03 10:32:02,           (1500,1077)
2025-08-03 10:32:02,           (1498,1077)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:02,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:32:02,原项目第一个轮廓点: 原始(53,32), 画布偏移(60.0,86.0)
2025-08-03 10:32:02,原项目转换结果: 第一个点(247.0,326.5)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:02,原项目尾巴数据: 精子1
2025-08-03 10:32:02,  vop_middlepiece点数: 82
2025-08-03 10:32:02,  MDPnum: 50
2025-08-03 10:32:02,  偏移量: (60.0,86.0)
2025-08-03 10:32:02,  图像尺寸: 1095x684
2025-08-03 10:32:02,  Basic.imageSize: 1920x1200
2025-08-03 10:32:02,  前3个点: (1502,1077)
2025-08-03 10:32:02,           (1500,1077)
2025-08-03 10:32:02,           (1498,1077)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:02,原项目坐标转换: 精子5, 偏移(1244.6,177.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:32:02,原项目第一个轮廓点: 原始(96,19), 画布偏移(60.0,86.0)
2025-08-03 10:32:02,原项目转换结果: 第一个点(824.5,197.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:02,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:02,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:02,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:04,原项目尾巴数据: 精子1
2025-08-03 10:32:04,  vop_middlepiece点数: 82
2025-08-03 10:32:04,  MDPnum: 50
2025-08-03 10:32:04,  偏移量: (60.0,86.0)
2025-08-03 10:32:04,  图像尺寸: 1095x684
2025-08-03 10:32:04,  Basic.imageSize: 1920x1200
2025-08-03 10:32:04,  前3个点: (1502,1077)
2025-08-03 10:32:04,           (1500,1077)
2025-08-03 10:32:04,           (1498,1077)
2025-08-03 10:32:04,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:04,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:04,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:04,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:04,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:04,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:04,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:32:04,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:32:04,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:32:04,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:04,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:04,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:04,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:04,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:04,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:04,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:04,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:04,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:06,原项目尾巴数据: 精子1
2025-08-03 10:32:06,  vop_middlepiece点数: 82
2025-08-03 10:32:06,  MDPnum: 50
2025-08-03 10:32:06,  偏移量: (60.0,86.0)
2025-08-03 10:32:06,  图像尺寸: 1095x684
2025-08-03 10:32:06,  Basic.imageSize: 1920x1200
2025-08-03 10:32:06,  前3个点: (1502,1077)
2025-08-03 10:32:06,           (1500,1077)
2025-08-03 10:32:06,           (1498,1077)
2025-08-03 10:32:06,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:06,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:06,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:06,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:06,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:06,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:06,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:32:06,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:32:06,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:32:06,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:06,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:06,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:06,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:06,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:06,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:06,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:06,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:06,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:09,原项目尾巴数据: 精子1
2025-08-03 10:32:09,  vop_middlepiece点数: 0
2025-08-03 10:32:09,  MDPnum: 0
2025-08-03 10:32:09,  偏移量: (60.0,86.0)
2025-08-03 10:32:09,  图像尺寸: 1095x684
2025-08-03 10:32:09,  Basic.imageSize: 1920x1200
2025-08-03 10:32:09,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:09,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:09,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:09,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:09,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:09,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:09,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:32:09,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:32:09,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:32:09,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:09,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:09,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:09,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:09,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:09,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:09,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:09,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:09,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:12,原项目尾巴数据: 精子1
2025-08-03 10:32:12,  vop_middlepiece点数: 0
2025-08-03 10:32:12,  MDPnum: 0
2025-08-03 10:32:12,  偏移量: (0.0,0.0)
2025-08-03 10:32:12,  图像尺寸: 1344x840
2025-08-03 10:32:12,  Basic.imageSize: 1920x1200
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:12,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:12,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:12,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:12,原项目尾巴数据: 精子1
2025-08-03 10:32:12,  vop_middlepiece点数: 0
2025-08-03 10:32:12,  MDPnum: 0
2025-08-03 10:32:12,  偏移量: (0.0,0.0)
2025-08-03 10:32:12,  图像尺寸: 1344x840
2025-08-03 10:32:12,  Basic.imageSize: 1920x1200
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:12,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:12,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:12,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:13,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:13,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:13,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:13,原项目尾巴数据: 精子1
2025-08-03 10:32:13,  vop_middlepiece点数: 0
2025-08-03 10:32:13,  MDPnum: 0
2025-08-03 10:32:13,  偏移量: (0.0,0.0)
2025-08-03 10:32:13,  图像尺寸: 1344x840
2025-08-03 10:32:13,  Basic.imageSize: 1920x1200
2025-08-03 10:32:13,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:13,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:13,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:13,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:13,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:13,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:13,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:13,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:13,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:13,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:13,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:13,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:13,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:13,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:13,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:13,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:13,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:13,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:14,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:14,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:14,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:14,原项目尾巴数据: 精子1
2025-08-03 10:32:14,  vop_middlepiece点数: 0
2025-08-03 10:32:14,  MDPnum: 0
2025-08-03 10:32:14,  偏移量: (0.0,0.0)
2025-08-03 10:32:14,  图像尺寸: 1344x840
2025-08-03 10:32:14,  Basic.imageSize: 1920x1200
2025-08-03 10:32:14,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:14,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:14,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:14,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:14,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:14,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:14,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:14,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:14,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:14,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:14,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:14,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:14,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:14,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:14,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:14,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:14,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:14,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:15,原项目尾巴数据: 精子1
2025-08-03 10:32:15,  vop_middlepiece点数: 0
2025-08-03 10:32:15,  MDPnum: 0
2025-08-03 10:32:15,  偏移量: (0.0,0.0)
2025-08-03 10:32:15,  图像尺寸: 1344x840
2025-08-03 10:32:15,  Basic.imageSize: 1920x1200
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:15,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:15,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:15,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:15,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:15,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:15,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目尾巴数据: 精子1
2025-08-03 10:32:17,  vop_middlepiece点数: 0
2025-08-03 10:32:17,  MDPnum: 0
2025-08-03 10:32:17,  偏移量: (0.0,0.0)
2025-08-03 10:32:17,  图像尺寸: 1344x840
2025-08-03 10:32:17,  Basic.imageSize: 1920x1200
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:17,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:17,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目尾巴数据: 精子1
2025-08-03 10:32:17,  vop_middlepiece点数: 0
2025-08-03 10:32:17,  MDPnum: 0
2025-08-03 10:32:17,  偏移量: (0.0,0.0)
2025-08-03 10:32:17,  图像尺寸: 1344x840
2025-08-03 10:32:17,  Basic.imageSize: 1920x1200
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:17,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:17,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:17,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:17,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:17,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:18,原项目尾巴数据: 精子1
2025-08-03 10:32:18,  vop_middlepiece点数: 0
2025-08-03 10:32:18,  MDPnum: 0
2025-08-03 10:32:18,  偏移量: (0.0,0.0)
2025-08-03 10:32:18,  图像尺寸: 1344x840
2025-08-03 10:32:18,  Basic.imageSize: 1920x1200
2025-08-03 10:32:18,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:18,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:18,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:18,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:18,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:18,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:18,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1344.0x840.0), 基准分辨率(1920x1200)
2025-08-03 10:32:18,原项目第一个轮廓点: 原始(156,44), 画布偏移(0.0,0.0)
2025-08-03 10:32:18,原项目转换结果: 第一个点(1090.4,739.2)
2025-08-03 10:32:18,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:18,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:18,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:18,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:18,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:18,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:18,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:18,原项目getBox: 图像尺寸(1344.0x840.0), 分辨率比例(1920x1200)
2025-08-03 10:32:18,原项目getBox结果: 精子1, 最终坐标(981.2,708.0), 尺寸(140.1x68.4)
2025-08-03 10:32:18,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:18,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:18,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:19,原项目尾巴数据: 精子1
2025-08-03 10:32:19,  vop_middlepiece点数: 0
2025-08-03 10:32:19,  MDPnum: 0
2025-08-03 10:32:19,  偏移量: (60.0,86.0)
2025-08-03 10:32:19,  图像尺寸: 1095x684
2025-08-03 10:32:19,  Basic.imageSize: 1920x1200
2025-08-03 10:32:19,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:19,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:19,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:19,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:19,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:19,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:19,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 10:32:19,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 10:32:19,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 10:32:19,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:19,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 10:32:19,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 10:32:19,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:19,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:19,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 10:32:19,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 10:32:19,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 10:32:19,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
