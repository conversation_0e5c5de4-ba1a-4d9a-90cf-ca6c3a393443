2025-08-03 11:43:39,原图片数：1 当前选择的图片数1
2025-08-03 11:43:42,原项目getBox: 精子1, 原始边界(0.800,0.766)-(0.863,0.869)
2025-08-03 11:43:42,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:43:42,原项目getBox结果: 精子1, 最终坐标(1516.7,898.0), 尺寸(159.7x164.4)
2025-08-03 11:43:59,GPU内存不足，尝试使用CPU模式: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:44:10,CPU模式也失败: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:44:10,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 97
2025-08-03 11:44:10,调试：5
2025-08-03 11:44:12,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:44:12,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:44:12,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:44:22,GPU内存不足，尝试使用CPU模式: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:44:31,原图片数：2 当前选择的图片数1
2025-08-03 11:44:32,CPU模式也失败: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:44:32,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 97
2025-08-03 11:44:32,调试：10
2025-08-03 11:44:35,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:44:35,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:44:35,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:44:42,原图片数：3 当前选择的图片数1
2025-08-03 11:44:45,GPU内存不足，尝试使用CPU模式: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:44:55,CPU模式也失败: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:44:55,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 97
2025-08-03 11:44:55,调试：10
2025-08-03 11:44:59,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:44:59,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:44:59,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:09,GPU内存不足，尝试使用CPU模式: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:45:20,CPU模式也失败: OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

2025-08-03 11:45:20,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 sperms, String fn) 位置 D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\Segments\clsTailProcess.cs:行号 97
2025-08-03 11:45:20,调试：10
2025-08-03 11:45:29,有错误我有执行吗？D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\bin\x64\Debug\Reports\2025\08\
2025-08-03 11:45:33,原项目尾巴数据: 精子1
2025-08-03 11:45:33,  vop_middlepiece点数: 0
2025-08-03 11:45:33,  MDPnum: 0
2025-08-03 11:45:33,  偏移量: (60.0,86.0)
2025-08-03 11:45:33,  图像尺寸: 1095x684
2025-08-03 11:45:33,  Basic.imageSize: 1920x1200
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:33,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:33,原项目第一个轮廓点: 原始(53,32), 画布偏移(60.0,86.0)
2025-08-03 11:45:33,原项目转换结果: 第一个点(247.0,326.5)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:33,原项目尾巴数据: 精子1
2025-08-03 11:45:33,  vop_middlepiece点数: 0
2025-08-03 11:45:33,  MDPnum: 0
2025-08-03 11:45:33,  偏移量: (60.0,86.0)
2025-08-03 11:45:33,  图像尺寸: 1095x684
2025-08-03 11:45:33,  Basic.imageSize: 1920x1200
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:33,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:33,原项目第一个轮廓点: 原始(53,32), 画布偏移(60.0,86.0)
2025-08-03 11:45:33,原项目转换结果: 第一个点(247.0,326.5)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:33,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:33,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:33,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:35,原项目尾巴数据: 精子1
2025-08-03 11:45:35,  vop_middlepiece点数: 0
2025-08-03 11:45:35,  MDPnum: 0
2025-08-03 11:45:35,  偏移量: (60.0,86.0)
2025-08-03 11:45:35,  图像尺寸: 1095x684
2025-08-03 11:45:35,  Basic.imageSize: 1920x1200
2025-08-03 11:45:35,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:35,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:35,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:35,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:35,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:35,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:35,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:35,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 11:45:35,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 11:45:35,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:35,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:35,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:35,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:35,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:35,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:35,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:35,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:35,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:36,原项目尾巴数据: 精子1
2025-08-03 11:45:36,  vop_middlepiece点数: 0
2025-08-03 11:45:36,  MDPnum: 0
2025-08-03 11:45:36,  偏移量: (60.0,86.0)
2025-08-03 11:45:36,  图像尺寸: 1095x684
2025-08-03 11:45:36,  Basic.imageSize: 1920x1200
2025-08-03 11:45:36,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:36,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:36,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:36,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:36,原项目第一个轮廓点: 原始(53,32), 画布偏移(60.0,86.0)
2025-08-03 11:45:36,原项目转换结果: 第一个点(247.0,326.5)
2025-08-03 11:45:36,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:36,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:36,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:36,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:36,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:36,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:36,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:36,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:36,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:36,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:36,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:36,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目尾巴数据: 精子1
2025-08-03 11:45:37,  vop_middlepiece点数: 0
2025-08-03 11:45:37,  MDPnum: 0
2025-08-03 11:45:37,  偏移量: (-70.0,86.0)
2025-08-03 11:45:37,  图像尺寸: 1095x684
2025-08-03 11:45:37,  Basic.imageSize: 1920x1200
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:37,原项目第一个轮廓点: 原始(53,32), 画布偏移(-70.0,86.0)
2025-08-03 11:45:37,原项目转换结果: 第一个点(117.0,326.5)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目尾巴数据: 精子1
2025-08-03 11:45:37,  vop_middlepiece点数: 0
2025-08-03 11:45:37,  MDPnum: 0
2025-08-03 11:45:37,  偏移量: (-230.0,86.0)
2025-08-03 11:45:37,  图像尺寸: 1095x684
2025-08-03 11:45:37,  Basic.imageSize: 1920x1200
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:37,原项目第一个轮廓点: 原始(53,32), 画布偏移(-230.0,86.0)
2025-08-03 11:45:37,原项目转换结果: 第一个点(-43.0,326.5)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目尾巴数据: 精子1
2025-08-03 11:45:37,  vop_middlepiece点数: 0
2025-08-03 11:45:37,  MDPnum: 0
2025-08-03 11:45:37,  偏移量: (-420.0,86.0)
2025-08-03 11:45:37,  图像尺寸: 1095x684
2025-08-03 11:45:37,  Basic.imageSize: 1920x1200
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:37,原项目第一个轮廓点: 原始(53,32), 画布偏移(-420.0,86.0)
2025-08-03 11:45:37,原项目转换结果: 第一个点(-233.0,326.5)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目尾巴数据: 精子1
2025-08-03 11:45:37,  vop_middlepiece点数: 0
2025-08-03 11:45:37,  MDPnum: 0
2025-08-03 11:45:37,  偏移量: (-640.0,86.0)
2025-08-03 11:45:37,  图像尺寸: 1095x684
2025-08-03 11:45:37,  Basic.imageSize: 1920x1200
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:37,原项目第一个轮廓点: 原始(53,32), 画布偏移(-640.0,86.0)
2025-08-03 11:45:37,原项目转换结果: 第一个点(-453.0,326.5)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目尾巴数据: 精子1
2025-08-03 11:45:37,  vop_middlepiece点数: 0
2025-08-03 11:45:37,  MDPnum: 0
2025-08-03 11:45:37,  偏移量: (-890.0,86.0)
2025-08-03 11:45:37,  图像尺寸: 1095x684
2025-08-03 11:45:37,  Basic.imageSize: 1920x1200
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:37,原项目第一个轮廓点: 原始(53,32), 画布偏移(-890.0,86.0)
2025-08-03 11:45:37,原项目转换结果: 第一个点(-703.0,326.5)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目尾巴数据: 精子1
2025-08-03 11:45:37,  vop_middlepiece点数: 0
2025-08-03 11:45:37,  MDPnum: 0
2025-08-03 11:45:37,  偏移量: (60.0,86.0)
2025-08-03 11:45:37,  图像尺寸: 1095x684
2025-08-03 11:45:37,  Basic.imageSize: 1920x1200
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目坐标转换: 精子4, 偏移(274.8,390.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:37,原项目第一个轮廓点: 原始(53,32), 画布偏移(60.0,86.0)
2025-08-03 11:45:37,原项目转换结果: 第一个点(247.0,326.5)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:37,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:37,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:37,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目尾巴数据: 精子1
2025-08-03 11:45:38,  vop_middlepiece点数: 0
2025-08-03 11:45:38,  MDPnum: 0
2025-08-03 11:45:38,  偏移量: (-70.0,86.0)
2025-08-03 11:45:38,  图像尺寸: 1095x684
2025-08-03 11:45:38,  Basic.imageSize: 1920x1200
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:38,原项目第一个轮廓点: 原始(156,44), 画布偏移(-70.0,86.0)
2025-08-03 11:45:38,原项目转换结果: 第一个点(818.4,687.9)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目尾巴数据: 精子1
2025-08-03 11:45:38,  vop_middlepiece点数: 0
2025-08-03 11:45:38,  MDPnum: 0
2025-08-03 11:45:38,  偏移量: (-230.0,86.0)
2025-08-03 11:45:38,  图像尺寸: 1095x684
2025-08-03 11:45:38,  Basic.imageSize: 1920x1200
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:38,原项目第一个轮廓点: 原始(156,44), 画布偏移(-230.0,86.0)
2025-08-03 11:45:38,原项目转换结果: 第一个点(658.4,687.9)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目尾巴数据: 精子1
2025-08-03 11:45:38,  vop_middlepiece点数: 0
2025-08-03 11:45:38,  MDPnum: 0
2025-08-03 11:45:38,  偏移量: (-420.0,86.0)
2025-08-03 11:45:38,  图像尺寸: 1095x684
2025-08-03 11:45:38,  Basic.imageSize: 1920x1200
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:38,原项目第一个轮廓点: 原始(156,44), 画布偏移(-420.0,86.0)
2025-08-03 11:45:38,原项目转换结果: 第一个点(468.4,687.9)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目尾巴数据: 精子1
2025-08-03 11:45:38,  vop_middlepiece点数: 0
2025-08-03 11:45:38,  MDPnum: 0
2025-08-03 11:45:38,  偏移量: (-640.0,86.0)
2025-08-03 11:45:38,  图像尺寸: 1095x684
2025-08-03 11:45:38,  Basic.imageSize: 1920x1200
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:38,原项目第一个轮廓点: 原始(156,44), 画布偏移(-640.0,86.0)
2025-08-03 11:45:38,原项目转换结果: 第一个点(248.4,687.9)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目尾巴数据: 精子1
2025-08-03 11:45:38,  vop_middlepiece点数: 0
2025-08-03 11:45:38,  MDPnum: 0
2025-08-03 11:45:38,  偏移量: (-890.0,86.0)
2025-08-03 11:45:38,  图像尺寸: 1095x684
2025-08-03 11:45:38,  Basic.imageSize: 1920x1200
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:38,原项目第一个轮廓点: 原始(156,44), 画布偏移(-890.0,86.0)
2025-08-03 11:45:38,原项目转换结果: 第一个点(-1.6,687.9)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:38,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:38,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:38,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:39,原项目尾巴数据: 精子1
2025-08-03 11:45:39,  vop_middlepiece点数: 0
2025-08-03 11:45:39,  MDPnum: 0
2025-08-03 11:45:39,  偏移量: (60.0,86.0)
2025-08-03 11:45:39,  图像尺寸: 1095x684
2025-08-03 11:45:39,  Basic.imageSize: 1920x1200
2025-08-03 11:45:39,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:39,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:39,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:39,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:39,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:39,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:39,原项目坐标转换: 精子1, 偏移(1401.7,1012.0), 显示尺寸(1095.0x684.0), 基准分辨率(1920x1200)
2025-08-03 11:45:39,原项目第一个轮廓点: 原始(156,44), 画布偏移(60.0,86.0)
2025-08-03 11:45:39,原项目转换结果: 第一个点(948.4,687.9)
2025-08-03 11:45:39,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:39,原项目getBox: 图像尺寸(1920.0x1200.0), 分辨率比例(1920x1200)
2025-08-03 11:45:39,原项目getBox结果: 精子1, 最终坐标(1401.7,1012.0), 尺寸(200.1x97.7)
2025-08-03 11:45:39,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:39,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:39,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
2025-08-03 11:45:39,原项目getBox: 精子1, 原始边界(0.740,0.860)-(0.824,0.908)
2025-08-03 11:45:39,原项目getBox: 图像尺寸(1095.0x684.0), 分辨率比例(1920x1200)
2025-08-03 11:45:39,原项目getBox结果: 精子1, 最终坐标(799.4,576.6), 尺寸(114.1x55.7)
