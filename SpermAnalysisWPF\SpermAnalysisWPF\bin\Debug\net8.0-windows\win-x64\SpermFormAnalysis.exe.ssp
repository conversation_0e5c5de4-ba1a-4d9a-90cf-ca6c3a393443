<?xml version="1.0" encoding="utf-8"?>
<ssprotect>
	<base_info>
		<app>Virbox Protector 2</app>
		<version>2.5.2.16690</version>
	</base_info>
	<file>
		<path>protected\SpermFormAnalysis.exe</path>
	</file>
	<option>
		<mix>1</mix>
		<name_list></name_list>
		<zip>0</zip>
		<jit_encrypt>1</jit_encrypt>
		<anti_debugging>1</anti_debugging>
	</option>
	<sign />
	<plugin>
		<ds enable="0">
			<x86 file="ds_windows_x86.dll"></x86>
			<x64 file="ds_windows_x64.dll"></x64>
			<option>
				<pwd type="password">5485F9039CE959004387626866E79F6F</pwd>
			</option>
		</ds>
	</plugin>
	<function>
		<SENSENODEFUNC1 type="function" name="AIDFI.Form::Quality:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC1>
		<SENSENODEFUNC2 type="function" name="AIDFI.Form::Quality:void Quality_FormClosed(object,FormClosedEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC2>
		<SENSENODEFUNC3 type="function" name="AIDFI.Form::Quality:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC3>
		<SENSENODEFUNC4 type="function" name="AIDFI.Form::Quality:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC4>
		<SENSENODEFUNC5 type="function" name="SpermFormAnalysis::Login:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC5>
		<SENSENODEFUNC6 type="function" name="SpermFormAnalysis::Login:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC6>
		<SENSENODEFUNC7 type="function" name="SpermFormAnalysis::Login:void Login_ButtonCancelClick(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC7>
		<SENSENODEFUNC8 type="function" name="SpermFormAnalysis::Login:void Login_ButtonLoginClick(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC8>
		<SENSENODEFUNC9 type="function" name="SpermFormAnalysis::Login:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC9>
		<SENSENODEFUNC10 type="function" name="SpermFormAnalysis::Login:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC10>
		<SENSENODEFUNC11 type="function" name="SpermFormAnalysis::Mains:void add_SearchFlushEvent(Handler)">
			<flags>1</flags>
		</SENSENODEFUNC11>
		<SENSENODEFUNC12 type="function" name="SpermFormAnalysis::Mains:void remove_SearchFlushEvent(Handler)">
			<flags>1</flags>
		</SENSENODEFUNC12>
		<SENSENODEFUNC13 type="function" name="SpermFormAnalysis::Mains:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC13>
		<SENSENODEFUNC14 type="function" name="SpermFormAnalysis::Mains:void Main_KeyDown(object,KeyEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC14>
		<SENSENODEFUNC15 type="function" name="SpermFormAnalysis::Mains:void Aside_MenuItemClick(TreeNode,NavMenuItem,int)">
			<flags>1</flags>
		</SENSENODEFUNC15>
		<SENSENODEFUNC16 type="function" name="SpermFormAnalysis::Mains:void uiSymbolButton1_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC16>
		<SENSENODEFUNC17 type="function" name="SpermFormAnalysis::Mains:void uiSymbolButton2_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC17>
		<SENSENODEFUNC18 type="function" name="SpermFormAnalysis::Mains:void Mains_FormClosed(object,FormClosedEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC18>
		<SENSENODEFUNC19 type="function" name="SpermFormAnalysis::Mains:void Mains_KeyDown(object,KeyEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC19>
		<SENSENODEFUNC20 type="function" name="SpermFormAnalysis::Mains:void uiHeaderButton1_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC20>
		<SENSENODEFUNC21 type="function" name="SpermFormAnalysis::Mains:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC21>
		<SENSENODEFUNC22 type="function" name="SpermFormAnalysis::Mains:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC22>
		<SENSENODEFUNC23 type="function" name="SpermFormAnalysis::Program:void Main()">
			<flags>1</flags>
		</SENSENODEFUNC23>
		<SENSENODEFUNC24 type="function" name="SpermFormAnalysis.Properties::Resources:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC24>
		<SENSENODEFUNC25 type="function" name="SpermFormAnalysis.Properties::Resources:ResourceManager get_ResourceManager()">
			<flags>1</flags>
		</SENSENODEFUNC25>
		<SENSENODEFUNC26 type="function" name="SpermFormAnalysis.Properties::Resources:CultureInfo get_Culture()">
			<flags>1</flags>
		</SENSENODEFUNC26>
		<SENSENODEFUNC27 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__01()">
			<flags>1</flags>
		</SENSENODEFUNC27>
		<SENSENODEFUNC28 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__02()">
			<flags>1</flags>
		</SENSENODEFUNC28>
		<SENSENODEFUNC29 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__03()">
			<flags>1</flags>
		</SENSENODEFUNC29>
		<SENSENODEFUNC30 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__11()">
			<flags>1</flags>
		</SENSENODEFUNC30>
		<SENSENODEFUNC31 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__12()">
			<flags>1</flags>
		</SENSENODEFUNC31>
		<SENSENODEFUNC32 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__13()">
			<flags>1</flags>
		</SENSENODEFUNC32>
		<SENSENODEFUNC33 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__14()">
			<flags>1</flags>
		</SENSENODEFUNC33>
		<SENSENODEFUNC34 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__15()">
			<flags>1</flags>
		</SENSENODEFUNC34>
		<SENSENODEFUNC35 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__21()">
			<flags>1</flags>
		</SENSENODEFUNC35>
		<SENSENODEFUNC36 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__31()">
			<flags>1</flags>
		</SENSENODEFUNC36>
		<SENSENODEFUNC37 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__32()">
			<flags>1</flags>
		</SENSENODEFUNC37>
		<SENSENODEFUNC38 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__33()">
			<flags>1</flags>
		</SENSENODEFUNC38>
		<SENSENODEFUNC39 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__34()">
			<flags>1</flags>
		</SENSENODEFUNC39>
		<SENSENODEFUNC40 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__41()">
			<flags>1</flags>
		</SENSENODEFUNC40>
		<SENSENODEFUNC41 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get__42()">
			<flags>1</flags>
		</SENSENODEFUNC41>
		<SENSENODEFUNC42 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get_Delete()">
			<flags>1</flags>
		</SENSENODEFUNC42>
		<SENSENODEFUNC43 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get_DeleteHover()">
			<flags>1</flags>
		</SENSENODEFUNC43>
		<SENSENODEFUNC44 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get_save()">
			<flags>1</flags>
		</SENSENODEFUNC44>
		<SENSENODEFUNC45 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get_savexuanzhong()">
			<flags>1</flags>
		</SENSENODEFUNC45>
		<SENSENODEFUNC46 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get_星博智造()">
			<flags>1</flags>
		</SENSENODEFUNC46>
		<SENSENODEFUNC47 type="function" name="SpermFormAnalysis.Properties::Resources:Bitmap get_星博智造黑色2()">
			<flags>1</flags>
		</SENSENODEFUNC47>
		<SENSENODEFUNC48 type="function" name="SpermFormAnalysis.Properties::Settings:Settings get_Default()">
			<flags>1</flags>
		</SENSENODEFUNC48>
		<SENSENODEFUNC49 type="function" name="SpermFormAnalysis.Properties::Settings:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC49>
		<SENSENODEFUNC50 type="function" name="SpermFormAnalysis.utils::Basic:ObservableCollection`1&lt;string&gt; getSpermType(string)">
			<flags>1</flags>
		</SENSENODEFUNC50>
		<SENSENODEFUNC51 type="function" name="SpermFormAnalysis.utils::Basic:ObservableCollection`1&lt;string&gt; getHeadType(string)">
			<flags>1</flags>
		</SENSENODEFUNC51>
		<SENSENODEFUNC52 type="function" name="SpermFormAnalysis.utils::Basic:ObservableCollection`1&lt;string&gt; getAcroType(string)">
			<flags>1</flags>
		</SENSENODEFUNC52>
		<SENSENODEFUNC53 type="function" name="SpermFormAnalysis.utils::Basic:ObservableCollection`1&lt;string&gt; getKernelType(string)">
			<flags>1</flags>
		</SENSENODEFUNC53>
		<SENSENODEFUNC54 type="function" name="SpermFormAnalysis.utils::Basic:ObservableCollection`1&lt;string&gt; getMDPTailType(string)">
			<flags>1</flags>
		</SENSENODEFUNC54>
		<SENSENODEFUNC55 type="function" name="SpermFormAnalysis.utils::Basic:ObservableCollection`1&lt;string&gt; getTailType(string)">
			<flags>1</flags>
		</SENSENODEFUNC55>
		<SENSENODEFUNC56 type="function" name="SpermFormAnalysis.utils::Basic:string GetDes(Enum)">
			<flags>1</flags>
		</SENSENODEFUNC56>
		<SENSENODEFUNC57 type="function" name="SpermFormAnalysis.utils::Basic:string GetDescription(Enum)">
			<flags>1</flags>
		</SENSENODEFUNC57>
		<SENSENODEFUNC58 type="function" name="SpermFormAnalysis.utils::Basic:DescriptionAttribute[] GetDescriptAttr(FieldInfo)">
			<flags>1</flags>
		</SENSENODEFUNC58>
		<SENSENODEFUNC59 type="function" name="SpermFormAnalysis.utils::Basic:T_0 GetEnumName&lt;T&gt;(string)">
			<flags>1</flags>
		</SENSENODEFUNC59>
		<SENSENODEFUNC60 type="function" name="SpermFormAnalysis.utils::Basic:void createDir(string)">
			<flags>1</flags>
		</SENSENODEFUNC60>
		<SENSENODEFUNC61 type="function" name="SpermFormAnalysis.utils::Basic:void createFullDir(string)">
			<flags>1</flags>
		</SENSENODEFUNC61>
		<SENSENODEFUNC62 type="function" name="SpermFormAnalysis.utils::Basic:string getMD5Encrypt(string)">
			<flags>1</flags>
		</SENSENODEFUNC62>
		<SENSENODEFUNC63 type="function" name="SpermFormAnalysis.utils::Basic:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC63>
		<SENSENODEFUNC64 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:int get_steps()">
			<flags>1</flags>
		</SENSENODEFUNC64>
		<SENSENODEFUNC65 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_steps(int)">
			<flags>1</flags>
		</SENSENODEFUNC65>
		<SENSENODEFUNC66 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mStoreStatus get_slideStoreStatus0()">
			<flags>1</flags>
		</SENSENODEFUNC66>
		<SENSENODEFUNC67 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_slideStoreStatus0(mStoreStatus)">
			<flags>1</flags>
		</SENSENODEFUNC67>
		<SENSENODEFUNC68 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_slideStoreStatus0str()">
			<flags>1</flags>
		</SENSENODEFUNC68>
		<SENSENODEFUNC69 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_slideStoreStatus0str(string)">
			<flags>1</flags>
		</SENSENODEFUNC69>
		<SENSENODEFUNC70 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mSlideStatus[] get_slidesStatus0()">
			<flags>1</flags>
		</SENSENODEFUNC70>
		<SENSENODEFUNC71 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_slidesStatus0(mSlideStatus[])">
			<flags>1</flags>
		</SENSENODEFUNC71>
		<SENSENODEFUNC72 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mStoreStatus get_slideStoreStatus1()">
			<flags>1</flags>
		</SENSENODEFUNC72>
		<SENSENODEFUNC73 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_slideStoreStatus1(mStoreStatus)">
			<flags>1</flags>
		</SENSENODEFUNC73>
		<SENSENODEFUNC74 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_slideStoreStatus1str()">
			<flags>1</flags>
		</SENSENODEFUNC74>
		<SENSENODEFUNC75 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_slideStoreStatus1str(string)">
			<flags>1</flags>
		</SENSENODEFUNC75>
		<SENSENODEFUNC76 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mSlideStatus[] get_slidesStatus1()">
			<flags>1</flags>
		</SENSENODEFUNC76>
		<SENSENODEFUNC77 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_slidesStatus1(mSlideStatus[])">
			<flags>1</flags>
		</SENSENODEFUNC77>
		<SENSENODEFUNC78 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mCtrlStatus get_deviceStatus()">
			<flags>1</flags>
		</SENSENODEFUNC78>
		<SENSENODEFUNC79 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_deviceStatus(mCtrlStatus)">
			<flags>1</flags>
		</SENSENODEFUNC79>
		<SENSENODEFUNC80 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_deviceStatusstr()">
			<flags>1</flags>
		</SENSENODEFUNC80>
		<SENSENODEFUNC81 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_deviceStatusstr(string)">
			<flags>1</flags>
		</SENSENODEFUNC81>
		<SENSENODEFUNC82 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mCameraStatus get_cameraStatus()">
			<flags>1</flags>
		</SENSENODEFUNC82>
		<SENSENODEFUNC83 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_cameraStatus(mCameraStatus)">
			<flags>1</flags>
		</SENSENODEFUNC83>
		<SENSENODEFUNC84 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_cameraStatusstr()">
			<flags>1</flags>
		</SENSENODEFUNC84>
		<SENSENODEFUNC85 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_cameraStatusstr(string)">
			<flags>1</flags>
		</SENSENODEFUNC85>
		<SENSENODEFUNC86 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:mQRScanStatus get_QRScanStatus()">
			<flags>1</flags>
		</SENSENODEFUNC86>
		<SENSENODEFUNC87 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_QRScanStatus(mQRScanStatus)">
			<flags>1</flags>
		</SENSENODEFUNC87>
		<SENSENODEFUNC88 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_QRScanStatusstr()">
			<flags>1</flags>
		</SENSENODEFUNC88>
		<SENSENODEFUNC89 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_QRScanStatusstr(string)">
			<flags>1</flags>
		</SENSENODEFUNC89>
		<SENSENODEFUNC90 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_motionStatus()">
			<flags>1</flags>
		</SENSENODEFUNC90>
		<SENSENODEFUNC91 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_motionStatus(string)">
			<flags>1</flags>
		</SENSENODEFUNC91>
		<SENSENODEFUNC92 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:string get_failureInfo()">
			<flags>1</flags>
		</SENSENODEFUNC92>
		<SENSENODEFUNC93 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void set_failureInfo(string)">
			<flags>1</flags>
		</SENSENODEFUNC93>
		<SENSENODEFUNC94 type="function" name="SpermFormAnalysis.utils::Basic:allStatus:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC94>
		<SENSENODEFUNC95 type="function" name="SpermFormAnalysis.utils::Crypto:DES:string Encode(string)">
			<flags>1</flags>
		</SENSENODEFUNC95>
		<SENSENODEFUNC96 type="function" name="SpermFormAnalysis.utils::Crypto:DES:string Decode(string)">
			<flags>1</flags>
		</SENSENODEFUNC96>
		<SENSENODEFUNC97 type="function" name="SpermFormAnalysis.utils::Crypto:DES:string MD5(string)">
			<flags>1</flags>
		</SENSENODEFUNC97>
		<SENSENODEFUNC98 type="function" name="SpermFormAnalysis.utils::GraphicsUtils:GraphicsPath GetRectangleGp(int,int,int,int,int)">
			<flags>1</flags>
		</SENSENODEFUNC98>
		<SENSENODEFUNC99 type="function" name="SpermFormAnalysis.utils::GraphicsUtils:void FillRectangleGp(int,int,int,int,int,Color,Graphics)">
			<flags>1</flags>
		</SENSENODEFUNC99>
		<SENSENODEFUNC100 type="function" name="SpermFormAnalysis.utils::GraphicsUtils:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC100>
		<SENSENODEFUNC101 type="function" name="SpermFormAnalysis.utils::IniFileUtils:string get_FileName()">
			<flags>1</flags>
		</SENSENODEFUNC101>
		<SENSENODEFUNC102 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void set_FileName(string)">
			<flags>1</flags>
		</SENSENODEFUNC102>
		<SENSENODEFUNC103 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void .ctor(string)">
			<flags>1</flags>
		</SENSENODEFUNC103>
		<SENSENODEFUNC104 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC104>
		<SENSENODEFUNC105 type="function" name="SpermFormAnalysis.utils::IniFileUtils:int ReadInt(string,string,int)">
			<flags>1</flags>
		</SENSENODEFUNC105>
		<SENSENODEFUNC106 type="function" name="SpermFormAnalysis.utils::IniFileUtils:string ReadString(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC106>
		<SENSENODEFUNC107 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void WriteInt(string,string,int)">
			<flags>1</flags>
		</SENSENODEFUNC107>
		<SENSENODEFUNC108 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void WriteString(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC108>
		<SENSENODEFUNC109 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void DeleteSection(string)">
			<flags>1</flags>
		</SENSENODEFUNC109>
		<SENSENODEFUNC110 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void DeleteAllSection()">
			<flags>1</flags>
		</SENSENODEFUNC110>
		<SENSENODEFUNC111 type="function" name="SpermFormAnalysis.utils::IniFileUtils:void IniWriteValue(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC111>
		<SENSENODEFUNC112 type="function" name="SpermFormAnalysis.utils::LogHelper:void WriteErrLog(string)">
			<flags>1</flags>
		</SENSENODEFUNC112>
		<SENSENODEFUNC113 type="function" name="SpermFormAnalysis.utils::PrivateImplementationDetails:uint ComputeStringHash(string)">
			<flags>1</flags>
		</SENSENODEFUNC113>
		<SENSENODEFUNC114 type="function" name="SpermFormAnalysis.TFmodel::common:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC114>
		<SENSENODEFUNC115 type="function" name="SpermFormAnalysis.Segments::clsMrfSegment:void getBox(Sperm,double,double,double,double,double,double)">
			<flags>1</flags>
		</SENSENODEFUNC115>
		<SENSENODEFUNC116 type="function" name="SpermFormAnalysis.Segments::clsMrfSegment:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC116>
		<SENSENODEFUNC117 type="function" name="SpermFormAnalysis.Segments::clsTailProcess:List`1&lt;tailResult&gt; doTailSegProcess_single(List`1&lt;Sperm&gt;,string)">
			<flags>1</flags>
		</SENSENODEFUNC117>
		<SENSENODEFUNC118 type="function" name="SpermFormAnalysis.Segments::clsTailProcess:void createTxt(ObservableCollection`1&lt;Sperm&gt;,string)">
			<flags>1</flags>
		</SENSENODEFUNC118>
		<SENSENODEFUNC119 type="function" name="SpermFormAnalysis.Segments::MRFSegment:double get_MIN_SCORE_FOR_OBJECT()">
			<flags>1</flags>
		</SENSENODEFUNC119>
		<SENSENODEFUNC120 type="function" name="SpermFormAnalysis.Segments::MRFSegment:void set_MIN_SCORE_FOR_OBJECT(double)">
			<flags>1</flags>
		</SENSENODEFUNC120>
		<SENSENODEFUNC121 type="function" name="SpermFormAnalysis.Segments::MRFSegment:bool get_useGpu()">
			<flags>1</flags>
		</SENSENODEFUNC121>
		<SENSENODEFUNC122 type="function" name="SpermFormAnalysis.Segments::MRFSegment:void set_useGpu(bool)">
			<flags>1</flags>
		</SENSENODEFUNC122>
		<SENSENODEFUNC123 type="function" name="SpermFormAnalysis.Segments::MRFSegment:List`1&lt;Sperm&gt; doSegment(string)">
			<flags>1</flags>
		</SENSENODEFUNC123>
		<SENSENODEFUNC124 type="function" name="SpermFormAnalysis.Segments::MRFSegment:Sperm doSegmentTask(int,dectResult,Image`2&lt;Gray&gt;,Image`2&lt;Bgr&gt;,double)">
			<flags>1</flags>
		</SENSENODEFUNC124>
		<SENSENODEFUNC125 type="function" name="SpermFormAnalysis.Segments::MRFSegment:int recognizeSpermShapeAdvanced(dectResult,int,int,int)">
			<flags>1</flags>
		</SENSENODEFUNC125>
		<SENSENODEFUNC126 type="function" name="SpermFormAnalysis.Segments::MRFSegment:int recognizeSpermShapeAdvanced(dectResult,int,int,int)">
			<flags>1</flags>
		</SENSENODEFUNC126>
		<SENSENODEFUNC127 type="function" name="SpermFormAnalysis.Segments::MRFSegment:bool get_subclinical_num(Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC127>
		<SENSENODEFUNC128 type="function" name="SpermFormAnalysis.Segments::MRFSegment:Sperm validSperm(Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC128>
		<SENSENODEFUNC129 type="function" name="SpermFormAnalysis.Segments::MRFSegment:double get_spermFieldValue(string,Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC129>
		<SENSENODEFUNC130 type="function" name="SpermFormAnalysis.Segments::MRFSegment:Sperm set_spermFieldValid(string,int,Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC130>
		<SENSENODEFUNC131 type="function" name="SpermFormAnalysis.Segments::MRFSegment:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC131>
		<SENSENODEFUNC132 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void add_RaiseProcessedPhotoChangedEvent(handleProcessedPhotoChanged)">
			<flags>1</flags>
		</SENSENODEFUNC132>
		<SENSENODEFUNC133 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void remove_RaiseProcessedPhotoChangedEvent(handleProcessedPhotoChanged)">
			<flags>1</flags>
		</SENSENODEFUNC133>
		<SENSENODEFUNC134 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void add_RaiseProcessingPhotoChangedEvent(handleProcesssingPhotoChanged)">
			<flags>1</flags>
		</SENSENODEFUNC134>
		<SENSENODEFUNC135 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void remove_RaiseProcessingPhotoChangedEvent(handleProcesssingPhotoChanged)">
			<flags>1</flags>
		</SENSENODEFUNC135>
		<SENSENODEFUNC136 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void refreshTaskList(string)">
			<flags>1</flags>
		</SENSENODEFUNC136>
		<SENSENODEFUNC137 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void runSegment(object)">
			<flags>1</flags>
		</SENSENODEFUNC137>
		<SENSENODEFUNC138 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void runSegmentTask(string)">
			<flags>1</flags>
		</SENSENODEFUNC138>
		<SENSENODEFUNC139 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void runSegmentTask(string,bool)">
			<flags>1</flags>
		</SENSENODEFUNC139>
		<SENSENODEFUNC140 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void addSperm2db(List`1&lt;Sperm&gt;,Samplephoto,Image`2&lt;Bgr&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC140>
		<SENSENODEFUNC141 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void addSperm2db(List`1&lt;Sperm&gt;,Samplephoto)">
			<flags>1</flags>
		</SENSENODEFUNC141>
		<SENSENODEFUNC142 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void createDirA(string)">
			<flags>1</flags>
		</SENSENODEFUNC142>
		<SENSENODEFUNC143 type="function" name="SpermFormAnalysis.Segments::TaskMgr:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC143>
		<SENSENODEFUNC144 type="function" name="SpermFormAnalysis.Segments::TaskMgr:&lt;&gt;c:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC144>
		<SENSENODEFUNC145 type="function" name="SpermFormAnalysis.PageForm::About:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC145>
		<SENSENODEFUNC146 type="function" name="SpermFormAnalysis.PageForm::About:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC146>
		<SENSENODEFUNC147 type="function" name="SpermFormAnalysis.PageForm::About:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC147>
		<SENSENODEFUNC148 type="function" name="SpermFormAnalysis.PageForm::Authorizes:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC148>
		<SENSENODEFUNC149 type="function" name="SpermFormAnalysis.PageForm::Authorizes:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC149>
		<SENSENODEFUNC150 type="function" name="SpermFormAnalysis.PageForm::Authorizes:void btn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC150>
		<SENSENODEFUNC151 type="function" name="SpermFormAnalysis.PageForm::Authorizes:void btn1_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC151>
		<SENSENODEFUNC152 type="function" name="SpermFormAnalysis.PageForm::Authorizes:void uiComboBox1_SelectedIndexChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC152>
		<SENSENODEFUNC153 type="function" name="SpermFormAnalysis.PageForm::Authorizes:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC153>
		<SENSENODEFUNC154 type="function" name="SpermFormAnalysis.PageForm::Authorizes:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC154>
		<SENSENODEFUNC155 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC155>
		<SENSENODEFUNC156 type="function" name="SpermFormAnalysis.PageForm::CellDetail:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC156>
		<SENSENODEFUNC157 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void .ctor(List`1&lt;Samplephoto&gt;,int,string,Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC157>
		<SENSENODEFUNC158 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void InitializeComponent2()">
			<flags>1</flags>
		</SENSENODEFUNC158>
		<SENSENODEFUNC159 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void ChangePic()">
			<flags>1</flags>
		</SENSENODEFUNC159>
		<SENSENODEFUNC160 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void OriginalPicPanel_Paint(object,PaintEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC160>
		<SENSENODEFUNC161 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void OriginalPicPanel_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC161>
		<SENSENODEFUNC162 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void ChangeCell()">
			<flags>1</flags>
		</SENSENODEFUNC162>
		<SENSENODEFUNC163 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void Cell_pic_Paint(object,PaintEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC163>
		<SENSENODEFUNC164 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void CellDetail_Load(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC164>
		<SENSENODEFUNC165 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void PreBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC165>
		<SENSENODEFUNC166 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void NextBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC166>
		<SENSENODEFUNC167 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void btnsave_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC167>
		<SENSENODEFUNC168 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void btnsave_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC168>
		<SENSENODEFUNC169 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void rab_headshape_CheckedChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC169>
		<SENSENODEFUNC170 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void rabacrosome_CheckedChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC170>
		<SENSENODEFUNC171 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void chkkernelType8_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC171>
		<SENSENODEFUNC172 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void rabtailType_ValueChanged(object,bool)">
			<flags>1</flags>
		</SENSENODEFUNC172>
		<SENSENODEFUNC173 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void rabtailType_CheckedChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC173>
		<SENSENODEFUNC174 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void chkmiddletype1_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC174>
		<SENSENODEFUNC175 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void uiImageButton1_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC175>
		<SENSENODEFUNC176 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void OriginalPicPanel_MouseDown(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC176>
		<SENSENODEFUNC177 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void OriginalPicPanel_MouseMove(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC177>
		<SENSENODEFUNC178 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void OriginalPicPanel_MouseUp(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC178>
		<SENSENODEFUNC179 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC179>
		<SENSENODEFUNC180 type="function" name="SpermFormAnalysis.PageForm::CellDetail:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC180>
		<SENSENODEFUNC181 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC181>
		<SENSENODEFUNC182 type="function" name="SpermFormAnalysis.PageForm::CellDetails:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC182>
		<SENSENODEFUNC183 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void .ctor(List`1&lt;Samplephoto&gt;,int,string,Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC183>
		<SENSENODEFUNC184 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void InitializeComponent2()">
			<flags>1</flags>
		</SENSENODEFUNC184>
		<SENSENODEFUNC185 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void ChangePic()">
			<flags>1</flags>
		</SENSENODEFUNC185>
		<SENSENODEFUNC186 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void OriginalPicPanel_Paint(object,PaintEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC186>
		<SENSENODEFUNC187 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void OriginalPicPanel_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC187>
		<SENSENODEFUNC188 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void ChangeCell()">
			<flags>1</flags>
		</SENSENODEFUNC188>
		<SENSENODEFUNC189 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void Cell_pic_Paint(object,PaintEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC189>
		<SENSENODEFUNC190 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void CellDetails_Load(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC190>
		<SENSENODEFUNC191 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void PreBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC191>
		<SENSENODEFUNC192 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void NextBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC192>
		<SENSENODEFUNC193 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void btnsave_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC193>
		<SENSENODEFUNC194 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void btnsave_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC194>
		<SENSENODEFUNC195 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void rab_headshape_CheckedChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC195>
		<SENSENODEFUNC196 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void rabacrosome_CheckedChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC196>
		<SENSENODEFUNC197 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void chkkernelType_ValueChanged(object,bool)">
			<flags>1</flags>
		</SENSENODEFUNC197>
		<SENSENODEFUNC198 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void chkmiddletype_ValueChanged(object,bool)">
			<flags>1</flags>
		</SENSENODEFUNC198>
		<SENSENODEFUNC199 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void rabtailType_CheckedChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC199>
		<SENSENODEFUNC200 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC200>
		<SENSENODEFUNC201 type="function" name="SpermFormAnalysis.PageForm::CellDetails:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC201>
		<SENSENODEFUNC202 type="function" name="SpermFormAnalysis.PageForm::MorTests:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC202>
		<SENSENODEFUNC203 type="function" name="SpermFormAnalysis.PageForm::MorTests:void btn_Add_FCS_Click(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC203>
		<SENSENODEFUNC204 type="function" name="SpermFormAnalysis.PageForm::MorTests:void __CloseStream()">
			<flags>1</flags>
		</SENSENODEFUNC204>
		<SENSENODEFUNC205 type="function" name="SpermFormAnalysis.PageForm::MorTests:void __CloseDevice()">
			<flags>1</flags>
		</SENSENODEFUNC205>
		<SENSENODEFUNC206 type="function" name="SpermFormAnalysis.PageForm::MorTests:void __InitDevice()">
			<flags>1</flags>
		</SENSENODEFUNC206>
		<SENSENODEFUNC207 type="function" name="SpermFormAnalysis.PageForm::MorTests:void __SetEnumValue(string,string,IGXFeatureControl)">
			<flags>1</flags>
		</SENSENODEFUNC207>
		<SENSENODEFUNC208 type="function" name="SpermFormAnalysis.PageForm::MorTests:void __InitUI()">
			<flags>1</flags>
		</SENSENODEFUNC208>
		<SENSENODEFUNC209 type="function" name="SpermFormAnalysis.PageForm::MorTests:void __CaptureCallbackPro(object,IFrameData)">
			<flags>1</flags>
		</SENSENODEFUNC209>
		<SENSENODEFUNC210 type="function" name="SpermFormAnalysis.PageForm::MorTests:void ImageShowAndSave(IFrameData,string)">
			<flags>1</flags>
		</SENSENODEFUNC210>
		<SENSENODEFUNC211 type="function" name="SpermFormAnalysis.PageForm::MorTests:GX_VALID_BIT_LIST __GetBestValudBit(GX_PIXEL_FORMAT_ENTRY)">
			<flags>1</flags>
		</SENSENODEFUNC211>
		<SENSENODEFUNC212 type="function" name="SpermFormAnalysis.PageForm::MorTests:void ShowWaitFormMethod(string)">
			<flags>1</flags>
		</SENSENODEFUNC212>
		<SENSENODEFUNC213 type="function" name="SpermFormAnalysis.PageForm::MorTests:void HideWaitFormMethod()">
			<flags>1</flags>
		</SENSENODEFUNC213>
		<SENSENODEFUNC214 type="function" name="SpermFormAnalysis.PageForm::MorTests:void MorTests_Load(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC214>
		<SENSENODEFUNC215 type="function" name="SpermFormAnalysis.PageForm::MorTests:void Preload()">
			<flags>1</flags>
		</SENSENODEFUNC215>
		<SENSENODEFUNC216 type="function" name="SpermFormAnalysis.PageForm::MorTests:string get_selectSampleIds()">
			<flags>1</flags>
		</SENSENODEFUNC216>
		<SENSENODEFUNC217 type="function" name="SpermFormAnalysis.PageForm::MorTests:void set_selectSampleIds(string)">
			<flags>1</flags>
		</SENSENODEFUNC217>
		<SENSENODEFUNC218 type="function" name="SpermFormAnalysis.PageForm::MorTests:void processingPhotoEvent(Samplephoto)">
			<flags>1</flags>
		</SENSENODEFUNC218>
		<SENSENODEFUNC219 type="function" name="SpermFormAnalysis.PageForm::MorTests:void processedPhotoEvent(Samplephoto)">
			<flags>1</flags>
		</SENSENODEFUNC219>
		<SENSENODEFUNC220 type="function" name="SpermFormAnalysis.PageForm::MorTests:void RefResultUI()">
			<flags>1</flags>
		</SENSENODEFUNC220>
		<SENSENODEFUNC221 type="function" name="SpermFormAnalysis.PageForm::MorTests:void AddPhotos(string[])">
			<flags>1</flags>
		</SENSENODEFUNC221>
		<SENSENODEFUNC222 type="function" name="SpermFormAnalysis.PageForm::MorTests:void AddPhotos(string[],Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC222>
		<SENSENODEFUNC223 type="function" name="SpermFormAnalysis.PageForm::MorTests:void do_addPhotos(string[])">
			<flags>1</flags>
		</SENSENODEFUNC223>
		<SENSENODEFUNC224 type="function" name="SpermFormAnalysis.PageForm::MorTests:void do_addPhotos(string[],Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC224>
		<SENSENODEFUNC225 type="function" name="SpermFormAnalysis.PageForm::MorTests:void saveThumbnail(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC225>
		<SENSENODEFUNC226 type="function" name="SpermFormAnalysis.PageForm::MorTests:void newPhotoEvent(string)">
			<flags>1</flags>
		</SENSENODEFUNC226>
		<SENSENODEFUNC227 type="function" name="SpermFormAnalysis.PageForm::MorTests:void newPhotoEvent(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC227>
		<SENSENODEFUNC228 type="function" name="SpermFormAnalysis.PageForm::MorTests:void refreshPhotos()">
			<flags>1</flags>
		</SENSENODEFUNC228>
		<SENSENODEFUNC229 type="function" name="SpermFormAnalysis.PageForm::MorTests:void newBtn_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC229>
		<SENSENODEFUNC230 type="function" name="SpermFormAnalysis.PageForm::MorTests:void updatestu()">
			<flags>1</flags>
		</SENSENODEFUNC230>
		<SENSENODEFUNC231 type="function" name="SpermFormAnalysis.PageForm::MorTests:void collectBtn_Click(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC231>
		<SENSENODEFUNC232 type="function" name="SpermFormAnalysis.PageForm::MorTests:void catchPic()">
			<flags>1</flags>
		</SENSENODEFUNC232>
		<SENSENODEFUNC233 type="function" name="SpermFormAnalysis.PageForm::MorTests:void Picture_doubleClick(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC233>
		<SENSENODEFUNC234 type="function" name="SpermFormAnalysis.PageForm::MorTests:void uiSymbolButton1_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC234>
		<SENSENODEFUNC235 type="function" name="SpermFormAnalysis.PageForm::MorTests:void btn_Cal_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC235>
		<SENSENODEFUNC236 type="function" name="SpermFormAnalysis.PageForm::MorTests:void btn_Print_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC236>
		<SENSENODEFUNC237 type="function" name="SpermFormAnalysis.PageForm::MorTests:void KeyDownEvent(object,KeyEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC237>
		<SENSENODEFUNC238 type="function" name="SpermFormAnalysis.PageForm::MorTests:void MorTest_KeyDown1(object,KeyEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC238>
		<SENSENODEFUNC239 type="function" name="SpermFormAnalysis.PageForm::MorTests:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC239>
		<SENSENODEFUNC240 type="function" name="SpermFormAnalysis.PageForm::MorTests:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC240>
		<SENSENODEFUNC241 type="function" name="SpermFormAnalysis.PageForm::MorTests:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC241>
		<SENSENODEFUNC242 type="function" name="SpermFormAnalysis.PageForm::MorTests:&lt;&gt;c:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC242>
		<SENSENODEFUNC243 type="function" name="SpermFormAnalysis.PageForm::Sample:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC243>
		<SENSENODEFUNC244 type="function" name="SpermFormAnalysis.PageForm::Sample:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC244>
		<SENSENODEFUNC245 type="function" name="SpermFormAnalysis.PageForm::Sample:void .ctor(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC245>
		<SENSENODEFUNC246 type="function" name="SpermFormAnalysis.PageForm::Sample:void Init1()">
			<flags>1</flags>
		</SENSENODEFUNC246>
		<SENSENODEFUNC247 type="function" name="SpermFormAnalysis.PageForm::Sample:void okBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC247>
		<SENSENODEFUNC248 type="function" name="SpermFormAnalysis.PageForm::Sample:bool checkTextString(string,string,int)">
			<flags>1</flags>
		</SENSENODEFUNC248>
		<SENSENODEFUNC249 type="function" name="SpermFormAnalysis.PageForm::Sample:bool checkInput()">
			<flags>1</flags>
		</SENSENODEFUNC249>
		<SENSENODEFUNC250 type="function" name="SpermFormAnalysis.PageForm::Sample:DialogResult _ShowDialog()">
			<flags>1</flags>
		</SENSENODEFUNC250>
		<SENSENODEFUNC251 type="function" name="SpermFormAnalysis.PageForm::Sample:void resetBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC251>
		<SENSENODEFUNC252 type="function" name="SpermFormAnalysis.PageForm::Sample:void cancelBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC252>
		<SENSENODEFUNC253 type="function" name="SpermFormAnalysis.PageForm::Sample:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC253>
		<SENSENODEFUNC254 type="function" name="SpermFormAnalysis.PageForm::Sample:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC254>
		<SENSENODEFUNC255 type="function" name="SpermFormAnalysis.PageForm::Searchs:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC255>
		<SENSENODEFUNC256 type="function" name="SpermFormAnalysis.PageForm::Searchs:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC256>
		<SENSENODEFUNC257 type="function" name="SpermFormAnalysis.PageForm::Searchs:void init()">
			<flags>1</flags>
		</SENSENODEFUNC257>
		<SENSENODEFUNC258 type="function" name="SpermFormAnalysis.PageForm::Searchs:void .ctor(Mains)">
			<flags>1</flags>
		</SENSENODEFUNC258>
		<SENSENODEFUNC259 type="function" name="SpermFormAnalysis.PageForm::Searchs:void SearchFlush()">
			<flags>1</flags>
		</SENSENODEFUNC259>
		<SENSENODEFUNC260 type="function" name="SpermFormAnalysis.PageForm::Searchs:void Form1_MouseWheel(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC260>
		<SENSENODEFUNC261 type="function" name="SpermFormAnalysis.PageForm::Searchs:void BindDataGridView()">
			<flags>1</flags>
		</SENSENODEFUNC261>
		<SENSENODEFUNC262 type="function" name="SpermFormAnalysis.PageForm::Searchs:void dataGridView1_CellClick(object,DataGridViewCellEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC262>
		<SENSENODEFUNC263 type="function" name="SpermFormAnalysis.PageForm::Searchs:Bitmap GetBitmap(int)">
			<flags>1</flags>
		</SENSENODEFUNC263>
		<SENSENODEFUNC264 type="function" name="SpermFormAnalysis.PageForm::Searchs:void btnsearchmore_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC264>
		<SENSENODEFUNC265 type="function" name="SpermFormAnalysis.PageForm::Searchs:void btnshouqi_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC265>
		<SENSENODEFUNC266 type="function" name="SpermFormAnalysis.PageForm::Searchs:void searchBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC266>
		<SENSENODEFUNC267 type="function" name="SpermFormAnalysis.PageForm::Searchs:void btn_PrintRepor_MouseClick(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC267>
		<SENSENODEFUNC268 type="function" name="SpermFormAnalysis.PageForm::Searchs:void btn_Delete_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC268>
		<SENSENODEFUNC269 type="function" name="SpermFormAnalysis.PageForm::Searchs:void resetBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC269>
		<SENSENODEFUNC270 type="function" name="SpermFormAnalysis.PageForm::Searchs:void uiSymbolButton8_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC270>
		<SENSENODEFUNC271 type="function" name="SpermFormAnalysis.PageForm::Searchs:void EditInfoBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC271>
		<SENSENODEFUNC272 type="function" name="SpermFormAnalysis.PageForm::Searchs:void ybpanel_Layout(object,LayoutEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC272>
		<SENSENODEFUNC273 type="function" name="SpermFormAnalysis.PageForm::Searchs:void ybpanel_MouseDown(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC273>
		<SENSENODEFUNC274 type="function" name="SpermFormAnalysis.PageForm::Searchs:void ybpanel_MouseMove(object,MouseEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC274>
		<SENSENODEFUNC275 type="function" name="SpermFormAnalysis.PageForm::Searchs:void ybpanel_Paint(object,PaintEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC275>
		<SENSENODEFUNC276 type="function" name="SpermFormAnalysis.PageForm::Searchs:void uiComboBox2_SelectedIndexChanged(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC276>
		<SENSENODEFUNC277 type="function" name="SpermFormAnalysis.PageForm::Searchs:void Searchs_Load(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC277>
		<SENSENODEFUNC278 type="function" name="SpermFormAnalysis.PageForm::Searchs:void uiDoubleUpDown1_ValueChanged(object,double)">
			<flags>1</flags>
		</SENSENODEFUNC278>
		<SENSENODEFUNC279 type="function" name="SpermFormAnalysis.PageForm::Searchs:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC279>
		<SENSENODEFUNC280 type="function" name="SpermFormAnalysis.PageForm::Searchs:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC280>
		<SENSENODEFUNC281 type="function" name="SpermFormAnalysis.PageForm::Searchs:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC281>
		<SENSENODEFUNC282 type="function" name="SpermFormAnalysis.PageForm::AddUser:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC282>
		<SENSENODEFUNC283 type="function" name="SpermFormAnalysis.PageForm::AddUser:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC283>
		<SENSENODEFUNC284 type="function" name="SpermFormAnalysis.PageForm::AddUser:bool CheckData()">
			<flags>1</flags>
		</SENSENODEFUNC284>
		<SENSENODEFUNC285 type="function" name="SpermFormAnalysis.PageForm::AddUser:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC285>
		<SENSENODEFUNC286 type="function" name="SpermFormAnalysis.PageForm::AddUser:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC286>
		<SENSENODEFUNC287 type="function" name="SpermFormAnalysis.PageForm::Setting:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC287>
		<SENSENODEFUNC288 type="function" name="SpermFormAnalysis.PageForm::Setting:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC288>
		<SENSENODEFUNC289 type="function" name="SpermFormAnalysis.PageForm::Setting:void btn3_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC289>
		<SENSENODEFUNC290 type="function" name="SpermFormAnalysis.PageForm::Setting:void Setting_Load(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC290>
		<SENSENODEFUNC291 type="function" name="SpermFormAnalysis.PageForm::Setting:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC291>
		<SENSENODEFUNC292 type="function" name="SpermFormAnalysis.PageForm::Setting:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC292>
		<SENSENODEFUNC293 type="function" name="SpermFormAnalysis.PageForm::UserManage:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC293>
		<SENSENODEFUNC294 type="function" name="SpermFormAnalysis.PageForm::UserManage:CreateParams get_CreateParams()">
			<flags>1</flags>
		</SENSENODEFUNC294>
		<SENSENODEFUNC295 type="function" name="SpermFormAnalysis.PageForm::UserManage:void addBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC295>
		<SENSENODEFUNC296 type="function" name="SpermFormAnalysis.PageForm::UserManage:void editBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC296>
		<SENSENODEFUNC297 type="function" name="SpermFormAnalysis.PageForm::UserManage:void delBtn_Click(object,EventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC297>
		<SENSENODEFUNC298 type="function" name="SpermFormAnalysis.PageForm::UserManage:void dataGrid_Admin_CellFormatting(object,DataGridViewCellFormattingEventArgs)">
			<flags>1</flags>
		</SENSENODEFUNC298>
		<SENSENODEFUNC299 type="function" name="SpermFormAnalysis.PageForm::UserManage:void Dispose(bool)">
			<flags>1</flags>
		</SENSENODEFUNC299>
		<SENSENODEFUNC300 type="function" name="SpermFormAnalysis.PageForm::UserManage:void InitializeComponent()">
			<flags>1</flags>
		</SENSENODEFUNC300>
		<SENSENODEFUNC301 type="function" name="SpermFormAnalysis.PageForm::UserManage:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC301>
		<SENSENODEFUNC302 type="function" name="SpermFormAnalysis.Data::DES_:void .ctor(string)">
			<flags>1</flags>
		</SENSENODEFUNC302>
		<SENSENODEFUNC303 type="function" name="SpermFormAnalysis.Data::DES_:void .ctor(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC303>
		<SENSENODEFUNC304 type="function" name="SpermFormAnalysis.Data::DES_:byte[] GetLegalKey()">
			<flags>1</flags>
		</SENSENODEFUNC304>
		<SENSENODEFUNC305 type="function" name="SpermFormAnalysis.Data::DES_:byte[] GetLegalIV()">
			<flags>1</flags>
		</SENSENODEFUNC305>
		<SENSENODEFUNC306 type="function" name="SpermFormAnalysis.Data::DES_:string Encrypt(string)">
			<flags>1</flags>
		</SENSENODEFUNC306>
		<SENSENODEFUNC307 type="function" name="SpermFormAnalysis.Data::DES_:string Decrypt(string)">
			<flags>1</flags>
		</SENSENODEFUNC307>
		<SENSENODEFUNC308 type="function" name="SpermFormAnalysis.Data::DES_:byte[] Encrypt(byte[])">
			<flags>1</flags>
		</SENSENODEFUNC308>
		<SENSENODEFUNC309 type="function" name="SpermFormAnalysis.Data::DES_:byte[] Decrypt(byte[])">
			<flags>1</flags>
		</SENSENODEFUNC309>
		<SENSENODEFUNC310 type="function" name="SpermFormAnalysis.Data::DES_:void Encrypt(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC310>
		<SENSENODEFUNC311 type="function" name="SpermFormAnalysis.Data::DES_:void Decrypt(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC311>
		<SENSENODEFUNC312 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:Admin GetAdmin(string)">
			<flags>1</flags>
		</SENSENODEFUNC312>
		<SENSENODEFUNC313 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:List`1&lt;Admin&gt; GetObjects()">
			<flags>1</flags>
		</SENSENODEFUNC313>
		<SENSENODEFUNC314 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:bool CreateObject(Admin)">
			<flags>1</flags>
		</SENSENODEFUNC314>
		<SENSENODEFUNC315 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:bool UpdateObject(Admin)">
			<flags>1</flags>
		</SENSENODEFUNC315>
		<SENSENODEFUNC316 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:bool DeleteObject(string)">
			<flags>1</flags>
		</SENSENODEFUNC316>
		<SENSENODEFUNC317 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:bool IsExist(string)">
			<flags>1</flags>
		</SENSENODEFUNC317>
		<SENSENODEFUNC318 type="function" name="SpermFormAnalysis.DataHelper::AdminServices:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC318>
		<SENSENODEFUNC319 type="function" name="SpermFormAnalysis.DataHelper::MrfrulesServices:Mrfrules GetObject(long)">
			<flags>1</flags>
		</SENSENODEFUNC319>
		<SENSENODEFUNC320 type="function" name="SpermFormAnalysis.DataHelper::MrfrulesServices:ObservableCollection`1&lt;Mrfrules&gt; GetObjects()">
			<flags>1</flags>
		</SENSENODEFUNC320>
		<SENSENODEFUNC321 type="function" name="SpermFormAnalysis.DataHelper::MrfrulesServices:bool UpdateObject(Mrfrules)">
			<flags>1</flags>
		</SENSENODEFUNC321>
		<SENSENODEFUNC322 type="function" name="SpermFormAnalysis.DataHelper::MrfrulesServices:bool InsertMrfRules(Mrfrules)">
			<flags>1</flags>
		</SENSENODEFUNC322>
		<SENSENODEFUNC323 type="function" name="SpermFormAnalysis.DataHelper::MrfrulesServices:bool ExistMrfRules(Mrfrules)">
			<flags>1</flags>
		</SENSENODEFUNC323>
		<SENSENODEFUNC324 type="function" name="SpermFormAnalysis.DataHelper::MrfrulesServices:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC324>
		<SENSENODEFUNC325 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:Sampleinfo GetObject(int)">
			<flags>1</flags>
		</SENSENODEFUNC325>
		<SENSENODEFUNC326 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:Sampleinfo GetLastObject()">
			<flags>1</flags>
		</SENSENODEFUNC326>
		<SENSENODEFUNC327 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:Sampleinfo GetLastObjectByPro0()">
			<flags>1</flags>
		</SENSENODEFUNC327>
		<SENSENODEFUNC328 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:ObservableCollection`1&lt;Sampleinfo&gt; getSampleInfo(string)">
			<flags>1</flags>
		</SENSENODEFUNC328>
		<SENSENODEFUNC329 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool IsExist(string)">
			<flags>1</flags>
		</SENSENODEFUNC329>
		<SENSENODEFUNC330 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool ExistSample(int)">
			<flags>1</flags>
		</SENSENODEFUNC330>
		<SENSENODEFUNC331 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool ExistSampleIdForEdit(string,int)">
			<flags>1</flags>
		</SENSENODEFUNC331>
		<SENSENODEFUNC332 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool CreateObject(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC332>
		<SENSENODEFUNC333 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool CreateTestObject()">
			<flags>1</flags>
		</SENSENODEFUNC333>
		<SENSENODEFUNC334 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool CreateObjectBase(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC334>
		<SENSENODEFUNC335 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool UpdateObject(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC335>
		<SENSENODEFUNC336 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool DeleteObject(int)">
			<flags>1</flags>
		</SENSENODEFUNC336>
		<SENSENODEFUNC337 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:ObservableCollection`1&lt;Sampleinfo&gt; GetSampleInfo()">
			<flags>1</flags>
		</SENSENODEFUNC337>
		<SENSENODEFUNC338 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:ObservableCollection`1&lt;Sampleinfo&gt; GetSampleInfo(string)">
			<flags>1</flags>
		</SENSENODEFUNC338>
		<SENSENODEFUNC339 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:ObservableCollection`1&lt;Sampleinfo&gt; GetSampleInfoByPage(string,int,int)">
			<flags>1</flags>
		</SENSENODEFUNC339>
		<SENSENODEFUNC340 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:int GetSampleCount(string)">
			<flags>1</flags>
		</SENSENODEFUNC340>
		<SENSENODEFUNC341 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:ObservableCollection`1&lt;Sampleinfo&gt; GetModelObjects(string)">
			<flags>1</flags>
		</SENSENODEFUNC341>
		<SENSENODEFUNC342 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool UpdateSampleProcessed(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC342>
		<SENSENODEFUNC343 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool UpdateSampleProcessed(Sampleinfo,int)">
			<flags>1</flags>
		</SENSENODEFUNC343>
		<SENSENODEFUNC344 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool UpdateSampleProcessedA(Sampleinfo)">
			<flags>1</flags>
		</SENSENODEFUNC344>
		<SENSENODEFUNC345 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:bool UpdateSampleStatus(Sampleinfo,int)">
			<flags>1</flags>
		</SENSENODEFUNC345>
		<SENSENODEFUNC346 type="function" name="SpermFormAnalysis.DataHelper::SampleinfoServices:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC346>
		<SENSENODEFUNC347 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:Samplephoto GetObject(int)">
			<flags>1</flags>
		</SENSENODEFUNC347>
		<SENSENODEFUNC348 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:ObservableCollection`1&lt;Samplephoto&gt; GetMRFTaskList(string)">
			<flags>1</flags>
		</SENSENODEFUNC348>
		<SENSENODEFUNC349 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:ObservableCollection`1&lt;Samplephoto&gt; GetSamplePhoto(string)">
			<flags>1</flags>
		</SENSENODEFUNC349>
		<SENSENODEFUNC350 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:ObservableCollection`1&lt;Samplephoto&gt; GetModelObjects(string)">
			<flags>1</flags>
		</SENSENODEFUNC350>
		<SENSENODEFUNC351 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:int GetMaxBlockNum(string)">
			<flags>1</flags>
		</SENSENODEFUNC351>
		<SENSENODEFUNC352 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:bool UpdatePhotoProcessed(Samplephoto,int)">
			<flags>1</flags>
		</SENSENODEFUNC352>
		<SENSENODEFUNC353 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:bool AddPhoto(Samplephoto)">
			<flags>1</flags>
		</SENSENODEFUNC353>
		<SENSENODEFUNC354 type="function" name="SpermFormAnalysis.DataHelper::SamplephotoServices:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC354>
		<SENSENODEFUNC355 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:Sperm GetObject(int)">
			<flags>1</flags>
		</SENSENODEFUNC355>
		<SENSENODEFUNC356 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetObjects(int)">
			<flags>1</flags>
		</SENSENODEFUNC356>
		<SENSENODEFUNC357 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetObjects(string)">
			<flags>1</flags>
		</SENSENODEFUNC357>
		<SENSENODEFUNC358 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetNewsObjects(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC358>
		<SENSENODEFUNC359 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; AddNewSperms(ObservableCollection`1&lt;Sperm&gt;,ObservableCollection`1&lt;Sperm&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC359>
		<SENSENODEFUNC360 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetnormalSperms(string)">
			<flags>1</flags>
		</SENSENODEFUNC360>
		<SENSENODEFUNC361 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetabnormalSperms(string)">
			<flags>1</flags>
		</SENSENODEFUNC361>
		<SENSENODEFUNC362 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetsubclinicalSperms(string)">
			<flags>1</flags>
		</SENSENODEFUNC362>
		<SENSENODEFUNC363 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:bool UpdateHandlerSperm(Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC363>
		<SENSENODEFUNC364 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetSpermsByType(DateTime,DateTime,int,int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC364>
		<SENSENODEFUNC365 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetAbnormalSpermsByType(int,int,int,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC365>
		<SENSENODEFUNC366 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:long CreateObject(Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC366>
		<SENSENODEFUNC367 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:ObservableCollection`1&lt;Sperm&gt; GetModelObjects(string)">
			<flags>1</flags>
		</SENSENODEFUNC367>
		<SENSENODEFUNC368 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:bool DelSperm(string)">
			<flags>1</flags>
		</SENSENODEFUNC368>
		<SENSENODEFUNC369 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:bool DelOneSperm(int)">
			<flags>1</flags>
		</SENSENODEFUNC369>
		<SENSENODEFUNC370 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:List`1&lt;Point&gt; stringToPointArray(string)">
			<flags>1</flags>
		</SENSENODEFUNC370>
		<SENSENODEFUNC371 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:List`1&lt;Point[]&gt; stringToListPointArray(string)">
			<flags>1</flags>
		</SENSENODEFUNC371>
		<SENSENODEFUNC372 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:string pointArrayToString(Point[])">
			<flags>1</flags>
		</SENSENODEFUNC372>
		<SENSENODEFUNC373 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:string listPointArrayToString(List`1&lt;Point[]&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC373>
		<SENSENODEFUNC374 type="function" name="SpermFormAnalysis.DataHelper::SpermServices:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC374>
		<SENSENODEFUNC375 type="function" name="SpermFormAnalysis.common::Drawing:void add_RaiseSelectedSpermChangedEvent(HandleSelectedSpermChanged)">
			<flags>1</flags>
		</SENSENODEFUNC375>
		<SENSENODEFUNC376 type="function" name="SpermFormAnalysis.common::Drawing:void remove_RaiseSelectedSpermChangedEvent(HandleSelectedSpermChanged)">
			<flags>1</flags>
		</SENSENODEFUNC376>
		<SENSENODEFUNC377 type="function" name="SpermFormAnalysis.common::Drawing:void add_RaiseDeletedSpermEvent(HandleDeletedSperm)">
			<flags>1</flags>
		</SENSENODEFUNC377>
		<SENSENODEFUNC378 type="function" name="SpermFormAnalysis.common::Drawing:void remove_RaiseDeletedSpermEvent(HandleDeletedSperm)">
			<flags>1</flags>
		</SENSENODEFUNC378>
		<SENSENODEFUNC379 type="function" name="SpermFormAnalysis.common::Drawing:void add_RaiseSetNormalSpermEvent(HandleSetNormalSperm)">
			<flags>1</flags>
		</SENSENODEFUNC379>
		<SENSENODEFUNC380 type="function" name="SpermFormAnalysis.common::Drawing:void remove_RaiseSetNormalSpermEvent(HandleSetNormalSperm)">
			<flags>1</flags>
		</SENSENODEFUNC380>
		<SENSENODEFUNC381 type="function" name="SpermFormAnalysis.common::Drawing:void add_RaiseSetAbnormalSpermEvent(HandleSetAbnormalSperm)">
			<flags>1</flags>
		</SENSENODEFUNC381>
		<SENSENODEFUNC382 type="function" name="SpermFormAnalysis.common::Drawing:void remove_RaiseSetAbnormalSpermEvent(HandleSetAbnormalSperm)">
			<flags>1</flags>
		</SENSENODEFUNC382>
		<SENSENODEFUNC383 type="function" name="SpermFormAnalysis.common::Drawing:string GetToolTip(Sperm)">
			<flags>1</flags>
		</SENSENODEFUNC383>
		<SENSENODEFUNC384 type="function" name="SpermFormAnalysis.common::Drawing:void DrawEllips(Graphics,float,float,List`1&lt;Sperm&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC384>
		<SENSENODEFUNC385 type="function" name="SpermFormAnalysis.common::Drawing:void DrawPolygons(float,float,Graphics,float,float,List`1&lt;Sperm&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC385>
		<SENSENODEFUNC386 type="function" name="SpermFormAnalysis.common::Drawing:void DrawVacuoles(float,float,Graphics,float,float,List`1&lt;Sperm&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC386>
		<SENSENODEFUNC387 type="function" name="SpermFormAnalysis.common::Drawing:void DrawTails(float,float,Graphics,float,float,List`1&lt;Sperm&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC387>
		<SENSENODEFUNC388 type="function" name="SpermFormAnalysis.common::Drawing:void DrawBoxs(float,float,Graphics,float,float,List`1&lt;Sperm&gt;)">
			<flags>1</flags>
		</SENSENODEFUNC388>
		<SENSENODEFUNC389 type="function" name="SpermFormAnalysis.Class::CardMag:bool ReadCard(string,int)">
			<flags>1</flags>
		</SENSENODEFUNC389>
		<SENSENODEFUNC390 type="function" name="SpermFormAnalysis.Class::CardMag:bool WriteCard(string,int)">
			<flags>1</flags>
		</SENSENODEFUNC390>
		<SENSENODEFUNC391 type="function" name="SpermFormAnalysis.Class::CardMag:bool WriteCard2(string,int)">
			<flags>1</flags>
		</SENSENODEFUNC391>
		<SENSENODEFUNC392 type="function" name="SpermFormAnalysis.Class::CardMag:List`1&lt;InterfCard&gt; GetInterfCard()">
			<flags>1</flags>
		</SENSENODEFUNC392>
		<SENSENODEFUNC393 type="function" name="SpermFormAnalysis.Class::CardMag:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC393>
		<SENSENODEFUNC394 type="function" name="SpermFormAnalysis.Class::CardMag:&lt;&gt;c:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC394>
		<SENSENODEFUNC395 type="function" name="SpermFormAnalysis.Class::clsUtility:string TO32MD5(string)">
			<flags>1</flags>
		</SENSENODEFUNC395>
		<SENSENODEFUNC396 type="function" name="SpermFormAnalysis.Class::clsUtility:List`1&lt;string&gt; getMacAddress()">
			<flags>1</flags>
		</SENSENODEFUNC396>
		<SENSENODEFUNC397 type="function" name="SpermFormAnalysis.Class::clsUtility:string GetDiskID()">
			<flags>1</flags>
		</SENSENODEFUNC397>
		<SENSENODEFUNC398 type="function" name="SpermFormAnalysis.Class::clsUtility:string GetCpuID()">
			<flags>1</flags>
		</SENSENODEFUNC398>
		<SENSENODEFUNC399 type="function" name="SpermFormAnalysis.Class::clsUtility:List`1&lt;string&gt; GetDiskIDs()">
			<flags>1</flags>
		</SENSENODEFUNC399>
		<SENSENODEFUNC400 type="function" name="SpermFormAnalysis.Class::clsUtility:string parseSerialFromDeviceID(string)">
			<flags>1</flags>
		</SENSENODEFUNC400>
		<SENSENODEFUNC401 type="function" name="SpermFormAnalysis.Class::clsUtility:string getValueInQuotes(string)">
			<flags>1</flags>
		</SENSENODEFUNC401>
		<SENSENODEFUNC402 type="function" name="SpermFormAnalysis.Class::clsUtility:string getMD5Encrypt(string)">
			<flags>1</flags>
		</SENSENODEFUNC402>
		<SENSENODEFUNC403 type="function" name="SpermFormAnalysis.Class::clsUtility:bool validComputer()">
			<flags>1</flags>
		</SENSENODEFUNC403>
		<SENSENODEFUNC404 type="function" name="SpermFormAnalysis.Class::clsUtility:bool validComputerByDiskCPU()">
			<flags>1</flags>
		</SENSENODEFUNC404>
		<SENSENODEFUNC405 type="function" name="SpermFormAnalysis.Class::clsUtility:bool validTime()">
			<flags>1</flags>
		</SENSENODEFUNC405>
		<SENSENODEFUNC406 type="function" name="SpermFormAnalysis.Class::clsUtility:string genKey()">
			<flags>1</flags>
		</SENSENODEFUNC406>
		<SENSENODEFUNC407 type="function" name="SpermFormAnalysis.Class::clsUtility:string genKeyByDisk()">
			<flags>1</flags>
		</SENSENODEFUNC407>
		<SENSENODEFUNC408 type="function" name="SpermFormAnalysis.Class::clsUtility:List`1&lt;string&gt; genKeyByDisks()">
			<flags>1</flags>
		</SENSENODEFUNC408>
		<SENSENODEFUNC409 type="function" name="SpermFormAnalysis.Class::clsUtility:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC409>
		<SENSENODEFUNC410 type="function" name="SpermFormAnalysis.Class::common:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC410>
		<SENSENODEFUNC411 type="function" name="SpermFormAnalysis.Class::GlobalProperty:bool get_IsLogin()">
			<flags>1</flags>
		</SENSENODEFUNC411>
		<SENSENODEFUNC412 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Admin_ID()">
			<flags>1</flags>
		</SENSENODEFUNC412>
		<SENSENODEFUNC413 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Admin_ID(string)">
			<flags>1</flags>
		</SENSENODEFUNC413>
		<SENSENODEFUNC414 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Admin_Name()">
			<flags>1</flags>
		</SENSENODEFUNC414>
		<SENSENODEFUNC415 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Admin_PWD()">
			<flags>1</flags>
		</SENSENODEFUNC415>
		<SENSENODEFUNC416 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Admin_PWD(string)">
			<flags>1</flags>
		</SENSENODEFUNC416>
		<SENSENODEFUNC417 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Admin_Level()">
			<flags>1</flags>
		</SENSENODEFUNC417>
		<SENSENODEFUNC418 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_AutCod()">
			<flags>1</flags>
		</SENSENODEFUNC418>
		<SENSENODEFUNC419 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_AutCod(string)">
			<flags>1</flags>
		</SENSENODEFUNC419>
		<SENSENODEFUNC420 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Id()">
			<flags>1</flags>
		</SENSENODEFUNC420>
		<SENSENODEFUNC421 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Id(string)">
			<flags>1</flags>
		</SENSENODEFUNC421>
		<SENSENODEFUNC422 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Hospital()">
			<flags>1</flags>
		</SENSENODEFUNC422>
		<SENSENODEFUNC423 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Hospital(string)">
			<flags>1</flags>
		</SENSENODEFUNC423>
		<SENSENODEFUNC424 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Ks()">
			<flags>1</flags>
		</SENSENODEFUNC424>
		<SENSENODEFUNC425 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Ks(string)">
			<flags>1</flags>
		</SENSENODEFUNC425>
		<SENSENODEFUNC426 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_BalanceWhite()">
			<flags>1</flags>
		</SENSENODEFUNC426>
		<SENSENODEFUNC427 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_BalanceWhite(string)">
			<flags>1</flags>
		</SENSENODEFUNC427>
		<SENSENODEFUNC428 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_ExposureAuto()">
			<flags>1</flags>
		</SENSENODEFUNC428>
		<SENSENODEFUNC429 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_ExposureAuto(string)">
			<flags>1</flags>
		</SENSENODEFUNC429>
		<SENSENODEFUNC430 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_GainAuto()">
			<flags>1</flags>
		</SENSENODEFUNC430>
		<SENSENODEFUNC431 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_GainAuto(string)">
			<flags>1</flags>
		</SENSENODEFUNC431>
		<SENSENODEFUNC432 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Gain()">
			<flags>1</flags>
		</SENSENODEFUNC432>
		<SENSENODEFUNC433 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Gain(string)">
			<flags>1</flags>
		</SENSENODEFUNC433>
		<SENSENODEFUNC434 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_ExposureTime()">
			<flags>1</flags>
		</SENSENODEFUNC434>
		<SENSENODEFUNC435 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_ExposureTime(string)">
			<flags>1</flags>
		</SENSENODEFUNC435>
		<SENSENODEFUNC436 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_AcquisitionFrameRate()">
			<flags>1</flags>
		</SENSENODEFUNC436>
		<SENSENODEFUNC437 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_AcquisitionFrameRate(string)">
			<flags>1</flags>
		</SENSENODEFUNC437>
		<SENSENODEFUNC438 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_Test_Type()">
			<flags>1</flags>
		</SENSENODEFUNC438>
		<SENSENODEFUNC439 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_Machine_Type()">
			<flags>1</flags>
		</SENSENODEFUNC439>
		<SENSENODEFUNC440 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Test_Type_No()">
			<flags>1</flags>
		</SENSENODEFUNC440>
		<SENSENODEFUNC441 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_CamType()">
			<flags>1</flags>
		</SENSENODEFUNC441>
		<SENSENODEFUNC442 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_StrAutcod()">
			<flags>1</flags>
		</SENSENODEFUNC442>
		<SENSENODEFUNC443 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_Renumber()">
			<flags>1</flags>
		</SENSENODEFUNC443>
		<SENSENODEFUNC444 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_MaxCellNum()">
			<flags>1</flags>
		</SENSENODEFUNC444>
		<SENSENODEFUNC445 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_MaxCellNum(int)">
			<flags>1</flags>
		</SENSENODEFUNC445>
		<SENSENODEFUNC446 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_MaxPic()">
			<flags>1</flags>
		</SENSENODEFUNC446>
		<SENSENODEFUNC447 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_MaxPic(int)">
			<flags>1</flags>
		</SENSENODEFUNC447>
		<SENSENODEFUNC448 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_Ui_Style()">
			<flags>1</flags>
		</SENSENODEFUNC448>
		<SENSENODEFUNC449 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Ui_Style(int)">
			<flags>1</flags>
		</SENSENODEFUNC449>
		<SENSENODEFUNC450 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_Rows()">
			<flags>1</flags>
		</SENSENODEFUNC450>
		<SENSENODEFUNC451 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Rows(int)">
			<flags>1</flags>
		</SENSENODEFUNC451>
		<SENSENODEFUNC452 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_Cols()">
			<flags>1</flags>
		</SENSENODEFUNC452>
		<SENSENODEFUNC453 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Cols(int)">
			<flags>1</flags>
		</SENSENODEFUNC453>
		<SENSENODEFUNC454 type="function" name="SpermFormAnalysis.Class::GlobalProperty:int get_QtrfIndex()">
			<flags>1</flags>
		</SENSENODEFUNC454>
		<SENSENODEFUNC455 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_QtrfIndex(int)">
			<flags>1</flags>
		</SENSENODEFUNC455>
		<SENSENODEFUNC456 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Machine_Code()">
			<flags>1</flags>
		</SENSENODEFUNC456>
		<SENSENODEFUNC457 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Machine_Code(string)">
			<flags>1</flags>
		</SENSENODEFUNC457>
		<SENSENODEFUNC458 type="function" name="SpermFormAnalysis.Class::GlobalProperty:bool get_Remember_User()">
			<flags>1</flags>
		</SENSENODEFUNC458>
		<SENSENODEFUNC459 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Remember_User(bool)">
			<flags>1</flags>
		</SENSENODEFUNC459>
		<SENSENODEFUNC460 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Testers()">
			<flags>1</flags>
		</SENSENODEFUNC460>
		<SENSENODEFUNC461 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Testers(string)">
			<flags>1</flags>
		</SENSENODEFUNC461>
		<SENSENODEFUNC462 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Reviewers()">
			<flags>1</flags>
		</SENSENODEFUNC462>
		<SENSENODEFUNC463 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Reviewers(string)">
			<flags>1</flags>
		</SENSENODEFUNC463>
		<SENSENODEFUNC464 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Doctors()">
			<flags>1</flags>
		</SENSENODEFUNC464>
		<SENSENODEFUNC465 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Doctors(string)">
			<flags>1</flags>
		</SENSENODEFUNC465>
		<SENSENODEFUNC466 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_Mode()">
			<flags>1</flags>
		</SENSENODEFUNC466>
		<SENSENODEFUNC467 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Mode(string)">
			<flags>1</flags>
		</SENSENODEFUNC467>
		<SENSENODEFUNC468 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_HisService()">
			<flags>1</flags>
		</SENSENODEFUNC468>
		<SENSENODEFUNC469 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_HisService(string)">
			<flags>1</flags>
		</SENSENODEFUNC469>
		<SENSENODEFUNC470 type="function" name="SpermFormAnalysis.Class::GlobalProperty:string get_LisInterface()">
			<flags>1</flags>
		</SENSENODEFUNC470>
		<SENSENODEFUNC471 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_LisInterface(string)">
			<flags>1</flags>
		</SENSENODEFUNC471>
		<SENSENODEFUNC472 type="function" name="SpermFormAnalysis.Class::GlobalProperty:bool get_Lis_push_on_calc()">
			<flags>1</flags>
		</SENSENODEFUNC472>
		<SENSENODEFUNC473 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Lis_push_on_calc(bool)">
			<flags>1</flags>
		</SENSENODEFUNC473>
		<SENSENODEFUNC474 type="function" name="SpermFormAnalysis.Class::GlobalProperty:bool get_Create_report_on_calc()">
			<flags>1</flags>
		</SENSENODEFUNC474>
		<SENSENODEFUNC475 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void set_Create_report_on_calc(bool)">
			<flags>1</flags>
		</SENSENODEFUNC475>
		<SENSENODEFUNC476 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void InitParams()">
			<flags>1</flags>
		</SENSENODEFUNC476>
		<SENSENODEFUNC477 type="function" name="SpermFormAnalysis.Class::GlobalProperty:void .cctor()">
			<flags>1</flags>
		</SENSENODEFUNC477>
		<SENSENODEFUNC478 type="function" name="SpermFormAnalysis.Class::GxBitmap:void .ctor(IGXDevice,PictureBox)">
			<flags>1</flags>
		</SENSENODEFUNC478>
		<SENSENODEFUNC479 type="function" name="SpermFormAnalysis.Class::GxBitmap:void ShowImageProcess(IImageProcessConfig,IBaseData)">
			<flags>1</flags>
		</SENSENODEFUNC479>
		<SENSENODEFUNC480 type="function" name="SpermFormAnalysis.Class::GxBitmap:void Show(IBaseData)">
			<flags>1</flags>
		</SENSENODEFUNC480>
		<SENSENODEFUNC481 type="function" name="SpermFormAnalysis.Class::GxBitmap:void SaveBmp(IImageProcessConfig,IBaseData,string)">
			<flags>1</flags>
		</SENSENODEFUNC481>
		<SENSENODEFUNC482 type="function" name="SpermFormAnalysis.Class::GxBitmap:void SaveRaw(IBaseData,string)">
			<flags>1</flags>
		</SENSENODEFUNC482>
		<SENSENODEFUNC483 type="function" name="SpermFormAnalysis.Class::GxBitmap:void __UpdateBufferSize(IBaseData)">
			<flags>1</flags>
		</SENSENODEFUNC483>
		<SENSENODEFUNC484 type="function" name="SpermFormAnalysis.Class::GxBitmap:void __UpdateBitmapForSave(byte[])">
			<flags>1</flags>
		</SENSENODEFUNC484>
		<SENSENODEFUNC485 type="function" name="SpermFormAnalysis.Class::GxBitmap:void __ShowImage(byte[])">
			<flags>1</flags>
		</SENSENODEFUNC485>
		<SENSENODEFUNC486 type="function" name="SpermFormAnalysis.Class::GxBitmap:bool __IsPixelFormat8(GX_PIXEL_FORMAT_ENTRY)">
			<flags>1</flags>
		</SENSENODEFUNC486>
		<SENSENODEFUNC487 type="function" name="SpermFormAnalysis.Class::GxBitmap:GX_VALID_BIT_LIST __GetBestValudBit(GX_PIXEL_FORMAT_ENTRY)">
			<flags>1</flags>
		</SENSENODEFUNC487>
		<SENSENODEFUNC488 type="function" name="SpermFormAnalysis.Class::GxBitmap:PixelFormat __GetFormat(bool)">
			<flags>1</flags>
		</SENSENODEFUNC488>
		<SENSENODEFUNC489 type="function" name="SpermFormAnalysis.Class::GxBitmap:int __GetStride(int,bool)">
			<flags>1</flags>
		</SENSENODEFUNC489>
		<SENSENODEFUNC490 type="function" name="SpermFormAnalysis.Class::GxBitmap:bool __IsCompatible(Bitmap,int,int,bool)">
			<flags>1</flags>
		</SENSENODEFUNC490>
		<SENSENODEFUNC491 type="function" name="SpermFormAnalysis.Class::GxBitmap:void __CreateBitmap(Bitmap,int,int,bool)">
			<flags>1</flags>
		</SENSENODEFUNC491>
		<SENSENODEFUNC492 type="function" name="SpermFormAnalysis.Class::GxBitmap:void __UpdateBitmap(Bitmap,byte[],int,int,bool)">
			<flags>1</flags>
		</SENSENODEFUNC492>
		<SENSENODEFUNC493 type="function" name="SpermFormAnalysis.Class::mifareone:void .ctor()">
			<flags>1</flags>
		</SENSENODEFUNC493>
		<SENSENODEFUNC494 type="function" name="SpermFormAnalysis.Class::ShowReport:bool printReport(string[],string,string,bool,string)">
			<flags>1</flags>
		</SENSENODEFUNC494>
		<SENSENODEFUNC495 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:string ByteConvertString(byte[])">
			<flags>1</flags>
		</SENSENODEFUNC495>
		<SENSENODEFUNC496 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:uint HexToInt(string)">
			<flags>1</flags>
		</SENSENODEFUNC496>
		<SENSENODEFUNC497 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int HexStringToByteArray(string,byte[])">
			<flags>1</flags>
		</SENSENODEFUNC497>
		<SENSENODEFUNC498 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:void EnCode(byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC498>
		<SENSENODEFUNC499 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:void DeCode(byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC499>
		<SENSENODEFUNC500 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:string StrEnc(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC500>
		<SENSENODEFUNC501 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:string StrDec(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC501>
		<SENSENODEFUNC502 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:bool isfindmydevice(int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC502>
		<SENSENODEFUNC503 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:bool Subisfindmydevice(int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC503>
		<SENSENODEFUNC504 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:bool GetFeature(native int,byte[],int)">
			<flags>1</flags>
		</SENSENODEFUNC504>
		<SENSENODEFUNC505 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:bool SetFeature(native int,byte[],int)">
			<flags>1</flags>
		</SENSENODEFUNC505>
		<SENSENODEFUNC506 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_FindPort(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC506>
		<SENSENODEFUNC507 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_FindPort_2(int,int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC507>
		<SENSENODEFUNC508 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int OpenMydivece(native int,string)">
			<flags>1</flags>
		</SENSENODEFUNC508>
		<SENSENODEFUNC509 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Read(byte,byte,byte,byte,string)">
			<flags>1</flags>
		</SENSENODEFUNC509>
		<SENSENODEFUNC510 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Write(byte,byte,byte,byte,string)">
			<flags>1</flags>
		</SENSENODEFUNC510>
		<SENSENODEFUNC511 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Write_2(byte,byte,byte,byte,string)">
			<flags>1</flags>
		</SENSENODEFUNC511>
		<SENSENODEFUNC512 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int GetIDVersion(short,string)">
			<flags>1</flags>
		</SENSENODEFUNC512>
		<SENSENODEFUNC513 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GetID(int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC513>
		<SENSENODEFUNC514 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int Y_Read(byte[],int,int,byte[],string,int)">
			<flags>1</flags>
		</SENSENODEFUNC514>
		<SENSENODEFUNC515 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int Y_Write(byte[],int,int,byte[],string,int)">
			<flags>1</flags>
		</SENSENODEFUNC515>
		<SENSENODEFUNC516 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Cal(byte[],byte[],string,int)">
			<flags>1</flags>
		</SENSENODEFUNC516>
		<SENSENODEFUNC517 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SetCal_2(byte[],byte,string,short)">
			<flags>1</flags>
		</SENSENODEFUNC517>
		<SENSENODEFUNC518 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int ReadDword(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC518>
		<SENSENODEFUNC519 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int WriteDword(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC519>
		<SENSENODEFUNC520 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int WriteDword_2(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC520>
		<SENSENODEFUNC521 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GetIDVersion(short,string)">
			<flags>1</flags>
		</SENSENODEFUNC521>
		<SENSENODEFUNC522 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int GetID(int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC522>
		<SENSENODEFUNC523 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sRead(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC523>
		<SENSENODEFUNC524 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sWrite(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC524>
		<SENSENODEFUNC525 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YWriteEx(byte[],int,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC525>
		<SENSENODEFUNC526 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YReadEx(byte[],short,short,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC526>
		<SENSENODEFUNC527 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int FindPort_2(int,int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC527>
		<SENSENODEFUNC528 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int FindPort(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC528>
		<SENSENODEFUNC529 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sWrite_2(int,string)">
			<flags>1</flags>
		</SENSENODEFUNC529>
		<SENSENODEFUNC530 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:string AddZero(string)">
			<flags>1</flags>
		</SENSENODEFUNC530>
		<SENSENODEFUNC531 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:void myconvert(string,string,byte[])">
			<flags>1</flags>
		</SENSENODEFUNC531>
		<SENSENODEFUNC532 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YRead(byte,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC532>
		<SENSENODEFUNC533 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_YRead(byte,int,byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC533>
		<SENSENODEFUNC534 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YWrite(byte,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC534>
		<SENSENODEFUNC535 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_YWrite(byte,int,byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC535>
		<SENSENODEFUNC536 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetReadPassword(string,string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC536>
		<SENSENODEFUNC537 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetWritePassword(string,string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC537>
		<SENSENODEFUNC538 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YWriteString(string,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC538>
		<SENSENODEFUNC539 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YReadString(string,int,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC539>
		<SENSENODEFUNC540 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetCal_2(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC540>
		<SENSENODEFUNC541 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int Cal(byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC541>
		<SENSENODEFUNC542 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int EncString(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC542>
		<SENSENODEFUNC543 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sWriteEx(int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC543>
		<SENSENODEFUNC544 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sWrite_2Ex(int,int,string)">
			<flags>1</flags>
		</SENSENODEFUNC544>
		<SENSENODEFUNC545 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int ReSet(string)">
			<flags>1</flags>
		</SENSENODEFUNC545>
		<SENSENODEFUNC546 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_ReSet(string)">
			<flags>1</flags>
		</SENSENODEFUNC546>
		<SENSENODEFUNC547 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetCal_New(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC547>
		<SENSENODEFUNC548 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int Cal_New(byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC548>
		<SENSENODEFUNC549 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int EncString_New(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC549>
		<SENSENODEFUNC550 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GetVersionEx(short,string)">
			<flags>1</flags>
		</SENSENODEFUNC550>
		<SENSENODEFUNC551 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Cal_New(byte[],byte[],string,int)">
			<flags>1</flags>
		</SENSENODEFUNC551>
		<SENSENODEFUNC552 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SetCal_New(byte[],byte,string,short)">
			<flags>1</flags>
		</SENSENODEFUNC552>
		<SENSENODEFUNC553 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int F_GetVersionEx(short,string)">
			<flags>1</flags>
		</SENSENODEFUNC553>
		<SENSENODEFUNC554 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_YRead_new(string,int,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC554>
		<SENSENODEFUNC555 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_YWrite_new(string,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC555>
		<SENSENODEFUNC556 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_YReadEx(byte[],int,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC556>
		<SENSENODEFUNC557 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_YWriteEx(byte[],int,int,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC557>
		<SENSENODEFUNC558 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetHidOnly(bool,string)">
			<flags>1</flags>
		</SENSENODEFUNC558>
		<SENSENODEFUNC559 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SetHidOnly(bool,string)">
			<flags>1</flags>
		</SENSENODEFUNC559>
		<SENSENODEFUNC560 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetUReadOnly(string)">
			<flags>1</flags>
		</SENSENODEFUNC560>
		<SENSENODEFUNC561 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SetUReadOnly(string)">
			<flags>1</flags>
		</SENSENODEFUNC561>
		<SENSENODEFUNC562 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int sub_GetTrashBufLen(native int,int)">
			<flags>1</flags>
		</SENSENODEFUNC562>
		<SENSENODEFUNC563 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int GetTrashBufLen(string,int)">
			<flags>1</flags>
		</SENSENODEFUNC563>
		<SENSENODEFUNC564 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Set_SM2_KeyPair(byte[],byte[],byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC564>
		<SENSENODEFUNC565 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GenKeyPair(byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC565>
		<SENSENODEFUNC566 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GetChipID(byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC566>
		<SENSENODEFUNC567 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Get_SM2_PubKey(byte[],byte[],byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC567>
		<SENSENODEFUNC568 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Set_Pin(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC568>
		<SENSENODEFUNC569 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SM2_Enc(byte[],byte[],byte,string)">
			<flags>1</flags>
		</SENSENODEFUNC569>
		<SENSENODEFUNC570 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SM2_Dec(byte[],byte[],byte,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC570>
		<SENSENODEFUNC571 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Sign(byte[],byte[],string,string)">
			<flags>1</flags>
		</SENSENODEFUNC571>
		<SENSENODEFUNC572 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Sign_2(byte[],byte[],string,string)">
			<flags>1</flags>
		</SENSENODEFUNC572>
		<SENSENODEFUNC573 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_Verfiy(byte[],byte[],bool,string)">
			<flags>1</flags>
		</SENSENODEFUNC573>
		<SENSENODEFUNC574 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:string ByteArrayToHexString(byte[],int)">
			<flags>1</flags>
		</SENSENODEFUNC574>
		<SENSENODEFUNC575 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YT_GenKeyPair(string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC575>
		<SENSENODEFUNC576 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int Set_SM2_KeyPair(string,string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC576>
		<SENSENODEFUNC577 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int Get_SM2_PubKey(string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC577>
		<SENSENODEFUNC578 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int GetChipID(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC578>
		<SENSENODEFUNC579 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SM2_EncBuf(byte[],byte[],int,string)">
			<flags>1</flags>
		</SENSENODEFUNC579>
		<SENSENODEFUNC580 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SM2_DecBuf(byte[],byte[],int,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC580>
		<SENSENODEFUNC581 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SM2_EncString(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC581>
		<SENSENODEFUNC582 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SM2_DecString(string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC582>
		<SENSENODEFUNC583 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int YtSetPin(string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC583>
		<SENSENODEFUNC584 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SetID(byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC584>
		<SENSENODEFUNC585 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetID(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC585>
		<SENSENODEFUNC586 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GetProduceDate(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC586>
		<SENSENODEFUNC587 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int GetProduceDate(string,string)">
			<flags>1</flags>
		</SENSENODEFUNC587>
		<SENSENODEFUNC588 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:string SnToProduceDate(string)">
			<flags>1</flags>
		</SENSENODEFUNC588>
		<SENSENODEFUNC589 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int y_setcal(byte[],int,int,byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC589>
		<SENSENODEFUNC590 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetCal(string,string,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC590>
		<SENSENODEFUNC591 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_SetDisableFlag(byte,byte[],string)">
			<flags>1</flags>
		</SENSENODEFUNC591>
		<SENSENODEFUNC592 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int NT_GetDisableFlag(bool,string)">
			<flags>1</flags>
		</SENSENODEFUNC592>
		<SENSENODEFUNC593 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int SetDisableFlag(bool,string,string,string)">
			<flags>1</flags>
		</SENSENODEFUNC593>
		<SENSENODEFUNC594 type="function" name="SpermFormAnalysis.Class::SoftKeyYT88:int GetDisableFlag(bool,string)">
			<flags>1</flags>
		</SENSENODEFUNC594>
	</function>
</ssprotect>
