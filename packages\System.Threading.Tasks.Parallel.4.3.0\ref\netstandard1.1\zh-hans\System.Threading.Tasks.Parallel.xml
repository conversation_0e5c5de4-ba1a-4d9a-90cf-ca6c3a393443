﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks.Parallel</name>
  </assembly>
  <members>
    <member name="T:System.Threading.Tasks.Parallel">
      <summary>提供对并行循环和区域的支持。</summary>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Action{System.Int32})">
      <summary>执行 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Action{System.Int32,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以监视和操作循环的状态。 </summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int32,System.Int32,System.Func{``0},System.Func{System.Int32,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>执行具有线程本地数据的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Action{System.Int32})">
      <summary>执行 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以配置循环选项。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Action{System.Int32,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Func{``0},System.Func{System.Int32,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>执行具有线程本地数据的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Action{System.Int64})">
      <summary>执行具有 64 位索引的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Action{System.Int64,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行具有 64 位索引的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>一个 <see cref="T:System.Threading.Tasks.ParallelLoopResult" /> 结构，其中包含有关已完成的循环部分的信息。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int64,System.Int64,System.Func{``0},System.Func{System.Int64,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>执行具有 64 位索引和线程本地数据的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Action{System.Int64})">
      <summary>执行具有 64 位索引的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以配置循环选项。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Action{System.Int64,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行具有 64 位索引的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Func{``0},System.Func{System.Int64,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>执行具有 64 位索引和线程本地数据的 for（在 Visual Basic 中为 For）循环，其中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="fromInclusive">开始索引（含）。</param>
      <param name="toExclusive">结束索引（不含）。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="localInit">用于返回每个线程的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个线程的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.OrderablePartitioner{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> 中可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的可排序分区程序。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The <see cref="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized" /> property in the source orderable partitioner returns false.-or-Any methods in the source orderable partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.OrderablePartitioner{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> 上可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的可排序分区程序。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.OrderablePartitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> 中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的可排序分区程序。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is  null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The <see cref="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> orderable partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.OrderablePartitioner{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>执行具有 64 位索引和线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" /> 上可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的可排序分区程序。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> or <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null  partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.Partitioner" /> 上可能会并行运行迭代。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的分区程序。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is  null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.-or-The <see cref="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)" /> method in the <paramref name="source" /> partitioner does not return the correct number of partitions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.Partitioner" /> 中可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的分区程序。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-A method in the <paramref name="source" /> partitioner returns null.-or-The <see cref="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)" /> method in the <paramref name="source" /> partitioner does not return the correct number of partitions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.Partitioner{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.Partitioner" /> 上可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的分区程序。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.Partitioner" /> 中可能会并行运行迭代，而且可以配置循环选项。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的分区程序。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.Partitioner" /> 中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的分区程序。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.Concurrent.Partitioner" /> 上可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">包含原始数据源的分区程序。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 中可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>执行具有 64 位索引的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代，而且可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 中可能会并行运行迭代，而且可以匹配配置循环选项。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>执行 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 中可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>执行具有 64 位索引的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>执行具有线程本地数据和 64 位索引的 foreach（在 Visual Basic 中为 For Each）操作，其中在 <see cref="T:System.Collections.IEnumerable" /> 上可能会并行运行迭代，而且可以配置循环选项，可以监视和操作循环的状态。</summary>
      <returns>包含有关已完成的循环部分的信息的结构。</returns>
      <param name="source">可枚举的数据源。</param>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="localInit">用于返回每个任务的本地数据的初始状态的函数委托。</param>
      <param name="body">将为每个迭代调用一次的委托。</param>
      <param name="localFinally">用于对每个任务的本地状态执行一个最终操作的委托。</param>
      <typeparam name="TSource">源中数据的类型。</typeparam>
      <typeparam name="TLocal">线程本地数据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.Invoke(System.Action[])">
      <summary>尽可能并行执行提供的每个操作。</summary>
      <param name="actions">要执行的 <see cref="T:System.Action" /> 数组。</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="actions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that is thrown when any action in the <paramref name="actions" /> array throws an exception.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="actions" /> array contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.Invoke(System.Threading.Tasks.ParallelOptions,System.Action[])">
      <summary>执行所提供的每个操作，而且尽可能并行运行，除非用户取消了操作。</summary>
      <param name="parallelOptions">一个对象，用于配置此操作的行为。</param>
      <param name="actions">要执行的操作数组。</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> is set.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="actions" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that is thrown when any action in the <paramref name="actions" /> array throws an exception.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="actions" /> array contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ParallelLoopResult">
      <summary>提供执行 <see cref="T:System.Threading.Tasks.Parallel" /> 循环的完成状态。</summary>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopResult.IsCompleted">
      <summary>获取该循环是否已运行完成（即，该循环的所有迭代均已执行，并且该循环没有收到提前结束的请求）。</summary>
      <returns>如果该循环已运行完成，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopResult.LowestBreakIteration">
      <summary>获取从中调用 <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> 的最低迭代的索引。</summary>
      <returns>返回一个表示从中调用 Break 语句的最低迭代的整数。</returns>
    </member>
    <member name="T:System.Threading.Tasks.ParallelLoopState">
      <summary>可使并行循环迭代与其他迭代交互。此类的实例由 <see cref="T:System.Threading.Tasks.Parallel" /> 类提供给每个循环；不能在您的用户代码中创建实例。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ParallelLoopState.Break">
      <summary>告知 <see cref="T:System.Threading.Tasks.Parallel" /> 循环应在系统方便的时候尽早停止执行当前迭代之外的迭代。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> 方法以前被调用过。<see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> 和 <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> 不能被相同循环的迭代组合使用。</exception>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.IsExceptional">
      <summary>获取循环的任何迭代是否已引发相应迭代未处理的异常。</summary>
      <returns>如果引发了未经处理的异常，则为 true；否则为 false。 </returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.IsStopped">
      <summary>获取循环的任何迭代是否已调用 <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> 方法。</summary>
      <returns>如果任何迭代通过调用 <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> 方法已停止循环，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.LowestBreakIteration">
      <summary>获取从中调用 <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> 的最低循环迭代。</summary>
      <returns>从中调用 <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> 的最低迭代。如果是 <see cref="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0})" /> 循环，该值会基于内部生成的索引。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.ShouldExitCurrentIteration">
      <summary>获取循环的当前迭代是否应基于此迭代或其他迭代发出的请求退出。</summary>
      <returns>如果当前迭代应退出，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Threading.Tasks.ParallelLoopState.Stop">
      <summary>告知 <see cref="T:System.Threading.Tasks.Parallel" /> 循环应在系统方便的时候尽早停止执行。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> 以前调用了方法。<see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> 和 <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> 不能被相同循环的迭代组合使用。</exception>
    </member>
    <member name="T:System.Threading.Tasks.ParallelOptions">
      <summary>存储用于配置 <see cref="T:System.Threading.Tasks.Parallel" /> 类的方法的操作的选项。</summary>
    </member>
    <member name="M:System.Threading.Tasks.ParallelOptions.#ctor">
      <summary>初始化 <see cref="T:System.Threading.Tasks.ParallelOptions" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.CancellationToken">
      <summary>获取或设置与此 <see cref="T:System.Threading.Tasks.ParallelOptions" /> 实例关联的 <see cref="T:System.Threading.CancellationToken" />。</summary>
      <returns>与此实例关联的标记。</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.MaxDegreeOfParallelism">
      <summary>获取或设置此 <see cref="T:System.Threading.Tasks.ParallelOptions" /> 实例所允许的并发任务的最大数目。</summary>
      <returns>一个表示最大并行度的整数。</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">该属性被设置为 0 或小于 1- 的值。</exception>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.TaskScheduler">
      <summary>获取或设置与此 <see cref="T:System.Threading.Tasks.ParallelOptions" /> 实例关联的 <see cref="T:System.Threading.Tasks.TaskScheduler" />。将此属性设置为 null，以指示应使用当前计划程序。</summary>
      <returns>与此实例关联的任务计划程序。</returns>
    </member>
  </members>
</doc>