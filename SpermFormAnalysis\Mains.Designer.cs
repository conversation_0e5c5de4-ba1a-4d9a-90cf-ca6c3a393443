﻿namespace SpermFormAnalysis
{
    partial class Mains
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.TreeNode treeNode11 = new System.Windows.Forms.TreeNode("新建实验", 0, 0);
            System.Windows.Forms.TreeNode treeNode12 = new System.Windows.Forms.TreeNode("实验查询");
            System.Windows.Forms.TreeNode treeNode13 = new System.Windows.Forms.TreeNode("授权管理");
            System.Windows.Forms.TreeNode treeNode14 = new System.Windows.Forms.TreeNode("参数配置");
            System.Windows.Forms.TreeNode treeNode15 = new System.Windows.Forms.TreeNode("用户管理");
            this.ucTitle1 = new SunnyUI.Library.UCTitle();
            this.uiImageButton1 = new Sunny.UI.UIImageButton();
            this.uiHeaderButton1 = new Sunny.UI.UIHeaderButton();
            this.MainContainer = new Sunny.UI.UITabControl();
            this.MorTest = new System.Windows.Forms.TabPage();
            this.Search = new System.Windows.Forms.TabPage();
            this.Authorize = new System.Windows.Forms.TabPage();
            this.Setting = new System.Windows.Forms.TabPage();
            this.UserManager = new System.Windows.Forms.TabPage();
            this.uiSymbolButton3 = new Sunny.UI.UISymbolButton();
            this.uiSymbolButton2 = new Sunny.UI.UISymbolButton();
            this.uiSymbolButton1 = new Sunny.UI.UISymbolButton();
            this.Aside = new Sunny.UI.UINavMenu();
            this.uiToolTip1 = new Sunny.UI.UIToolTip(this.components);
            this.ucTitle1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.uiImageButton1)).BeginInit();
            this.MainContainer.SuspendLayout();
            this.SuspendLayout();
            // 
            // ucTitle1
            // 
            this.ucTitle1.BackColor = System.Drawing.Color.White;
            this.ucTitle1.Controls.Add(this.uiImageButton1);
            this.ucTitle1.Controls.Add(this.uiHeaderButton1);
            this.ucTitle1.Controls.Add(this.MainContainer);
            this.ucTitle1.Controls.Add(this.uiSymbolButton3);
            this.ucTitle1.Controls.Add(this.uiSymbolButton2);
            this.ucTitle1.Controls.Add(this.uiSymbolButton1);
            this.ucTitle1.Controls.Add(this.Aside);
            this.ucTitle1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.ucTitle1.Location = new System.Drawing.Point(0, 31);
            this.ucTitle1.Name = "ucTitle1";
            this.ucTitle1.RectHeight = 20;
            this.ucTitle1.Size = new System.Drawing.Size(1418, 777);
            this.ucTitle1.TabIndex = 0;
            this.ucTitle1.Text = "精子形态分析管理软件";
            this.ucTitle1.TitleColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.ucTitle1.TitleTextColor = System.Drawing.Color.Black;
            this.ucTitle1.TitleTextFont = new System.Drawing.Font("宋体", 15F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ucTitle1.TrapHeight = 20;
            // 
            // uiImageButton1
            // 
            this.uiImageButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.uiImageButton1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiImageButton1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiImageButton1.Image = global::SpermFormAnalysis.Properties.Resources.星博智造黑色2;
            this.uiImageButton1.Location = new System.Drawing.Point(1, 1);
            this.uiImageButton1.Name = "uiImageButton1";
            this.uiImageButton1.Size = new System.Drawing.Size(138, 74);
            this.uiImageButton1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.uiImageButton1.TabIndex = 8;
            this.uiImageButton1.TabStop = false;
            this.uiImageButton1.Text = null;
            // 
            // uiHeaderButton1
            // 
            this.uiHeaderButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.uiHeaderButton1.BackColor = System.Drawing.Color.Transparent;
            this.uiHeaderButton1.CircleColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.uiHeaderButton1.CircleHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.uiHeaderButton1.CircleSize = 54;
            this.uiHeaderButton1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiHeaderButton1.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.uiHeaderButton1.FillHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.uiHeaderButton1.FillPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.uiHeaderButton1.FillSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.uiHeaderButton1.Font = new System.Drawing.Font("微软雅黑", 15F);
            this.uiHeaderButton1.ForeColor = System.Drawing.Color.Black;
            this.uiHeaderButton1.ForeHoverColor = System.Drawing.Color.Black;
            this.uiHeaderButton1.ForePressColor = System.Drawing.Color.Black;
            this.uiHeaderButton1.ForeSelectedColor = System.Drawing.Color.Black;
            this.uiHeaderButton1.Location = new System.Drawing.Point(13, 639);
            this.uiHeaderButton1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiHeaderButton1.Name = "uiHeaderButton1";
            this.uiHeaderButton1.Padding = new System.Windows.Forms.Padding(0, 8, 0, 3);
            this.uiHeaderButton1.Radius = 0;
            this.uiHeaderButton1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uiHeaderButton1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uiHeaderButton1.Size = new System.Drawing.Size(114, 101);
            this.uiHeaderButton1.Style = Sunny.UI.UIStyle.Custom;
            this.uiHeaderButton1.StyleCustomMode = true;
            this.uiHeaderButton1.Symbol = 61714;
            this.uiHeaderButton1.SymbolOffset = new System.Drawing.Point(0, 3);
            this.uiHeaderButton1.SymbolSize = 32;
            this.uiHeaderButton1.TabIndex = 10;
            this.uiHeaderButton1.Text = "返回";
            this.uiHeaderButton1.TextAlign = System.Drawing.ContentAlignment.BottomCenter;
            this.uiHeaderButton1.Click += new System.EventHandler(this.uiHeaderButton1_Click);
            // 
            // MainContainer
            // 
            this.MainContainer.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.MainContainer.Controls.Add(this.MorTest);
            this.MainContainer.Controls.Add(this.Search);
            this.MainContainer.Controls.Add(this.Authorize);
            this.MainContainer.Controls.Add(this.Setting);
            this.MainContainer.Controls.Add(this.UserManager);
            this.MainContainer.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.MainContainer.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.MainContainer.ItemSize = new System.Drawing.Size(0, 1);
            this.MainContainer.Location = new System.Drawing.Point(145, 72);
            this.MainContainer.MainPage = "";
            this.MainContainer.Name = "MainContainer";
            this.MainContainer.SelectedIndex = 0;
            this.MainContainer.Size = new System.Drawing.Size(1271, 705);
            this.MainContainer.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.MainContainer.StyleCustomMode = true;
            this.MainContainer.TabIndex = 7;
            this.MainContainer.TabVisible = false;
            // 
            // MorTest
            // 
            this.MorTest.Location = new System.Drawing.Point(0, 0);
            this.MorTest.Name = "MorTest";
            this.MorTest.Size = new System.Drawing.Size(1271, 705);
            this.MorTest.TabIndex = 0;
            this.MorTest.Text = "实验分析";
            this.MorTest.UseVisualStyleBackColor = true;
            // 
            // Search
            // 
            this.Search.Location = new System.Drawing.Point(0, 0);
            this.Search.Name = "Search";
            this.Search.Size = new System.Drawing.Size(1271, 705);
            this.Search.TabIndex = 1;
            this.Search.Text = "实验查询";
            this.Search.UseVisualStyleBackColor = true;
            // 
            // Authorize
            // 
            this.Authorize.Location = new System.Drawing.Point(0, 0);
            this.Authorize.Name = "Authorize";
            this.Authorize.Size = new System.Drawing.Size(1271, 705);
            this.Authorize.TabIndex = 2;
            this.Authorize.Text = "授权管理";
            this.Authorize.UseVisualStyleBackColor = true;
            // 
            // Setting
            // 
            this.Setting.Location = new System.Drawing.Point(0, 0);
            this.Setting.Name = "Setting";
            this.Setting.Size = new System.Drawing.Size(1271, 705);
            this.Setting.TabIndex = 3;
            this.Setting.Text = "参数配置";
            this.Setting.UseVisualStyleBackColor = true;
            // 
            // UserManager
            // 
            this.UserManager.Location = new System.Drawing.Point(0, 0);
            this.UserManager.Name = "UserManager";
            this.UserManager.Size = new System.Drawing.Size(1271, 705);
            this.UserManager.TabIndex = 4;
            this.UserManager.Text = "用户管理";
            this.UserManager.UseVisualStyleBackColor = true;
            // 
            // uiSymbolButton3
            // 
            this.uiSymbolButton3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.uiSymbolButton3.BackColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton3.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiSymbolButton3.Enabled = false;
            this.uiSymbolButton3.FillColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.uiSymbolButton3.ForeColor = System.Drawing.Color.DodgerBlue;
            this.uiSymbolButton3.ForeDisableColor = System.Drawing.Color.DodgerBlue;
            this.uiSymbolButton3.ForeHoverColor = System.Drawing.Color.Red;
            this.uiSymbolButton3.ForePressColor = System.Drawing.Color.Red;
            this.uiSymbolButton3.ForeSelectedColor = System.Drawing.Color.Red;
            this.uiSymbolButton3.Location = new System.Drawing.Point(1315, 40);
            this.uiSymbolButton3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton3.Name = "uiSymbolButton3";
            this.uiSymbolButton3.RectColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton3.RectDisableColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton3.RectPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(168)))), ((int)(((byte)(255)))));
            this.uiSymbolButton3.Size = new System.Drawing.Size(100, 34);
            this.uiSymbolButton3.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton3.StyleCustomMode = true;
            this.uiSymbolButton3.Symbol = 62141;
            this.uiSymbolButton3.TabIndex = 5;
            this.uiSymbolButton3.Text = "Admin";
            // 
            // uiSymbolButton2
            // 
            this.uiSymbolButton2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.uiSymbolButton2.BackColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiSymbolButton2.FillColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.uiSymbolButton2.ForeColor = System.Drawing.Color.DodgerBlue;
            this.uiSymbolButton2.ForeHoverColor = System.Drawing.Color.Red;
            this.uiSymbolButton2.ForePressColor = System.Drawing.Color.Red;
            this.uiSymbolButton2.ForeSelectedColor = System.Drawing.Color.Red;
            this.uiSymbolButton2.Location = new System.Drawing.Point(1209, 39);
            this.uiSymbolButton2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton2.Name = "uiSymbolButton2";
            this.uiSymbolButton2.RectColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton2.RectDisableColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton2.RectPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(168)))), ((int)(((byte)(255)))));
            this.uiSymbolButton2.Size = new System.Drawing.Size(100, 34);
            this.uiSymbolButton2.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton2.StyleCustomMode = true;
            this.uiSymbolButton2.Symbol = 61950;
            this.uiSymbolButton2.TabIndex = 4;
            this.uiSymbolButton2.Text = "质控图";
            this.uiSymbolButton2.Click += new System.EventHandler(this.uiSymbolButton2_Click);
            // 
            // uiSymbolButton1
            // 
            this.uiSymbolButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.uiSymbolButton1.BackColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.uiSymbolButton1.FillColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.uiSymbolButton1.ForeColor = System.Drawing.Color.DodgerBlue;
            this.uiSymbolButton1.ForeHoverColor = System.Drawing.Color.Red;
            this.uiSymbolButton1.ForePressColor = System.Drawing.Color.Red;
            this.uiSymbolButton1.ForeSelectedColor = System.Drawing.Color.Red;
            this.uiSymbolButton1.Location = new System.Drawing.Point(1108, 39);
            this.uiSymbolButton1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiSymbolButton1.Name = "uiSymbolButton1";
            this.uiSymbolButton1.RectColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton1.RectDisableColor = System.Drawing.Color.Transparent;
            this.uiSymbolButton1.RectPressColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(168)))), ((int)(((byte)(255)))));
            this.uiSymbolButton1.Size = new System.Drawing.Size(95, 35);
            this.uiSymbolButton1.Style = Sunny.UI.UIStyle.Custom;
            this.uiSymbolButton1.StyleCustomMode = true;
            this.uiSymbolButton1.Symbol = 61546;
            this.uiSymbolButton1.TabIndex = 3;
            this.uiSymbolButton1.Text = "关于";
            this.uiSymbolButton1.Click += new System.EventHandler(this.uiSymbolButton1_Click);
            // 
            // Aside
            // 
            this.Aside.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.Aside.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.Aside.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.Aside.DrawMode = System.Windows.Forms.TreeViewDrawMode.OwnerDrawAll;
            this.Aside.ExpandSelectFirst = true;
            this.Aside.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.Aside.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold);
            this.Aside.ForeColor = System.Drawing.Color.Black;
            this.Aside.FullRowSelect = true;
            this.Aside.HoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(185)))), ((int)(((byte)(255)))));
            this.Aside.Indent = 24;
            this.Aside.ItemHeight = 50;
            this.Aside.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(167)))), ((int)(((byte)(208)))), ((int)(((byte)(249)))));
            this.Aside.Location = new System.Drawing.Point(1, 72);
            this.Aside.MenuStyle = Sunny.UI.UIMenuStyle.Custom;
            this.Aside.Name = "Aside";
            treeNode11.ImageIndex = 0;
            treeNode11.Name = "新建实验";
            treeNode11.SelectedImageIndex = 0;
            treeNode11.Text = "新建实验";
            treeNode12.Name = "实验查询";
            treeNode12.Text = "实验查询";
            treeNode13.Name = "授权管理";
            treeNode13.Text = "授权管理";
            treeNode14.Name = "参数配置";
            treeNode14.Text = "参数配置";
            treeNode15.Name = "用户管理";
            treeNode15.Text = "用户管理";
            this.Aside.Nodes.AddRange(new System.Windows.Forms.TreeNode[] {
            treeNode11,
            treeNode12,
            treeNode13,
            treeNode14,
            treeNode15});
            this.Aside.SelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(185)))), ((int)(((byte)(255)))));
            this.Aside.SelectedForeColor = System.Drawing.Color.Black;
            this.Aside.SelectedHighColor = System.Drawing.Color.FromArgb(((int)(((byte)(113)))), ((int)(((byte)(185)))), ((int)(((byte)(255)))));
            this.Aside.ShowLines = false;
            this.Aside.Size = new System.Drawing.Size(138, 705);
            this.Aside.Style = Sunny.UI.UIStyle.Custom;
            this.Aside.StyleCustomMode = true;
            this.Aside.TabIndex = 1;
            this.Aside.MenuItemClick += new Sunny.UI.UINavMenu.OnMenuItemClick(this.Aside_MenuItemClick);
            // 
            // uiToolTip1
            // 
            this.uiToolTip1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(54)))), ((int)(((byte)(54)))), ((int)(((byte)(54)))));
            this.uiToolTip1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(239)))), ((int)(((byte)(239)))), ((int)(((byte)(239)))));
            this.uiToolTip1.OwnerDraw = true;
            // 
            // Mains
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 21F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1418, 808);
            this.Controls.Add(this.ucTitle1);
            this.Name = "Mains";
            this.Padding = new System.Windows.Forms.Padding(0, 31, 0, 0);
            this.Style = Sunny.UI.UIStyle.Custom;
            this.StyleCustomMode = true;
            this.Text = "";
            this.TitleColor = System.Drawing.Color.DodgerBlue;
            this.TitleHeight = 31;
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.FormClosed += new System.Windows.Forms.FormClosedEventHandler(this.Mains_FormClosed);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.Mains_KeyDown);
            this.ucTitle1.ResumeLayout(false);
            this.ucTitle1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.uiImageButton1)).EndInit();
            this.MainContainer.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private SunnyUI.Library.UCTitle ucTitle1;
        private Sunny.UI.UINavMenu Aside;
        private Sunny.UI.UISymbolButton uiSymbolButton1;
        private Sunny.UI.UISymbolButton uiSymbolButton2;
        private Sunny.UI.UISymbolButton uiSymbolButton3;
        private Sunny.UI.UITabControl MainContainer;
        private System.Windows.Forms.TabPage MorTest;
        private System.Windows.Forms.TabPage Search;
        private System.Windows.Forms.TabPage Authorize;
        private System.Windows.Forms.TabPage Setting;
        private System.Windows.Forms.TabPage UserManager;
        private Sunny.UI.UIImageButton uiImageButton1;
        private Sunny.UI.UIHeaderButton uiHeaderButton1;
        private Sunny.UI.UIToolTip uiToolTip1;
    }
}