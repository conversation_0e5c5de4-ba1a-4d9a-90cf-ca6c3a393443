﻿using Emgu.CV.Util;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.TFmodel
{
    public static class common
    {
        // Token: 0x04000004 RID: 4
        public const double pixelSize = 5.5803571428571423;  //5.86
        //

        // Token: 0x04000005 RID: 5
        public static Size resolutionRatio = new Size(1920, 1200);

        // Token: 0x04000006 RID: 6
        public const double Magnification = 100.0;

        // Token: 0x04000007 RID: 7
        public const double scalar = 0.055803571428571425;

        // Token: 0x04000008 RID: 8
        public const double scalarArea = 0.0031140385841836732;

        // Token: 0x0200000C RID: 12
        // (Invoke) Token: 0x06000018 RID: 24
        public delegate void TrunDFIOnceDoneEventHandler(string sampleId_fn);

        // Token: 0x0200000D RID: 13
        // (Invoke) Token: 0x0600001C RID: 28
        public delegate void TrunDFIOnceStartEventHandler(string sampleId_fn);

        // Token: 0x0200000E RID: 14
        // (Invoke) Token: 0x06000020 RID: 32
        public delegate void TrunDFITskDoneEventHandler(string sampleId);

        // Token: 0x0200000F RID: 15
        // (Invoke) Token: 0x06000024 RID: 36
        public delegate void TrunDFITskStartEventHandler(string sampleId);

        // Token: 0x02000010 RID: 16
        // (Invoke) Token: 0x06000028 RID: 40
        public delegate void TrunDFITskStoppedEventHandler(string sampleId);

        // Token: 0x02000011 RID: 17
        // (Invoke) Token: 0x0600002C RID: 44
        public delegate void TrunAllTsksStoppedEventHandler();

        // Token: 0x02000012 RID: 18
        // (Invoke) Token: 0x06000030 RID: 48
        public delegate void TrunAllTsksStartEventHandler();

        // Token: 0x02000013 RID: 19
        // (Invoke) Token: 0x06000034 RID: 52
        public delegate void TrunAllTsksDoneEventHandler();

        // Token: 0x02000014 RID: 20
        // (Invoke) Token: 0x06000038 RID: 56
        public delegate void TrunAllTsksProgressEventHandler(float progress);

        // Token: 0x02000015 RID: 21
        public struct dectResult
        {
            // Token: 0x04000033 RID: 51
            public string imageSource;

            // Token: 0x04000034 RID: 52
            public float ymin;

            // Token: 0x04000035 RID: 53
            public float xmin;

            // Token: 0x04000036 RID: 54
            public float ymax;

            // Token: 0x04000037 RID: 55
            public float xmax;

            // Token: 0x04000038 RID: 56
            public float score;

            // Token: 0x04000039 RID: 57
            public int classid;

            // Token: 0x0400003A RID: 58
            public string clsname;

            // Token: 0x0400003B RID: 59
            public int isNormal;

            // Token: 0x0400003C RID: 60
            public VectorOfPoint vop_acrosome;

            // Token: 0x0400003D RID: 61
            public VectorOfPoint vop_kernel;

            // Token: 0x0400003E RID: 62
            public VectorOfPoint vop_sperm;

            // Token: 0x0400003F RID: 63
            public double acrosomeArea;

            // Token: 0x04000040 RID: 64
            public double kernelArea;

            // Token: 0x04000041 RID: 65
            public double spermArea;

            // Token: 0x04000042 RID: 66
            public double spermGirth;

            // Token: 0x04000043 RID: 67
            public double longAxix;

            // Token: 0x04000044 RID: 68
            public double shortAxis;

            // Token: 0x04000045 RID: 69
            public PointF center;

            // Token: 0x04000046 RID: 70
            public double angle;

            // Token: 0x04000047 RID: 71
            public double ellipsRatio;

            // Token: 0x04000048 RID: 72
            public double acrosomeRatio;

            // Token: 0x04000049 RID: 73
            public double areaCV;

            // Token: 0x0400004A RID: 74
            public double girthCV;

            // Token: 0x0400004B RID: 75
            public double ellipseCV;

            // Token: 0x0400004C RID: 76
            public double SymmetryRatio;

            // Token: 0x0400004D RID: 77
            public double SymmetryRatioLong;
        }

        // Token: 0x02000016 RID: 22
        public enum enumResult
        {
            // Token: 0x0400004F RID: 79
            abnormal,
            // Token: 0x04000050 RID: 80
            normal,
            // Token: 0x04000051 RID: 81
            ignore
        }

        public struct taskInfo
        {
            // Token: 0x04000052 RID: 82
            public string patientId;

            // Token: 0x04000053 RID: 83
            public string regId;

            // Token: 0x04000054 RID: 84
            public string sampleId;

            // Token: 0x04000055 RID: 85
            public string name;

            // Token: 0x04000056 RID: 86
            public bool sexual;

            // Token: 0x04000057 RID: 87
            public int age;

            // Token: 0x04000058 RID: 88
            public int asceticDays;

            // Token: 0x04000059 RID: 89
            public int methods;

            // Token: 0x0400005A RID: 90
            public string path;

            // Token: 0x0400005B RID: 91
            public List<string> fileList;

            // Token: 0x0400005C RID: 92
            public int numNornal;

            // Token: 0x0400005D RID: 93
            public int numAbnormal;

            // Token: 0x0400005E RID: 94
            public int numTotal;

            // Token: 0x0400005F RID: 95
            public float normalPercentage;

            // Token: 0x04000060 RID: 96
            public int status;

            // Token: 0x04000061 RID: 97
            public int procCount;

            // Token: 0x04000062 RID: 98
            public Dictionary<string, List<common.dectResult>> results;

            // Token: 0x04000063 RID: 99
            public int inspectionPurpose;

            // Token: 0x04000064 RID: 100
            public int sectionID;

            // Token: 0x04000065 RID: 101
            public string inspectionMethod;

            // Token: 0x04000066 RID: 102
            public string sendDoctor;

            // Token: 0x04000067 RID: 103
            public int collectStatus;

            // Token: 0x04000068 RID: 104
            public int collectResult;

            // Token: 0x04000069 RID: 105
            public DateTime sampleTime;

            // Token: 0x0400006A RID: 106
            public DateTime getSampleTime;

            // Token: 0x0400006B RID: 107
            public DateTime analyzTime;

            // Token: 0x0400006C RID: 108
            public DateTime checkTime;
        }

        public enum enumCollectResult
        {
            partial,
            all
        }

        public enum enumCollectStatus
        {
            difficult,
            success
        }

        public enum enumPurpose
        {
            spermAnalyz
        }

        public enum enumSection
        {
            maleClinic
        }


        public enum enumTaskStat
        {
            // Token: 0x04000078 RID: 120
            Added,
            // Token: 0x04000079 RID: 121
            Analyzed,
            // Token: 0x0400007A RID: 122
            Checking,
            // Token: 0x0400007B RID: 123
            Checked,
            // Token: 0x0400007C RID: 124
            Reported
        }


        public enum enumAnalyzeStat
        {
            // Token: 0x0400007E RID: 126
            Stopped = -1,
            // Token: 0x0400007F RID: 127
            Wait,
            // Token: 0x04000080 RID: 128
            Busy
        }


        public enum enumMethods
        {
            // Token: 0x04000082 RID: 130
            const_0,
            // Token: 0x04000083 RID: 131
            const_1
        }
    }
}
