﻿using MySql.Data.MySqlClient;
using SpermFormAnalysis.Model;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace SpermFormAnalysis.DataHelper
{
    public static class AdminServices
    {
        private static string connectionString = @"Data Source=" + Application.StartupPath + @"\database\morsperm.db;Initial Catalog=sqlite;Integrated Security=True; =10";
        public static Admin GetAdmin(string UserName)
        {
            Admin admin = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {

                SQLiteCommand cmd = new SQLiteCommand("select * from admin where Admin_ID = '" + UserName + "'", conn);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        admin = new Admin()
                        {
                            Admin_ID = dr["Admin_ID"].ToString(),
                            Admin_Level = Convert.ToInt32(dr["Admin_Level"]),
                            Admin_Name = dr["Admin_Name"].ToString(),
                            Admin_PWD = dr["Admin_PWD"].ToString(),
                        };
                    }
                }
                conn.Close();
            }
            return admin;
        }

        public static List<Admin> GetObjects()
        {
            List<Admin> lists = new List<Admin>();
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from admin", conn);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        lists.Add(new Admin()
                        {
                            Admin_ID = dr["Admin_ID"].ToString(),
                            Admin_Level = Convert.ToInt32(dr["Admin_Level"]),
                            Admin_Name = dr["Admin_Name"].ToString(),
                            Admin_PWD = dr["Admin_PWD"].ToString(),
                        });
                    }
                }
                conn.Close();
            }
            return lists;
        }

        /// <summary>
        /// 创建账户
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static bool CreateObject(Admin obj)
        {
            bool issuc = true;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("insert into admin(Admin_ID,Admin_PWD,Admin_Level) values(@Admin_ID,@Admin_PWD,@Admin_Level)", conn);
                cmd.Parameters.Add(new SQLiteParameter("@Admin_ID", obj.Admin_ID));
                cmd.Parameters.Add(new SQLiteParameter("@Admin_PWD", obj.Admin_PWD));
                cmd.Parameters.Add(new SQLiteParameter("@Admin_Level", obj.Admin_Level));
                conn.Open();
                try
                {
                    cmd.ExecuteNonQuery();
                }
                catch (Exception exc)
                {
                    issuc = false;
                }
                conn.Close();
            }
            return issuc;
        }

        public static bool UpdateObject(Admin obj)
        {
            bool issuc = true;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("update admin set Admin_PWD=@Admin_PWD,Admin_Level=@Admin_Level where Admin_ID =@Admin_ID", conn);
                cmd.Parameters.Add(new SQLiteParameter("@Admin_ID", obj.Admin_ID));
                cmd.Parameters.Add(new SQLiteParameter("@Admin_PWD", obj.Admin_PWD));
                cmd.Parameters.Add(new SQLiteParameter("@Admin_Level", obj.Admin_Level));
                conn.Open();
                try
                {
                    cmd.ExecuteNonQuery();
                }
                catch (Exception exc)
                {
                    issuc = false;
                }
                conn.Close();
            }
            return issuc;
        }

        public static bool DeleteObject(string Admin_ID)
        {
            bool issuc = true;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("delete from admin where Admin_ID =@Admin_ID", conn);
                cmd.Parameters.Add(new SQLiteParameter("@Admin_ID", Admin_ID));
                conn.Open();
                try
                {
                    cmd.ExecuteNonQuery();
                }
                catch (Exception exc)
                {
                    issuc = false;
                }
                conn.Close();
            }
            return issuc;
        }

        public static bool IsExist(string Admin_ID)
        {
            bool isexist = false;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select count(*) from admin where Admin_ID = '" + Admin_ID + "'", conn);
                conn.Open();
                var obj=cmd.ExecuteScalar();
                conn.Close();
                if (Convert.ToInt32(obj) > 0)
                    isexist = true;
            }
            return isexist;
        }
    }
}
