2025-08-02 21:37:41,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, <PERSON><PERSON><PERSON> last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 , String ) 位置 D:\办公用\公司软件\形态学\SpermFormAnalysis-界面优化版本\SpermFormAnalysis\PageForm\CellDetails.cs:行号 478
2025-08-02 21:37:41,调试：5
2025-08-02 21:37:45,原图片数：1 当前选择的图片数1
2025-08-02 21:37:56,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 , String ) 位置 D:\办公用\公司软件\形态学\SpermFormAnalysis-界面优化版本\SpermFormAnalysis\PageForm\CellDetails.cs:行号 478
2025-08-02 21:37:56,调试：4
2025-08-02 21:38:09,原图片数：5 当前选择的图片数4
2025-08-02 21:38:20,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 , String ) 位置 D:\办公用\公司软件\形态学\SpermFormAnalysis-界面优化版本\SpermFormAnalysis\PageForm\CellDetails.cs:行号 478
2025-08-02 21:38:20,调试：1
2025-08-02 21:38:32,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 , String ) 位置 D:\办公用\公司软件\形态学\SpermFormAnalysis-界面优化版本\SpermFormAnalysis\PageForm\CellDetails.cs:行号 478
2025-08-02 21:38:32,调试：5
2025-08-02 21:38:44,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 , String ) 位置 D:\办公用\公司软件\形态学\SpermFormAnalysis-界面优化版本\SpermFormAnalysis\PageForm\CellDetails.cs:行号 478
2025-08-02 21:38:44,调试：3
2025-08-02 21:38:57,调试：分析时报错OOM when allocating tensor with shape[128,128,1,1] and type float on /job:localhost/replica:0/task:0/device:GPU:0 by allocator GPU_0_bfc
	 [[{{node xception_65/entry_flow/block1/unit_1/xception_module/separable_conv2_pointwise/Conv2D}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.

	 [[{{node ExpandDims_1}}]]
Hint: If you want to see a list of allocated tensors when OOM happens, add report_tensor_allocations_upon_oom to RunOptions for current allocation info.
   在 TensorFlow.TFStatus.CheckMaybeRaise(TFStatus incomingStatus, Boolean last)
   在 TensorFlow.TFSession.Run(TFOutput[] inputs, TFTensor[] inputValues, TFOutput[] outputs, TFOperation[] targetOpers, TFBuffer runMetadata, TFBuffer runOptions, TFStatus status)
   在 TensorFlow.TFSession.Runner.Run(TFStatus status)
   在 TFRunModel.TFRunDeepLab.RunModelOnce(String input)
   在 SpermFormAnalysis.Segments.clsTailProcess.doTailSegProcess_single(List`1 , String ) 位置 D:\办公用\公司软件\形态学\SpermFormAnalysis-界面优化版本\SpermFormAnalysis\PageForm\CellDetails.cs:行号 478
2025-08-02 21:38:57,调试：4
