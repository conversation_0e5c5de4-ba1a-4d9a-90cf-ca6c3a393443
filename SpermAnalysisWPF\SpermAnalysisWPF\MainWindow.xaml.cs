﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Win32;
using SpermAnalysisWPF.DataHelper;
using SpermAnalysisWPF.Model;
using SpermAnalysisWPF.Services;
using SpermAnalysisWPF.Utils;

namespace SpermAnalysisWPF;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private SpermAnalysisService? analysisService;
    private string? currentImagePath;
    private List<Sperm>? analysisResults;
    private string currentSampleId = string.Empty;
    private BitmapImage? originalBitmap;
    private double currentZoom = 1.0;
    private double imageDisplayScale = 1.0;

    public MainWindow()
    {
        InitializeComponent();
        InitializeApplication();

        // 监听窗口大小变化
        this.SizeChanged += MainWindow_SizeChanged;
        ImageContainer.SizeChanged += ImageContainer_SizeChanged;
    }

    private void InitializeApplication()
    {
        try
        {
            // 初始化数据库
            SpermDbContext.InitializeDatabase();

            // 初始化分析服务
            analysisService = new SpermAnalysisService();

            // 初始化GPU内存管理（在TensorFlow预加载之前）
            try
            {
                // 对于WPF版本，使用保守的GPU设置
                Basic.bForceUseCPU = false; // 可以根据需要调整
                Basic.bGPUMemoryOptimized = true;
                LogHelper.WriteInfoLog("WPF版本GPU内存管理配置完成");
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"GPU内存管理配置失败: {ex.Message}");
            }

            // 加载系统信息
            LoadSystemInfo();

            // 生成新的样本ID
            currentSampleId = Guid.NewGuid().ToString();

            StatusText.Text = "系统初始化完成，开始预加载...";
            LogHelper.WriteInfoLog("系统初始化完成");

            // 初始化日志显示
            RefreshLogDisplay();

            // 启动预加载线程
            var preloadThread = new System.Threading.Thread(PreloadTensorFlowModel);
            preloadThread.IsBackground = true;
            preloadThread.Start();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"系统初始化失败: {ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadSystemInfo()
    {
        // 更新系统信息显示
        FrameworkVersionText.Text = $".NET {Environment.Version}";
        ArchitectureText.Text = RuntimeInformation.ProcessArchitecture.ToString();

        StatusText.Text = $"系统已加载 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
    }

    private void TestButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            StatusText.Text = "正在运行系统测试...";

            var sb = new StringBuilder();
            sb.AppendLine("=== 系统测试报告 ===");
            sb.AppendLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            // 基本系统信息
            sb.AppendLine("【基本信息】");
            sb.AppendLine($"操作系统: {Environment.OSVersion}");
            sb.AppendLine($"机器名称: {Environment.MachineName}");
            sb.AppendLine($"用户名称: {Environment.UserName}");
            sb.AppendLine($"处理器数量: {Environment.ProcessorCount}");
            sb.AppendLine($"系统目录: {Environment.SystemDirectory}");
            sb.AppendLine();

            // .NET 运行时信息
            sb.AppendLine("【.NET 运行时信息】");
            sb.AppendLine($"框架版本: {RuntimeInformation.FrameworkDescription}");
            sb.AppendLine($"运行时标识符: {RuntimeInformation.RuntimeIdentifier}");
            sb.AppendLine($"进程架构: {RuntimeInformation.ProcessArchitecture}");
            sb.AppendLine($"操作系统架构: {RuntimeInformation.OSArchitecture}");
            sb.AppendLine($"操作系统描述: {RuntimeInformation.OSDescription}");
            sb.AppendLine();

            // 程序集信息
            sb.AppendLine("【程序集信息】");
            var assembly = Assembly.GetExecutingAssembly();
            sb.AppendLine($"程序集名称: {assembly.GetName().Name}");
            sb.AppendLine($"程序集版本: {assembly.GetName().Version}");
            sb.AppendLine($"程序集位置: {assembly.Location}");
            sb.AppendLine();

            // 内存信息
            sb.AppendLine("【内存信息】");
            sb.AppendLine($"工作集: {Environment.WorkingSet / 1024 / 1024:F2} MB");
            sb.AppendLine($"GC 总内存: {GC.GetTotalMemory(false) / 1024 / 1024:F2} MB");
            sb.AppendLine();

            // 测试结果
            sb.AppendLine("【测试结果】");
            sb.AppendLine("✓ 系统运行正常");
            sb.AppendLine("✓ x64 架构支持正常");
            sb.AppendLine("✓ .NET 8.0 运行时正常");
            sb.AppendLine("✓ WPF 界面渲染正常");
            sb.AppendLine();
            sb.AppendLine("系统测试完成！所有组件运行正常。");

            OutputText.Text = sb.ToString();
            StatusText.Text = "系统测试完成 - 所有组件正常";
        }
        catch (Exception ex)
        {
            OutputText.Text = $"系统测试失败:\n\n{ex.Message}\n\n{ex.StackTrace}";
            StatusText.Text = "系统测试失败";
        }
    }

    private void ImportImageButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择精子图像",
                Filter = "图像文件|*.jpg;*.jpeg;*.png;*.bmp;*.tiff|所有文件|*.*",
                Multiselect = false
            };

            if (openFileDialog.ShowDialog() == true)
            {
                currentImagePath = openFileDialog.FileName;
                LogHelper.WriteInfoLog($"导入图像: {System.IO.Path.GetFileName(currentImagePath)}");

                // 加载并显示图像
                LoadAndDisplayImage(currentImagePath);

                // 显示选中的文件信息
                var fileInfo = new FileInfo(currentImagePath);
                OutputText.Text = $"已选择图像文件:\n\n" +
                                 $"文件名: {fileInfo.Name}\n" +
                                 $"文件大小: {fileInfo.Length / 1024:F2} KB\n" +
                                 $"修改时间: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}\n\n" +
                                 $"点击'开始分析'按钮进行精子形态学分析。";

                AnalyzeButton.IsEnabled = true;
                StatusText.Text = $"已选择图像: {fileInfo.Name}";
                RefreshLogDisplay();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"导入图像失败: {ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void AnalyzeButton_Click(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrEmpty(currentImagePath) || !File.Exists(currentImagePath))
        {
            MessageBox.Show("请先选择有效的图像文件", "提示",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            // 禁用按钮，显示分析状态
            AnalyzeButton.IsEnabled = false;
            ImportImageButton.IsEnabled = false;
            StatusText.Text = "正在分析图像，请稍候...";

            LogHelper.WriteInfoLog($"开始分析图像: {System.IO.Path.GetFileName(currentImagePath)}");

            var sb = new StringBuilder();
            sb.AppendLine("=== 精子形态学分析报告 ===");
            sb.AppendLine($"分析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"图像文件: {System.IO.Path.GetFileName(currentImagePath)}");
            sb.AppendLine($"样本ID: {currentSampleId}");
            sb.AppendLine();
            sb.AppendLine("正在进行AI检测和形态学分析...");

            OutputText.Text = sb.ToString();
            RefreshLogDisplay();

            // 异步执行分析
            LogHelper.WriteInfoLog("开始TensorFlow模型检测...");
            var startTime = DateTime.Now;

            analysisResults = await Task.Run(() => analysisService.AnalyzeImage(currentImagePath, currentSampleId));

            var duration = DateTime.Now - startTime;
            LogHelper.WritePerformanceLog("图像分析", duration);
            LogHelper.WriteInfoLog($"分析完成，检测到 {analysisResults?.Count ?? 0} 个精子");

            // 在图像上标记分析结果
            if (analysisResults != null && analysisResults.Count > 0)
            {
                LogHelper.WriteInfoLog("开始在图像上标记分析结果...");
                DrawAnalysisResults(analysisResults);
                LogHelper.WriteInfoLog("图像标记完成");
            }

            // 保存分析结果到数据库
            LogHelper.WriteInfoLog("保存分析结果到数据库...");
            analysisService.SaveAnalysisResults(analysisResults, currentSampleId, currentImagePath);

            // 计算统计结果
            var statistics = analysisService.CalculateStatistics(analysisResults);

            // 记录分析结果日志
            LogHelper.WriteAnalysisLog(
                System.IO.Path.GetFileName(currentImagePath),
                statistics.TotalCount,
                statistics.NormalCount,
                statistics.AbnormalCount);

            // 更新界面显示
            UpdateAnalysisResults(statistics);

            // 显示详细分析报告
            DisplayDetailedReport(statistics, analysisResults);

            // 启用保存和查看按钮
            SaveResultButton.IsEnabled = true;
            ViewResultsButton.IsEnabled = true;
            StatusText.Text = $"分析完成 - 检测到 {statistics.TotalCount} 个精子";
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"图像分析失败: {ex.Message}");
            LogHelper.WriteErrLog($"错误堆栈: {ex.StackTrace}");

            MessageBox.Show($"图像分析失败: {ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
            StatusText.Text = "分析失败";
            RefreshLogDisplay();
        }
        finally
        {
            // 重新启用按钮
            AnalyzeButton.IsEnabled = true;
            ImportImageButton.IsEnabled = true;

            // 刷新日志显示
            RefreshLogDisplay();
        }
    }

    private void UpdateAnalysisResults(AnalysisResult statistics)
    {
        TotalCountText.Text = statistics.TotalCount.ToString();
        NormalCountText.Text = statistics.NormalCount.ToString();
        AbnormalCountText.Text = statistics.AbnormalCount.ToString();
        NormalRateText.Text = $"{statistics.NormalRate:F2}%";
    }

    private void DisplayDetailedReport(AnalysisResult statistics, List<Sperm> sperms)
    {
        var sb = new StringBuilder();
        sb.AppendLine("=== 精子形态学分析报告 ===");
        sb.AppendLine($"分析时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"图像文件: {System.IO.Path.GetFileName(currentImagePath)}");
        sb.AppendLine($"样本ID: {currentSampleId}");
        sb.AppendLine();

        sb.AppendLine("【统计结果】");
        sb.AppendLine($"检测总数: {statistics.TotalCount}");
        sb.AppendLine($"正常精子: {statistics.NormalCount} ({statistics.NormalRate:F2}%)");
        sb.AppendLine($"异常精子: {statistics.AbnormalCount} ({statistics.AbnormalRate:F2}%)");
        sb.AppendLine();

        if (sperms.Count > 0)
        {
            sb.AppendLine("【形态学特征统计】");
            sb.AppendLine($"平均面积: {sperms.Average(s => s.spermArea):F2}");
            sb.AppendLine($"平均长轴: {sperms.Average(s => s.longAxis):F2}");
            sb.AppendLine($"平均短轴: {sperms.Average(s => s.shortAxis):F2}");
            sb.AppendLine($"平均椭圆比: {sperms.Average(s => s.ellipsRatio):F2}");
            sb.AppendLine($"平均顶体比例: {sperms.Average(s => s.acrosomeRatio):F2}");
            sb.AppendLine();

            sb.AppendLine("【详细检测结果】");
            for (int i = 0; i < Math.Min(10, sperms.Count); i++)
            {
                var sperm = sperms[i];
                sb.AppendLine($"精子 {i + 1}: {(sperm.isNormal == 1 ? "正常" : "异常")} " +
                             $"(面积:{sperm.spermArea:F1}, 椭圆比:{sperm.ellipsRatio:F2}, " +
                             $"置信度:{sperm.score:F3})");
            }

            if (sperms.Count > 10)
            {
                sb.AppendLine($"... 还有 {sperms.Count - 10} 个精子的检测结果");
            }
        }

        sb.AppendLine();
        sb.AppendLine("分析完成！可以点击'保存结果'将数据保存到数据库。");

        OutputText.Text = sb.ToString();
    }

    private async void SaveResultButton_Click(object sender, RoutedEventArgs e)
    {
        if (analysisResults == null || analysisResults.Count == 0)
        {
            MessageBox.Show("没有分析结果可保存", "提示",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            SaveResultButton.IsEnabled = false;
            StatusText.Text = "正在保存分析结果...";

            await Task.Run(() => SaveAnalysisResults());

            MessageBox.Show($"分析结果已成功保存到数据库！\n\n" +
                           $"样本ID: {currentSampleId}\n" +
                           $"精子总数: {analysisResults.Count}\n" +
                           $"正常精子: {analysisResults.Count(s => s.isNormal == 1)}",
                           "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);

            StatusText.Text = "分析结果已保存";

            // 重置界面，准备下一次分析
            ResetForNewAnalysis();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存分析结果失败: {ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
            StatusText.Text = "保存失败";
        }
        finally
        {
            SaveResultButton.IsEnabled = true;
        }
    }

    private void SaveAnalysisResults()
    {
        try
        {
            // 简化：直接使用分析服务保存
            analysisService?.SaveAnalysisResults(analysisResults, currentSampleId, currentImagePath);
            MessageBox.Show("分析结果已保存！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ResetForNewAnalysis()
    {
        // 生成新的样本ID
        currentSampleId = Guid.NewGuid().ToString();

        // 清空当前数据
        currentImagePath = string.Empty;
        analysisResults = new List<Sperm>();

        // 重置界面
        TotalCountText.Text = "0";
        NormalCountText.Text = "0";
        AbnormalCountText.Text = "0";
        NormalRateText.Text = "0%";

        AnalyzeButton.IsEnabled = false;
        SaveResultButton.IsEnabled = false;
        ViewResultsButton.IsEnabled = false;

        OutputText.Text = "系统已重置，可以导入新的图像进行分析。";
        StatusText.Text = "准备就绪";
    }

    private void ViewResultsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (analysisResults != null && analysisResults.Count > 0 && !string.IsNullOrEmpty(currentImagePath))
            {
                var imageViewWindow = new Windows.ImageViewWindow(analysisResults, currentImagePath);
                imageViewWindow.Show();
            }
            else
            {
                MessageBox.Show("没有可查看的分析结果，请先进行图像分析。", "提示",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"打开结果查看窗口失败: {ex.Message}", "错误",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PreloadTensorFlowModel()
    {
        try
        {
            LogHelper.WriteInfoLog("开始TensorFlow预加载...");

            // 使用TensorFlowHelper进行安全初始化
            bool success = TensorFlowHelper.TryInitialize();

            // 在UI线程更新状态
            Dispatcher.Invoke(() =>
            {
                if (success)
                {
                    StatusText.Text = "TensorFlow预加载完成，系统准备就绪";
                    LogHelper.WriteInfoLog("TensorFlow预加载成功");
                }
                else
                {
                    StatusText.Text = $"TensorFlow预加载失败: {TensorFlowHelper.LastInitError?.Message ?? "未知错误"}";
                    LogHelper.WriteErrLog($"TensorFlow预加载失败: {TensorFlowHelper.GetStatusInfo()}");
                }
                RefreshLogDisplay();
            });
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"预加载过程异常: {ex.Message}");
            Dispatcher.Invoke(() =>
            {
                StatusText.Text = $"预加载异常: {ex.Message}";
                RefreshLogDisplay();
            });
        }
    }

    private void LoadAndDisplayImage(string imagePath)
    {
        try
        {
            // 清除之前的绘制痕迹
            ClearAllDrawings();

            // 加载图像
            originalBitmap = new BitmapImage();
            originalBitmap.BeginInit();
            originalBitmap.UriSource = new Uri(imagePath);
            originalBitmap.EndInit();

            // 显示图像
            DisplayImage.Source = originalBitmap;

            // 等待布局更新后设置Canvas大小
            Dispatcher.BeginInvoke(new Action(() =>
            {
                SetupImageDisplay();
            }), System.Windows.Threading.DispatcherPriority.Loaded);

            // 重置缩放
            currentZoom = 1.0;

            LogHelper.WriteInfoLog($"图像加载成功: {originalBitmap.PixelWidth}x{originalBitmap.PixelHeight}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"加载图像失败: {ex.Message}");
            throw;
        }
    }

    private void MainWindow_SizeChanged(object sender, SizeChangedEventArgs e)
    {
        // 延迟重新设置图片显示
        Dispatcher.BeginInvoke(new Action(() =>
        {
            SetupImageDisplay();
            RefreshAnalysisDisplay();
        }), System.Windows.Threading.DispatcherPriority.Background);
    }

    private void ImageContainer_SizeChanged(object sender, SizeChangedEventArgs e)
    {
        // 延迟重新设置图片显示
        Dispatcher.BeginInvoke(new Action(() =>
        {
            SetupImageDisplay();
            RefreshAnalysisDisplay();
        }), System.Windows.Threading.DispatcherPriority.Background);
    }

    /// <summary>
    /// 刷新分析结果显示
    /// </summary>
    private void RefreshAnalysisDisplay()
    {
        if (analysisResults != null && analysisResults.Count > 0)
        {
            DrawAnalysisResults(analysisResults);
        }
    }

    /// <summary>
    /// 设置图片显示，让图片适应控件大小并保持宽高比
    /// </summary>
    private void SetupImageDisplay()
    {
        if (originalBitmap == null || ImageContainer.ActualWidth <= 0 || ImageContainer.ActualHeight <= 0)
            return;

        try
        {
            // 获取容器的实际大小
            double containerWidth = ImageContainer.ActualWidth;
            double containerHeight = ImageContainer.ActualHeight;

            // 获取图片的原始大小
            double imageWidth = originalBitmap.PixelWidth;
            double imageHeight = originalBitmap.PixelHeight;

            // 计算缩放比例，保持宽高比
            double scaleX = containerWidth / imageWidth;
            double scaleY = containerHeight / imageHeight;
            double scale = Math.Min(scaleX, scaleY);

            // 计算显示大小
            double displayWidth = imageWidth * scale;
            double displayHeight = imageHeight * scale;

            // 设置图片大小
            DisplayImage.Width = displayWidth;
            DisplayImage.Height = displayHeight;

            // 设置Canvas大小为显示大小
            DrawingCanvas.Width = displayWidth;
            DrawingCanvas.Height = displayHeight;

            // 居中显示
            Canvas.SetLeft(DisplayImage, 0);
            Canvas.SetTop(DisplayImage, 0);

            // 记录缩放比例，用于坐标转换
            imageDisplayScale = scale;

            LogHelper.WriteInfoLog($"图片显示设置: 原始({imageWidth}x{imageHeight}) -> 显示({displayWidth:F1}x{displayHeight:F1}), 缩放比例: {scale:F3}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"设置图片显示失败: {ex.Message}");
        }
    }

    private void DrawAnalysisResults(List<Sperm> results)
    {
        try
        {
            // 清除之前的标记
            var elementsToRemove = DrawingCanvas.Children.OfType<FrameworkElement>()
                .Where(e => e.Tag?.ToString() == "AnalysisResult").ToList();

            foreach (var element in elementsToRemove)
            {
                DrawingCanvas.Children.Remove(element);
            }

            if (results == null || results.Count == 0) return;

            foreach (var sperm in results)
            {
                // 绘制检测框
                if (ShowDetectionBoxes.IsChecked == true)
                {
                    DrawDetectionBox(sperm);
                }

                // 绘制分割轮廓
                if (ShowSegmentation.IsChecked == true)
                {
                    DrawSegmentationContours(sperm);
                }

                // 绘制标签
                if (ShowLabels.IsChecked == true)
                {
                    DrawSpermLabel(sperm);
                }
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"绘制分析结果失败: {ex.Message}");
        }
    }

    private void DrawDetectionBox(Sperm sperm)
    {
        try
        {
            // 获取图像尺寸
            if (originalBitmap == null) return;

            double imgWidth = originalBitmap.PixelWidth;
            double imgHeight = originalBitmap.PixelHeight;

            // 计算实际的绘制坐标（参考原项目的getBox逻辑）
            double x, y, width, height;
            GetSpermBox(sperm, imgWidth, imgHeight, out x, out y, out width, out height);

            var rect = new Rectangle
            {
                Width = width,
                Height = height,
                Stroke = sperm.isNormal == 1 ? Brushes.Green : Brushes.Red,
                StrokeThickness = 2,
                Fill = Brushes.Transparent,
                Tag = "AnalysisResult"
            };

            Canvas.SetLeft(rect, x);
            Canvas.SetTop(rect, y);
            DrawingCanvas.Children.Add(rect);

            // 简化日志：只记录第一个精子的坐标信息
            if (sperm.spermindex == 1)
            {
                LogHelper.WriteDebugLog($"检测框示例: 精子{sperm.spermindex}, 坐标({x:F1},{y:F1}), 尺寸({width:F1}x{height:F1})");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"绘制检测框失败: {ex.Message}");
        }
    }

    private void DrawSegmentationContours(Sperm sperm)
    {
        if (originalBitmap == null) return;

        double imgWidth = originalBitmap.PixelWidth;
        double imgHeight = originalBitmap.PixelHeight;

        // 计算原始图像坐标系中的偏移量（不应用显示缩放）
        double resolutionRatioWidth = 1920.0;
        double resolutionRatioHeight = 1200.0;

        double originalOffsetX = imgWidth * sperm.xmin - 20.0 * imgWidth / resolutionRatioWidth;
        originalOffsetX = (originalOffsetX < 0.0) ? 0.0 : originalOffsetX;

        double originalOffsetY = (int)(imgHeight * sperm.ymin) - 20.0 * imgHeight / resolutionRatioHeight;
        originalOffsetY = (originalOffsetY < 0.0) ? 0.0 : originalOffsetY;

        // 绘制精子轮廓
        if (!string.IsNullOrEmpty(sperm.vop_sperm))
        {
            DrawContourWithOffset(sperm.vop_sperm, sperm.isNormal == 1 ? Brushes.LightGreen : Brushes.LightCoral, 1,
                                originalOffsetX, originalOffsetY, imgWidth, imgHeight);
        }

        // 绘制顶体轮廓
        if (!string.IsNullOrEmpty(sperm.vop_acrosome))
        {
            DrawContourWithOffset(sperm.vop_acrosome, Brushes.Blue, 1,
                                originalOffsetX, originalOffsetY, imgWidth, imgHeight);
        }

        // 绘制核轮廓
        if (!string.IsNullOrEmpty(sperm.vop_kernel))
        {
            DrawContourWithOffset(sperm.vop_kernel, Brushes.Orange, 1,
                                originalOffsetX, originalOffsetY, imgWidth, imgHeight);
        }

        // 记录第一个精子的尾巴数据
        if (sperm.spermindex == 1)
        {
            LogHelper.WriteDebugLog($"我的项目尾巴数据: 精子{sperm.spermindex}");
            LogHelper.WriteDebugLog($"  vop_middlePiece: '{sperm.vop_middlePiece}' (长度:{sperm.vop_middlePiece?.Length ?? 0})");
            LogHelper.WriteDebugLog($"  vop_tail: '{sperm.vop_tail}' (长度:{sperm.vop_tail?.Length ?? 0})");
            LogHelper.WriteDebugLog($"  偏移量: ({originalOffsetX:F1},{originalOffsetY:F1})");
        }

        // 绘制中段轮廓（使用原项目的绿色）
        if (!string.IsNullOrEmpty(sperm.vop_middlePiece))
        {
            var middlePieceBrush = new SolidColorBrush(Color.FromRgb(15, 187, 18));
            DrawTailWithOffset(sperm.vop_middlePiece, middlePieceBrush, 2,
                             originalOffsetX, originalOffsetY, imgWidth, imgHeight);
        }

        // 绘制尾巴轮廓（使用随机颜色，与原项目保持一致）
        if (!string.IsNullOrEmpty(sperm.vop_tail))
        {
            var random = new Random();
            var tailBrush = new SolidColorBrush(Color.FromRgb(
                (byte)random.Next(0, 20),
                (byte)random.Next(0, 128),
                (byte)random.Next(100, 200)));
            DrawTailWithOffset(sperm.vop_tail, tailBrush, 1,
                             originalOffsetX, originalOffsetY, imgWidth, imgHeight);
        }
    }

    private void DrawContour(string contourData, Brush brush, double thickness)
    {
        try
        {
            if (string.IsNullOrEmpty(contourData)) return;

            var points = ParseContourPoints(contourData);
            if (points.Count < 3) return;

            var polygon = new Polygon
            {
                Points = new PointCollection(points),
                Stroke = brush,
                StrokeThickness = thickness,
                Fill = Brushes.Transparent,
                Tag = "AnalysisResult"
            };

            DrawingCanvas.Children.Add(polygon);

            LogHelper.WriteDebugLog($"绘制轮廓: {points.Count}个点, 颜色{brush}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"绘制轮廓失败: {ex.Message}");
        }
    }

    private void DrawContourWithOffset(string contourData, Brush brush, double thickness,
                                     double offsetX, double offsetY, double imgWidth, double imgHeight)
    {
        try
        {
            if (string.IsNullOrEmpty(contourData)) return;

            var rawPoints = ParseContourPoints(contourData);
            if (rawPoints.Count < 3) return;

            // 按照原项目逻辑转换坐标
            var transformedPoints = new List<Point>();

            // 原项目的分辨率比例（根据日志修正）
            double resolutionRatioWidth = 1920.0;  // Basic.imageSize.Width
            double resolutionRatioHeight = 1200.0;   // Basic.imageSize.Height

            // 计算当前显示的图像尺寸
            double displayWidth = imgWidth * imageDisplayScale;
            double displayHeight = imgHeight * imageDisplayScale;

            // offsetX和offsetY现在直接是原始图像坐标系中的偏移量
            double originalOffsetX = offsetX;
            double originalOffsetY = offsetY;

            foreach (var rawPoint in rawPoints)
            {
                // 完全按照原项目Drawing.cs的逻辑：
                // value.X = (float)((num + (double)array2[k].X) * width / (double)Basic.imageSize.Width + point.X);
                // value.Y = (float)((num2 + (double)array2[k].Y) * height / (double)Basic.imageSize.Height + point.Y);
                // 其中：
                // - num, num2 是精子框的偏移（原始图像坐标系）
                // - array2[k].X, array2[k].Y 是轮廓的相对坐标
                // - width, height 是显示尺寸
                // - Basic.imageSize.Width/Height 是基准分辨率（1344x840）
                // - point.X, point.Y 是画布偏移，在我们的情况下为0

                double transformedX = (originalOffsetX + rawPoint.X) * displayWidth / resolutionRatioWidth;
                double transformedY = (originalOffsetY + rawPoint.Y) * displayHeight / resolutionRatioHeight;

                transformedPoints.Add(new Point(transformedX, transformedY));
            }

            var polygon = new Polygon
            {
                Points = new PointCollection(transformedPoints),
                Stroke = brush,
                StrokeThickness = thickness,
                Fill = Brushes.Transparent,
                Tag = "AnalysisResult"
            };

            DrawingCanvas.Children.Add(polygon);

            // 简化日志：只记录关键信息
            LogHelper.WriteDebugLog($"轮廓转换: {rawPoints.Count}点 -> 偏移({originalOffsetX:F1},{originalOffsetY:F1}), 缩放:{imageDisplayScale:F3}");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"绘制轮廓(带偏移)失败: {ex.Message}");
        }
    }

    private void DrawTailWithOffset(string tailData, Brush brush, double thickness,
                                   double offsetX, double offsetY, double imgWidth, double imgHeight)
    {
        try
        {
            if (string.IsNullOrEmpty(tailData))
            {
                LogHelper.WriteDebugLog($"尾巴数据为空，跳过绘制");
                return;
            }

            var rawPoints = ParseContourPoints(tailData);
            LogHelper.WriteDebugLog($"尾巴解析结果: {rawPoints.Count}个点");
            if (rawPoints.Count < 2)
            {
                LogHelper.WriteDebugLog($"尾巴点数不足({rawPoints.Count}<2)，跳过绘制");
                return; // 至少需要2个点才能画线
            }

            // 原项目的分辨率比例（根据日志修正）
            double resolutionRatioWidth = 1920.0;
            double resolutionRatioHeight = 1200.0;

            // 计算当前显示的图像尺寸
            double displayWidth = imgWidth * imageDisplayScale;
            double displayHeight = imgHeight * imageDisplayScale;

            // offsetX和offsetY现在直接是原始图像坐标系中的偏移量
            double originalOffsetX = offsetX;
            double originalOffsetY = offsetY;

            // 绘制尾巴线段（按照原项目DrawTails的逻辑）
            for (int i = 0; i < rawPoints.Count - 1; i++)
            {
                // 计算起点和终点坐标
                double startX = (originalOffsetX + rawPoints[i].X) * displayWidth / resolutionRatioWidth;
                double startY = (originalOffsetY + rawPoints[i].Y) * displayHeight / resolutionRatioHeight;

                double endX = (originalOffsetX + rawPoints[i + 1].X) * displayWidth / resolutionRatioWidth;
                double endY = (originalOffsetY + rawPoints[i + 1].Y) * displayHeight / resolutionRatioHeight;

                // 创建线段
                var line = new Line
                {
                    X1 = startX,
                    Y1 = startY,
                    X2 = endX,
                    Y2 = endY,
                    Stroke = brush,
                    StrokeThickness = thickness
                };

                DrawingCanvas.Children.Add(line);
            }

            // 简化日志：只记录关键信息
            LogHelper.WriteDebugLog($"尾巴绘制: {rawPoints.Count}点 -> {rawPoints.Count - 1}线段, 偏移({originalOffsetX:F1},{originalOffsetY:F1})");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"绘制尾巴失败: {ex.Message}");
        }
    }

    private List<Point> ParseContourPoints(string contourData)
    {
        var points = new List<Point>();

        if (string.IsNullOrEmpty(contourData)) return points;

        try
        {
            // 原项目格式：用空格分隔点，每个点用逗号分隔坐标
            var pointStrings = contourData.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var pointString in pointStrings)
            {
                if (string.IsNullOrEmpty(pointString.Trim())) continue;

                var coords = pointString.Split(',');
                if (coords.Length == 2 &&
                    double.TryParse(coords[0].Trim(), out double x) &&
                    double.TryParse(coords[1].Trim(), out double y))
                {
                    points.Add(new Point(x, y));
                }
            }

            // 简化日志：只记录点数
            if (points.Count > 0)
            {
                LogHelper.WriteDebugLog($"解析轮廓: {points.Count}个点");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"解析轮廓点失败: {ex.Message}");
        }

        return points;
    }

    private void ClearAllDrawings()
    {
        try
        {
            // 清除Canvas上的所有绘制元素
            DrawingCanvas.Children.Clear();

            // 重置分析结果
            analysisResults = null;

            LogHelper.WriteDebugLog("已清除所有绘制痕迹");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"清除绘制痕迹失败: {ex.Message}");
        }
    }

    private void DrawSpermLabel(Sperm sperm)
    {
        try
        {
            if (originalBitmap == null) return;

            double imgWidth = originalBitmap.PixelWidth;
            double imgHeight = originalBitmap.PixelHeight;

            // 获取正确的坐标
            double x, y, width, height;
            GetSpermBox(sperm, imgWidth, imgHeight, out x, out y, out width, out height);

            var label = new TextBlock
            {
                Text = $"{sperm.spermindex}",
                Foreground = sperm.isNormal == 1 ? Brushes.Green : Brushes.Red,
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Background = Brushes.White,
                Tag = "AnalysisResult"
            };

            Canvas.SetLeft(label, x);
            Canvas.SetTop(label, y - 20);
            DrawingCanvas.Children.Add(label);

            // 简化日志：移除标签绘制日志
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"绘制精子标签失败: {ex.Message}");
        }
    }

    private void RefreshLogDisplay()
    {
        try
        {
            string logContent = LogHelper.GetTodayLogContent();

            // 添加日志头部信息
            var header = new StringBuilder();
            header.AppendLine("=== 精子形态分析系统日志 ===");
            header.AppendLine($"日期: {DateTime.Now:yyyy-MM-dd}");
            header.AppendLine($"刷新时间: {DateTime.Now:HH:mm:ss}");
            header.AppendLine($"系统版本: .NET 8.0 WPF");
            header.AppendLine("================================");
            header.AppendLine();

            LogText.Text = header.ToString() + logContent;

            // 自动滚动到底部
            LogText.ScrollToEnd();
        }
        catch (Exception ex)
        {
            LogText.Text = $"读取日志失败: {ex.Message}";
        }
    }

    private void RefreshLogButton_Click(object sender, RoutedEventArgs e)
    {
        RefreshLogDisplay();
    }

    /// <summary>
    /// 获取精子的显示框坐标（完全参考原项目的getBox方法）
    /// </summary>
    private void GetSpermBox(Sperm sperm, double imgWidth, double imgHeight, out double x, out double y, out double width, out double height)
    {
        x = y = width = height = 0.0;

        if (sperm == null) return;

        try
        {
            // 完全按照原项目clsMrfSegment.getBox的逻辑
            // 根据原项目日志，分辨率比例应该是1920x1200
            double resolutionRatioWidth = 1920.0;  // TFmodel.common.resolutionRatio.Width
            double resolutionRatioHeight = 1200.0;   // TFmodel.common.resolutionRatio.Height

            // 原项目的精确计算逻辑
            x = imgWidth * sperm.xmin - 20.0 * imgWidth / resolutionRatioWidth;
            x = (x < 0.0) ? 0.0 : x;

            width = imgWidth * (sperm.xmax - sperm.xmin) + 40.0 * imgWidth / resolutionRatioWidth;
            if (x + width >= imgWidth)
            {
                width = imgWidth - x - 1.0;
            }

            y = (int)(imgHeight * sperm.ymin) - 20.0 * imgHeight / resolutionRatioHeight;
            y = (y < 0.0) ? 0.0 : y;

            height = imgHeight * (sperm.ymax - sperm.ymin) + 40.0 * imgHeight / resolutionRatioHeight;
            if (y + height >= imgHeight)
            {
                height = imgHeight - y - 1.0;
            }

            // 简化日志：只记录第一个精子的详细信息
            if (sperm.spermindex == 1)
            {
                LogHelper.WriteDebugLog($"getBox计算: 原始({sperm.xmin:F3},{sperm.ymin:F3}), 原始框({x:F1},{y:F1}), 尺寸({width:F1}x{height:F1})");
            }

            // 应用显示缩放比例（转换到显示坐标系）
            x *= imageDisplayScale;
            y *= imageDisplayScale;
            width *= imageDisplayScale;
            height *= imageDisplayScale;

            // 简化日志：只记录第一个精子的最终显示坐标
            if (sperm.spermindex == 1)
            {
                LogHelper.WriteDebugLog($"显示坐标: ({x:F1},{y:F1}), 尺寸({width:F1}x{height:F1}), 缩放:{imageDisplayScale:F3}");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"计算精子框坐标失败: {ex.Message}");
            // 使用简单的比例缩放作为备用
            x = sperm.xmin * imgWidth * imageDisplayScale;
            y = sperm.ymin * imgHeight * imageDisplayScale;
            width = (sperm.xmax - sperm.xmin) * imgWidth * imageDisplayScale;
            height = (sperm.ymax - sperm.ymin) * imgHeight * imageDisplayScale;
        }
    }

    private void CopyLogButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (!string.IsNullOrEmpty(LogText.Text))
            {
                Clipboard.SetText(LogText.Text);
                StatusText.Text = "日志已复制到剪贴板";
                LogHelper.WriteInfoLog("用户复制了日志内容");
            }
            else
            {
                StatusText.Text = "没有日志内容可复制";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"复制日志失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            LogHelper.WriteErrLog($"复制日志失败: {ex.Message}");
        }
    }

    private void ClearLogButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LogText.Text = "日志显示已清空（实际日志文件未删除）";
            StatusText.Text = "日志显示已清空";
            LogHelper.WriteInfoLog("用户清空了日志显示");
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"清空日志显示失败: {ex.Message}");
        }
    }

    private void CheckTensorFlowButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LogHelper.WriteInfoLog("用户请求检查TensorFlow状态");

            var sb = new StringBuilder();
            sb.AppendLine("=== TensorFlow状态检查 ===");
            sb.AppendLine($"检查时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            // 检查初始化状态
            sb.AppendLine($"初始化状态: {TensorFlowHelper.GetStatusInfo()}");
            sb.AppendLine($"是否已初始化: {TensorFlowHelper.IsInitialized}");

            if (TensorFlowHelper.LastInitError != null)
            {
                sb.AppendLine($"最后错误: {TensorFlowHelper.LastInitError.Message}");
            }

            // 检查文件
            sb.AppendLine();
            sb.AppendLine("目录信息:");
            string currentDir = Environment.CurrentDirectory;
            string appDir = AppDomain.CurrentDomain.BaseDirectory;
            string assemblyDir = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? "";

            sb.AppendLine($"  当前工作目录: {currentDir}");
            sb.AppendLine($"  应用程序基目录: {appDir}");
            sb.AppendLine($"  程序集目录: {assemblyDir}");

            sb.AppendLine();
            sb.AppendLine("文件检查:");
            string[] requiredFiles = {
                "libtensorflow.dll",
                "TFRunModel.dll",
                "TFCommon.dll",
                "TensorFlowSharp.dll",
                "tfmodel\\frozen_inference_graph_mrf.pb",
                "configure\\manager.config"
            };

            // 检查多个可能的目录
            string[] possibleDirs = { currentDir, appDir, assemblyDir };

            foreach (string file in requiredFiles)
            {
                bool found = false;
                string foundPath = "";

                foreach (string dir in possibleDirs)
                {
                    string filePath = System.IO.Path.Combine(dir, file);
                    if (File.Exists(filePath))
                    {
                        found = true;
                        foundPath = filePath;
                        break;
                    }
                }

                if (found)
                {
                    sb.AppendLine($"  {file}: ✓ 存在 ({foundPath})");
                }
                else
                {
                    sb.AppendLine($"  {file}: ✗ 缺失");
                }
            }

            OutputText.Text = sb.ToString();
            StatusText.Text = "TensorFlow状态检查完成";
            RefreshLogDisplay();
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"TensorFlow状态检查失败: {ex.Message}");
            MessageBox.Show($"状态检查失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ResetTensorFlowButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LogHelper.WriteInfoLog("用户请求重置TensorFlow");

            // 重置TensorFlow状态
            TensorFlowHelper.Reset();

            // 重新尝试初始化
            StatusText.Text = "正在重新初始化TensorFlow...";

            var resetThread = new System.Threading.Thread(() =>
            {
                bool success = TensorFlowHelper.TryInitialize();

                Dispatcher.Invoke(() =>
                {
                    if (success)
                    {
                        StatusText.Text = "TensorFlow重置并初始化成功";
                        OutputText.Text = "TensorFlow已重置并重新初始化成功！\n\n现在可以尝试重新分析图像。";
                        LogHelper.WriteInfoLog("TensorFlow重置成功");
                    }
                    else
                    {
                        StatusText.Text = "TensorFlow重置后初始化失败";
                        OutputText.Text = $"TensorFlow重置后初始化失败:\n\n{TensorFlowHelper.GetStatusInfo()}";
                        LogHelper.WriteErrLog("TensorFlow重置后初始化失败");
                    }
                    RefreshLogDisplay();
                });
            });

            resetThread.IsBackground = true;
            resetThread.Start();
        }
        catch (Exception ex)
        {
            LogHelper.WriteErrLog($"TensorFlow重置失败: {ex.Message}");
            MessageBox.Show($"重置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}