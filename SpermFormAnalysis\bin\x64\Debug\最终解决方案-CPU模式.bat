@echo off
setlocal enabledelayedexpansion
title 精子形态分析系统 - 最终CPU模式解决方案
color 0B

echo ========================================
echo   精子形态分析系统 - 最终CPU模式解决方案
echo ========================================
echo.
echo 此方案将彻底解决MX450显卡内存不足问题
echo.

echo [步骤1] 检查程序文件...
if not exist "SpermFormAnalysis.exe" (
    echo ✗ 错误：找不到SpermFormAnalysis.exe
    pause
    exit /b 1
)
echo ✓ 程序文件存在

echo.
echo [步骤2] 设置系统环境变量...
REM 在系统级别设置环境变量
setx CUDA_VISIBLE_DEVICES "-1" >nul 2>&1
setx TF_CPP_MIN_LOG_LEVEL "2" >nul 2>&1
setx TF_FORCE_GPU_ALLOW_GROWTH "false" >nul 2>&1
echo ✓ 系统环境变量已设置

echo.
echo [步骤3] 设置当前会话环境变量...
set CUDA_VISIBLE_DEVICES=-1
set TF_CPP_MIN_LOG_LEVEL=2
set TF_FORCE_GPU_ALLOW_GROWTH=false
set NVIDIA_VISIBLE_DEVICES=none
set TF_DEVICE_MIN_SYS_MEMORY_IN_MB=4096
set OMP_NUM_THREADS=4
echo ✓ 当前会话环境变量已设置

echo.
echo [步骤4] 创建专用配置文件...
echo # MX450专用CPU模式配置 > gpu_config.ini
echo [GPU设置] >> gpu_config.ini
echo ForceUseCPU=true >> gpu_config.ini
echo GPUMemoryOptimized=false >> gpu_config.ini
echo GPUMemoryLimit=0 >> gpu_config.ini
echo EnableTailProcessing=true >> gpu_config.ini
echo AutoDetectGPU=false >> gpu_config.ini
echo. >> gpu_config.ini
echo # 此配置强制使用CPU模式，适用于MX450等低端显卡 >> gpu_config.ini
echo ✓ 配置文件已创建

echo.
echo [步骤5] 显示当前环境配置...
echo     CUDA_VISIBLE_DEVICES = %CUDA_VISIBLE_DEVICES%
echo     TF_CPP_MIN_LOG_LEVEL = %TF_CPP_MIN_LOG_LEVEL%
echo     TF_FORCE_GPU_ALLOW_GROWTH = %TF_FORCE_GPU_ALLOW_GROWTH%
echo     OMP_NUM_THREADS = %OMP_NUM_THREADS%

echo.
echo [步骤6] 启动程序...
echo ========================================
echo              重要提示
echo ========================================
echo 1. 程序将在纯CPU模式下运行
echo 2. 尾巴信息处理时间：1-3分钟
echo 3. 请耐心等待，不要重复点击
echo 4. 建议关闭其他占用CPU的程序
echo ========================================
echo.

echo 正在启动程序，请稍候...
start "" "SpermFormAnalysis.exe"

echo ✓ 程序已启动
echo.
echo 程序运行状态监控：
echo - 如果程序正常启动，此窗口可以关闭
echo - 如果程序崩溃，请查看Log文件夹中的日志
echo - 如果尾巴信息仍无法显示，可能需要更新TensorFlow版本
echo.

echo 测试步骤：
echo 1. 上传精子图像
echo 2. 点击开始分析（等待1-3分钟）
echo 3. 分析完成后点击"实验查询"
echo 4. 检查尾巴信息是否正常显示
echo.

echo 按任意键关闭此窗口...
pause >nul

echo.
echo 正在清理临时设置...
REM 清理系统环境变量（可选）
REM setx CUDA_VISIBLE_DEVICES "" >nul 2>&1
echo ✓ 清理完成

endlocal
