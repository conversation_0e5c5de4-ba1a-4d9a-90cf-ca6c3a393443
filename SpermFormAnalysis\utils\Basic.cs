﻿using Prism.Mvvm;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.utils
{
    public static class Basic
    {
        public static int threadNum { get; set; } = 10;

        public static Size imageSize { get; set; } = new Size(1920, 1200);

        public static ObservableCollection<string> getSpermType(string type)
        {
            bool flag = type == "DFI" || type == "MAT";
            ObservableCollection<string> result;
            if (flag)
            {
                result = Basic.spermTypeBasic;
            }
            else
            {
                result = Basic.spermTypeBasicMrf;
            }
            return result;
        }

        public static ObservableCollection<string> getHeadType(string type)
        {
            bool flag = type == "MRF";
            ObservableCollection<string> result;
            if (flag)
            {
                result = Basic.spermHeadType;
            }
            else
            {
                result = null;
            }
            return result;
        }

        public static ObservableCollection<string> getAcroType(string type)
        {
            bool flag = type == "MRF";
            ObservableCollection<string> result;
            if (flag)
            {
                result = Basic.spermAcroType;
            }
            else
            {
                result = null;
            }
            return result;
        }

        public static ObservableCollection<string> getKernelType(string type)
        {
            bool flag = type == "MRF";
            ObservableCollection<string> result;
            if (flag)
            {
                result = Basic.spermKernelType;
            }
            else
            {
                result = null;
            }
            return result;
        }

        public static ObservableCollection<string> getMDPTailType(string type)
        {
            bool flag = type == "MRF";
            ObservableCollection<string> result;
            if (flag)
            {
                result = Basic.spermMDPTailType;
            }
            else
            {
                result = null;
            }
            return result;
        }

        public static ObservableCollection<string> getTailType(string type)
        {
            bool flag = type == "MRF";
            ObservableCollection<string> result;
            if (flag)
            {
                result = Basic.spermTailType;
            }
            else
            {
                result = null;
            }
            return result;
        }

        //public static BitmapImage getImage(string imagePath)
        //{
        //    BitmapImage bitmapImage = new BitmapImage();
        //    bool flag = File.Exists(imagePath);
        //    if (flag)
        //    {
        //        bitmapImage.BeginInit();
        //        bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
        //        using (Stream stream = new MemoryStream(File.ReadAllBytes(imagePath)))
        //        {
        //            bitmapImage.StreamSource = stream;
        //            bitmapImage.EndInit();
        //            bitmapImage.Freeze();
        //        }
        //    }
        //    return bitmapImage;
        //}

        public static string GetDes(Enum value)
        {
            return ((DescriptionAttribute)Attribute.GetCustomAttribute(value.GetType().GetField(value.ToString()), typeof(DescriptionAttribute))).Description;
        }

        public static string GetDescription(this Enum enumName)
        {
            string result = string.Empty;
            FieldInfo field = enumName.GetType().GetField(enumName.ToString());
            DescriptionAttribute[] descriptAttr = field.GetDescriptAttr();
            bool flag = descriptAttr != null && descriptAttr.Length != 0;
            if (flag)
            {
                result = descriptAttr[0].Description;
            }
            else
            {
                result = enumName.ToString();
            }
            return result;
        }

        public static DescriptionAttribute[] GetDescriptAttr(this FieldInfo fieldInfo)
        {
            bool flag = fieldInfo != null;
            DescriptionAttribute[] result;
            if (flag)
            {
                result = (DescriptionAttribute[])fieldInfo.GetCustomAttributes(typeof(DescriptionAttribute), false);
            }
            else
            {
                result = null;
            }
            return result;
        }

        public static T GetEnumName<T>(string description)
        {
            Type typeFromHandle = typeof(T);
            foreach (FieldInfo fieldInfo in typeFromHandle.GetFields())
            {
                DescriptionAttribute[] descriptAttr = fieldInfo.GetDescriptAttr();
                bool flag = descriptAttr != null && descriptAttr.Length != 0;
                if (flag)
                {
                    bool flag2 = descriptAttr[0].Description == description;
                    if (flag2)
                    {
                        return (T)((object)fieldInfo.GetValue(null));
                    }
                }
                else
                {
                    bool flag3 = fieldInfo.Name == description;
                    if (flag3)
                    {
                        return (T)((object)fieldInfo.GetValue(null));
                    }
                }
            }
            throw new ArgumentException(string.Format("{0} 未能找到对应的枚举.", description), "Description");
        }

        public static void createDir(string dir)
        {
            string text = Directory.GetCurrentDirectory().ToString();
            text += dir;
            bool flag = !Directory.Exists(text);
            if (flag)
            {
                Directory.CreateDirectory(text);
            }
        }

        public static void createFullDir(string dir)
        {
            bool flag = !Directory.Exists(dir);
            if (flag)
            {
                Directory.CreateDirectory(dir);
            }
        }

        public static string getMD5Encrypt(string strText)
        {
            string text = "";
            MD5 md = new MD5CryptoServiceProvider();
            byte[] array = md.ComputeHash(Encoding.Default.GetBytes(strText));
            foreach (byte b in array)
            {
                text += b.ToString();
            }
            return text;
        }



        // Token: 0x04000541 RID: 1345
        public static string ProductName = "精子形态分析处理软件—星博智造";

        // Token: 0x04000542 RID: 1346
        public static string ProductVer = "";

        // Token: 0x04000543 RID: 1347
        public static string CompanyName = "";

        // Token: 0x04000544 RID: 1348
        public static string title = Basic.ProductName;

        // Token: 0x04000545 RID: 1349
        public static bool bUseKey = true;

        // Token: 0x04000546 RID: 1350
        public static bool bTailused = true;

        // Token: 0x04000547 RID: 1351
        public static bool bCrfAdjust = false;

        // Token: 0x04000548 RID: 1352
        public static bool bUseICCard = false;

        // Token: 0x04000549 RID: 1353
        public static bool bUseMachine = false;

        // Token: 0x0400054A RID: 1354
        public static bool bICCardConnected = false;

        // Token: 0x0400054B RID: 1355
        public static bool useCheckcode = false;

        // Token: 0x0400054C RID: 1356
        public static bool bPwdCrypto = true;

        // Token: 0x0400054D RID: 1357
        public static long Balance = 0L;

        // Token: 0x0400054E RID: 1358
        public static List<string> inpectItems = new List<string>
        {
            "形态"
        };

        // Token: 0x0400054F RID: 1359
        public static List<string> sampleStats = new List<string>
        {
            "新建",
            "采集中",
            "采集完成",
            "已完成"
        };

        // Token: 0x04000550 RID: 1360
        public static List<int> days = new List<int>
        {
            1,
            7,
            14,
            30,
            90,
            180,
            365
        };

        // Token: 0x04000551 RID: 1361
        public static bool bHuaXi = false;

        // Token: 0x04000554 RID: 1364
        public static int m_iCoarseAfStepNum = 60;

        // Token: 0x04000555 RID: 1365
        public static int m_iFineAfStepNum = 40;

        // Token: 0x04000556 RID: 1366
        public static double m_dPosZ = 0.0;

        // Token: 0x04000557 RID: 1367
        public static double m_dZ1Speed = 1.0;

        // Token: 0x04000558 RID: 1368
        public static double m_dAFStartPos = 10.04;

        // Token: 0x04000559 RID: 1369
        public static double m_dAFRange = -0.25;

        // Token: 0x0400055A RID: 1370
        public static double m_dPosDelta = 0.004;

        // Token: 0x0400055B RID: 1371
        public static double m_dCenterX = 0.0;

        // Token: 0x0400055C RID: 1372
        public static double m_dCenterY = 0.0;

        // Token: 0x0400055D RID: 1373
        public static double[] dHomeSpdArray = new double[]
        {
            5.0,
            5.0,
            2.0,
            40.0,
            30.0,
            40.0,
            40.0,
            5.0,
            0.5
        };

        // Token: 0x0400055E RID: 1374
        public static string serialPortName = "COM3";

        // Token: 0x0400055F RID: 1375
        public static string ip1 = "***********";

        // Token: 0x04000560 RID: 1376
        public static string ip2 = "***********";

        // Token: 0x04000561 RID: 1377
        public static double[] dAxisSpdArray = new double[]
        {
            5.0,
            5.0,
            2.0,
            40.0,
            20.0,
            12.5,
            15.0,
            2.5,
            0.5
        };

        // Token: 0x04000562 RID: 1378
        public static int oilCount = 2;

        // Token: 0x04000563 RID: 1379
        public static ObservableCollection<string> spermTypeBasic = new ObservableCollection<string>
        {
            Basic.GetDes(Basic.enumSpermTypeBasic.all),
            Basic.GetDes(Basic.enumSpermTypeBasic.normal),
            Basic.GetDes(Basic.enumSpermTypeBasic.abnormal)
        };

        // Token: 0x04000564 RID: 1380
        public static ObservableCollection<string> spermTypeBasicMrf = new ObservableCollection<string>
        {
            Basic.GetDes(Basic.enumSpermTypeBasic.all),
            Basic.GetDes(Basic.enumSpermTypeBasic.normal),
            Basic.GetDes(Basic.enumSpermTypeBasic.subclinical),
            Basic.GetDes(Basic.enumSpermTypeBasic.abnormal)
        };

        // Token: 0x04000565 RID: 1381
        public static ObservableCollection<string> spermHeadType = new ObservableCollection<string>
        {
            Basic.enumHeadType.all.GetDescription(),
            Basic.GetDes(Basic.enumHeadType.normal),
            Basic.GetDes(Basic.enumHeadType.pyriform),
            Basic.GetDes(Basic.enumHeadType.tapered),
            Basic.GetDes(Basic.enumHeadType.round),
            Basic.GetDes(Basic.enumHeadType.mirco),
            Basic.GetDes(Basic.enumHeadType.macro),
            Basic.GetDes(Basic.enumHeadType.irregular),
            Basic.GetDes(Basic.enumHeadType.thin),
            Basic.GetDes(Basic.enumHeadType._short),
            Basic.GetDes(Basic.enumHeadType.tall),
            Basic.GetDes(Basic.enumHeadType.fat)
        };

        // Token: 0x04000566 RID: 1382
        public static ObservableCollection<string> spermAcroType = new ObservableCollection<string>
        {
            Basic.enumAcroType.all.GetDescription(),
            Basic.GetDes(Basic.enumAcroType.normal),
            Basic.GetDes(Basic.enumAcroType.small),
            Basic.GetDes(Basic.enumAcroType.extremly_little),
            Basic.GetDes(Basic.enumAcroType.excessive),
            Basic.GetDes(Basic.enumAcroType.vaculesGT2),
            Basic.GetDes(Basic.enumAcroType.bigVacules),
            Basic.GetDes(Basic.enumAcroType.texture_abnormal)
        };

        // Token: 0x04000567 RID: 1383
        public static ObservableCollection<string> spermKernelType = new ObservableCollection<string>
        {
            Basic.enumKernelType.all.GetDescription(),
            Basic.GetDes(Basic.enumKernelType.normal),
            Basic.GetDes(Basic.enumKernelType.vaculesGT0),
            Basic.GetDes(Basic.enumKernelType.texture_abnormal)
        };

        // Token: 0x04000568 RID: 1384
        public static ObservableCollection<string> spermMDPTailType = new ObservableCollection<string>
        {
            Basic.enumMDPTailType.all.GetDescription(),
            Basic.GetDes(Basic.enumMDPTailType.normal),
            Basic.GetDes(Basic.enumMDPTailType.angle),
            Basic.GetDes(Basic.enumMDPTailType.big),
            Basic.GetDes(Basic.enumMDPTailType.cytoso)
        };

        // Token: 0x04000569 RID: 1385
        public static ObservableCollection<string> spermTailType = new ObservableCollection<string>
        {
            Basic.enumTailType.all.GetDescription(),
            Basic.GetDes(Basic.enumTailType.normal),
            Basic.GetDes(Basic.enumTailType.notail),
            Basic.GetDes(Basic.enumTailType.shorttail),
            Basic.GetDes(Basic.enumTailType.curvetail),
            Basic.GetDes(Basic.enumTailType.bendtail)
        };

        // Token: 0x020000C1 RID: 193
        public enum mStoreStatus
        {
            // Token: 0x0400056B RID: 1387
            [Description("错误")]
            Error = -1,
            // Token: 0x0400056C RID: 1388
            [Description("空闲")]
            Empty,
            // Token: 0x0400056D RID: 1389
            [Description("装载")]
            Loaded,
            // Token: 0x0400056E RID: 1390
            [Description("就绪")]
            Ready,
            // Token: 0x0400056F RID: 1391
            [Description("采集中")]
            Woking = 4,
            // Token: 0x04000570 RID: 1392
            [Description("完成")]
            Finished = 8
        }

        public enum mSlideStatus
        {
            [Description("无玻片")]
            None,
            [Description("已准备")]
            Ready,
            [Description("采集中")]
            Scaning,
            [Description("已完成")]
            Finished,
            [Description("出错")]
            Error
        }

        public enum mCameraStatus
        {
            [Description("错误")]
            Error = -1,
            [Description("未连接")]
            Disconnected,
            [Description("已连接")]
            Connected
        }

        public enum mQRScanStatus
        {
            // Token: 0x0400057C RID: 1404
            [Description("错误")]
            Error = -1,
            // Token: 0x0400057D RID: 1405
            [Description("未连接")]
            Disconnected,
            // Token: 0x0400057E RID: 1406
            [Description("已连接")]
            Connected
        }

        public enum mCtrlStatus
        {
            // Token: 0x04000580 RID: 1408
            [Description("未连接")]
            Disconnected,
            // Token: 0x04000581 RID: 1409
            [Description("连接...")]
            Connecting = 101,
            // Token: 0x04000582 RID: 1410
            [Description("连接错误")]
            ConnectError = 109,
            // Token: 0x04000583 RID: 1411
            [Description("已连接")]
            Connected,
            // Token: 0x04000584 RID: 1412
            [Description("归零...")]
            Homing = 201,
            // Token: 0x04000585 RID: 1413
            [Description("归零错误")]
            HomingError = 209,
            // Token: 0x04000586 RID: 1414
            [Description("已归零")]
            Homed,
            // Token: 0x04000587 RID: 1415
            [Description("数片...")]
            Countingslides = 401,
            // Token: 0x04000588 RID: 1416
            [Description("数片错误")]
            CountingslidesError = 409,
            // Token: 0x04000589 RID: 1417
            [Description("已数片")]
            Countedslides,
            // Token: 0x0400058A RID: 1418
            [Description("就绪")]
            Ready = 800,
            // Token: 0x0400058B RID: 1419
            [Description("取片...")]
            GettingSlide = 1601,
            // Token: 0x0400058C RID: 1420
            [Description("取片错误")]
            GettingSlideError = 1609,
            // Token: 0x0400058D RID: 1421
            [Description("已取片")]
            GotSlide = 1600,
            // Token: 0x0400058E RID: 1422
            [Description("拍照...")]
            TakingPicture = 3201,
            // Token: 0x0400058F RID: 1423
            [Description("拍照错误")]
            TakingPictureError = 3209,
            // Token: 0x04000590 RID: 1424
            [Description("已拍照")]
            TakedPicture,
            // Token: 0x04000591 RID: 1425
            [Description("还片...")]
            ReturningSlide = 6401,
            // Token: 0x04000592 RID: 1426
            [Description("还片错误")]
            ReturningSlideError = 6409,
            // Token: 0x04000593 RID: 1427
            [Description("还片")]
            ReturnedSlide,
            // Token: 0x04000594 RID: 1428
            [Description("完成")]
            Finished = 12800
        }



        public class allStatus : BindableBase
        {
            public int steps
            {
                get
                {
                    return this._steps;
                }
                set
                {
                    this._steps = value;
                    base.RaisePropertyChanged("steps");
                }
            }

            public Basic.mStoreStatus slideStoreStatus0
            {
                get
                {
                    return this._slideStoreStatus0;
                }
                set
                {
                    this._slideStoreStatus0 = value;
                    this._slideStoreStatus0str = Basic.GetDes(value);
                    base.RaisePropertyChanged("slideStoreStatus0");
                    base.RaisePropertyChanged("slideStoreStatus0str");
                }
            }

            public string slideStoreStatus0str
            {
                get
                {
                    return this._slideStoreStatus0str;
                }
                set
                {
                    this._slideStoreStatus0str = value;
                    base.RaisePropertyChanged("slideStoreStatus0str");
                }
            }

            public Basic.mSlideStatus[] slidesStatus0
            {
                get
                {
                    return this._slidesStatus0;
                }
                set
                {
                    this._slidesStatus0 = value;
                    base.RaisePropertyChanged("slidesStatus0");
                }
            }

            public Basic.mStoreStatus slideStoreStatus1
            {
                get
                {
                    return this._slideStoreStatus1;
                }
                set
                {
                    this._slideStoreStatus1 = value;
                    this._slideStoreStatus1str = Basic.GetDes(value);
                    base.RaisePropertyChanged("slideStoreStatus1str");
                    base.RaisePropertyChanged("slideStoreStatus1");
                }
            }

            public string slideStoreStatus1str
            {
                get
                {
                    return this._slideStoreStatus1str;
                }
                set
                {
                    this._slideStoreStatus1str = value;
                    base.RaisePropertyChanged("slideStoreStatus1str");
                }
            }

            public Basic.mSlideStatus[] slidesStatus1
            {
                get
                {
                    return this._slidesStatus1;
                }
                set
                {
                    this._slidesStatus1 = value;
                    base.RaisePropertyChanged("slidesStatus1");
                }
            }


            public Basic.mCtrlStatus deviceStatus
            {
                get
                {
                    return this._deviceStatus;
                }
                set
                {
                    this._deviceStatus = value;
                    this._deviceStatusstr = Basic.GetDes(value);
                    base.RaisePropertyChanged("deviceStatusstr");
                    base.RaisePropertyChanged("deviceStatus");
                }
            }

            public string deviceStatusstr
            {
                get
                {
                    return this._deviceStatusstr;
                }
                set
                {
                    this._deviceStatusstr = value;
                    base.RaisePropertyChanged("deviceStatusstr");
                }
            }

            public Basic.mCameraStatus cameraStatus
            {
                get
                {
                    return this._cameraStatus;
                }
                set
                {
                    this._cameraStatus = value;
                    this._cameraStatusstr = Basic.GetDes(value);
                    base.RaisePropertyChanged("cameraStatusstr");
                    base.RaisePropertyChanged("cameraStatus");
                }
            }

            public string cameraStatusstr
            {
                get
                {
                    return this._cameraStatusstr;
                }
                set
                {
                    this._cameraStatusstr = value;
                    base.RaisePropertyChanged("cameraStatusstr");
                }
            }

            public Basic.mQRScanStatus QRScanStatus
            {
                get
                {
                    return this._QRScanStatus;
                }
                set
                {
                    this._QRScanStatus = value;
                    this._QRScanStatusstr = Basic.GetDes(value);
                    base.RaisePropertyChanged("QRScanStatusstr");
                    base.RaisePropertyChanged("QRScanStatus");
                }
            }

            public string QRScanStatusstr
            {
                get
                {
                    return this._QRScanStatusstr;
                }
                set
                {
                    this._QRScanStatusstr = value;
                    base.RaisePropertyChanged("QRScanStatusstr");
                }
            }

            public string motionStatus
            {
                get
                {
                    return this._motionStatus;
                }
                set
                {
                    this._motionStatus = value;
                    base.RaisePropertyChanged("motionStatus");
                }
            }

            public string failureInfo
            {
                get
                {
                    return this._failureInfo;
                }
                set
                {
                    this._failureInfo = value;
                    base.RaisePropertyChanged("failureInfo");
                }
            }

            private int _steps;

            private Basic.mStoreStatus _slideStoreStatus0;

            private string _slideStoreStatus0str;

            private Basic.mSlideStatus[] _slidesStatus0 = new Basic.mSlideStatus[35];

            private Basic.mStoreStatus _slideStoreStatus1;

            private string _slideStoreStatus1str;

            private Basic.mSlideStatus[] _slidesStatus1 = new Basic.mSlideStatus[35];

            private Basic.mCtrlStatus _deviceStatus;

            private string _deviceStatusstr;

            private Basic.mCameraStatus _cameraStatus;

            private string _cameraStatusstr;

            private Basic.mQRScanStatus _QRScanStatus;

            private string _QRScanStatusstr;

            private string _motionStatus;

            private string _failureInfo;
        }

        public enum enumSpermTypeBasic
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal = 1,
            [Description("异常")]
            abnormal = 0,
            [Description("亚临床")]
            subclinical = 2
        }
        public enum enumHeadType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal,
            [Description("梨形头")]
            pyriform = 32,
            [Description("锥形头")]
            tapered = 1,
            [Description("瘦头")]
            thin,
         
            [Description("圆头")]
            round = 16,
           
            [Description("小头")]
            mirco = 4,
           
            [Description("大头")]
            macro = 8,
            
            [Description("不定形头")]
            irregular = 64,
            [Description("矮")]
            _short = 128,
            [Description("高")]
            tall = 256,
            [Description("胖")]
            fat = 512
        }

        public enum enumAcroType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal = 32,
            // Token: 0x040005B9 RID: 1465
            [Description("小顶体")]
            small = 2,
            // Token: 0x040005BA RID: 1466
            [Description("超大顶体")]
            excessive = 4,
            // Token: 0x040005BB RID: 1467
            [Description("超小顶体")]
            extremly_little = 1,
            // Token: 0x040005BC RID: 1468
            [Description("不均匀")]
            texture_abnormal = 16,
            // Token: 0x040005BD RID: 1469
            [Description("多空泡")]
            vaculesGT2 = 8,
            // Token: 0x040005BE RID: 1470
            [Description("大空泡面积")]
            bigVacules = 64
        }

        // Token: 0x020000CA RID: 202
        public enum enumKernelType
        {
            // Token: 0x040005C0 RID: 1472
            [Description("全部")]
            all = -1,
            // Token: 0x040005C1 RID: 1473
            [Description("无")]
            no = 1,
            // Token: 0x040005C2 RID: 1474
            [Description("小")]
            small,
            // Token: 0x040005C3 RID: 1475
            [Description("大")]
            big = 4,
            // Token: 0x040005C4 RID: 1476
            [Description("正常")]
            normal = 32,
            // Token: 0x040005C5 RID: 1477
            [Description("不均匀")]
            texture_abnormal = 16,
            // Token: 0x040005C6 RID: 1478
            [Description("空泡")]
            vaculesGT0 = 8
        }

        // Token: 0x020000CB RID: 203
        public enum enumMDPTailType
        {
            // Token: 0x040005C8 RID: 1480
            [Description("全部")]
            all = -1,
            // Token: 0x040005C9 RID: 1481
            [Description("正常")]
            normal,
            // Token: 0x040005CA RID: 1482
            [Description("中段角度异常")]
            angle,
            // Token: 0x040005CB RID: 1483
            [Description("中段粗大")]
            big,
            // Token: 0x040005CC RID: 1484
            [Description("残留胞浆")]
            cytoso = 4
        }

        // Token: 0x020000CC RID: 204
        public enum enumTailType
        {
            // Token: 0x040005CE RID: 1486
            [Description("全部")]
            all = -1,
            // Token: 0x040005CF RID: 1487
            [Description("正常")]
            normal,
            // Token: 0x040005D0 RID: 1488
            [Description("无尾")]
            notail = 8,
            // Token: 0x040005D1 RID: 1489
            [Description("短尾")]
            shorttail = 16,
            // Token: 0x040005D2 RID: 1490
            [Description("卷尾")]
            curvetail = 32,
            // Token: 0x040005D3 RID: 1491
            [Description("折弯")]
            bendtail = 64
        }

        //public class EnumToBooleanConverter : IValueConverter
        //{
        //    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        //    {
        //        return value != null && value.Equals(parameter);
        //    }


        //    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        //    {
        //        return (value != null && value.Equals(true)) ? parameter : System.Windows.Data.Binding.DoNothing;
        //    }
        //}
    }
}
