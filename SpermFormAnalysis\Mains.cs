﻿using AIDFI.Form;
using SpermFormAnalysis.PageForm;
using Sunny.UI;
using System;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;

namespace SpermFormAnalysis
{
    public delegate void Handler();
    public partial class Mains : UIForm
    {
        public event Handler SearchFlushEvent;
        public Action<object, KeyEventArgs> KeyDownEvent;
        public Mains()
        {
            InitializeComponent();
            MorTests morTest = new MorTests();
            this.KeyPreview = true;
            this.KeyDownEvent += morTest.KeyDownEvent;
            MainContainer.AddPage(morTest);
            MainContainer.AddPage(new Searchs(this));
            MainContainer.AddPage(new Authorizes());
            MainContainer.AddPage(new Setting());
            MainContainer.AddPage(new UserManage());
            
            Aside.SetNodeSymbol(Aside.Nodes[0], 57601);
            Aside.SetNodeSymbol(Aside.Nodes[1], 85);
            Aside.SetNodeSymbol(Aside.Nodes[2], 57345);
            Aside.SetNodeSymbol(Aside.Nodes[3], 61573);
            Aside.SetNodeSymbol(Aside.Nodes[4], 61447);
            Aside.SelectFirst();

            uiToolTip1.SetToolTip(uiHeaderButton1, "返回主菜单");
        }
        int CurrPageIndex = 0;
        private void Main_KeyDown(object sender, KeyEventArgs e)
        {
            if (this.CurrPageIndex == 0)
            {
                KeyDownEvent(sender, e);
            }
        }
        /// <summary>
        /// 切换节点后，页面更改
        /// </summary>
        /// <param name="node"></param>
        /// <param name="item"></param>
        /// <param name="pageIndex"></param>
        private void Aside_MenuItemClick(TreeNode node, NavMenuItem item, int pageIndex)
        {
            switch (node.Text)
            {
                case "新建实验":
                    MainContainer.SelectedIndex = 0;
                    break;
                case "实验查询":
                    MainContainer.SelectedIndex = 1;
                    SearchFlushEvent();
                    break;
                case "授权管理":
                    MainContainer.SelectedIndex = 2;
                    break;
                case "参数配置":
                    MainContainer.SelectedIndex = 3;
                    break;
                case "用户管理":
                    MainContainer.SelectedIndex = 4;
                    break;
            }
            this.CurrPageIndex = node.Index;
        }
        /// <summary>
        /// 软件信息呈现
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton1_Click(object sender, EventArgs e)
        {
            About a = new About();
            a.ShowDialog();
        }
        /// <summary>
        /// 质控图呈现
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton2_Click(object sender, EventArgs e)
        {
            Quality q = new Quality();
            q.ShowDialog();
        }
        /// <summary>
        /// 线程关闭，防止占用无法重启软件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Mains_FormClosed(object sender, FormClosedEventArgs e)
        {
            Environment.Exit(0);
            Application.Exit();
        }

        private void Mains_KeyDown(object sender, KeyEventArgs e)
        {
            if (MainContainer.SelectedIndex==0)
            {
                KeyDownEvent(sender, e);
            }
        }
        /// <summary>
        /// 返回首页
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiHeaderButton1_Click(object sender, EventArgs e)
        {
            string parentDirectory = Path.GetDirectoryName(Application.StartupPath);
            string appPath = parentDirectory + "\\精子质量多维度分析管理系统.exe";
            ProcessStartInfo startInfo = new ProcessStartInfo
            {
                FileName = appPath,
                UseShellExecute = false, // 必须设置为false
                WorkingDirectory = System.IO.Path.GetDirectoryName(parentDirectory), // 设置新的程序的根目录
            };

            Process p = Process.Start(startInfo);
            this.Close();
        }
    }
}
