﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <PlatformTarget>x64</PlatformTarget>
    <Platforms>x64</Platforms>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118.0" />
    <PackageReference Include="System.Data.SQLite.EF6" Version="1.0.118.0" />
    <PackageReference Include="EntityFramework" Version="6.4.4" />
    <PackageReference Include="NPOI" Version="2.6.0-rc-3" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Emgu.CV.World">
      <HintPath>bin\x64\Debug\net8.0-windows\win-x64\Emgu.CV.World.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="TFRunModel">
      <HintPath>bin\x64\Debug\net8.0-windows\win-x64\TFRunModel.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="TFCommon">
      <HintPath>bin\x64\Debug\net8.0-windows\win-x64\TFCommon.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="TensorFlowSharp">
      <HintPath>bin\x64\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SpermSegment">
      <HintPath>bin\x64\Debug\net8.0-windows\win-x64\SpermSegment.dll</HintPath>
      <Private>True</Private>
    </Reference>

  </ItemGroup>

  <ItemGroup>
    <None Update="x64\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="tfmodel\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="database\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="*.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
