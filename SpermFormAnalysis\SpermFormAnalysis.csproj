﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9AE3E8A5-BC0B-4F5F-8D08-375BE123B997}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>SpermFormAnalysis</RootNamespace>
    <AssemblyName>SpermFormAnalysis</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>CellPro.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>..\packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="Emgu.CV.UI, Version=4.1.0.3420, Culture=neutral, PublicKeyToken=7281126722ab4438, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\Emgu.CV.UI.dll</HintPath>
    </Reference>
    <Reference Include="Emgu.CV.World, Version=4.1.0.3420, Culture=neutral, PublicKeyToken=7281126722ab4438, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\Emgu.CV.World.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Enums.NET, Version=*******, Culture=neutral, PublicKeyToken=7ea1c1650d506225, processorArchitecture=MSIL">
      <HintPath>..\packages\Enums.NET.4.0.0\lib\net45\Enums.NET.dll</HintPath>
    </Reference>
    <Reference Include="GxIAPINET, Version=1.0.7468.19767, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\GxIAPINET.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.3.3\lib\net45\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\log4net.dll</HintPath>
    </Reference>
    <Reference Include="MathNet.Numerics, Version=********, Culture=neutral, PublicKeyToken=cd8b63ad3d691a37, processorArchitecture=MSIL">
      <HintPath>..\packages\MathNet.Numerics.Signed.4.15.0\lib\net461\MathNet.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IO.RecyclableMemoryStream.2.2.0\lib\net462\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data, Version=********, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=2.6.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.6.0-rc-3\lib\net472\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=2.6.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.6.0-rc-3\lib\net472\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=2.6.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.6.0-rc-3\lib\net472\NPOI.OpenXml4Net.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=2.6.0.0, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.2.6.0-rc-3\lib\net472\NPOI.OpenXmlFormats.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="Prism, Version=7.2.0.1422, Culture=neutral, PublicKeyToken=40ee6c3a2184dc59, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\Prism.dll</HintPath>
    </Reference>
    <Reference Include="QtrfControl, Version=1.0.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\QtrfControl.dll</HintPath>
    </Reference>
    <Reference Include="SampleControl, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\SampleControl.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.0-beta18\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp, Version=2.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.ImageSharp.2.1.3\lib\net472\SixLabors.ImageSharp.dll</HintPath>
    </Reference>
    <Reference Include="SpermSegment, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\SpermSegment.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI, Version=3.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.3.0.3\lib\net40\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SunnyUI.Common.3.0.3\lib\net40\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Library, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\SunnyUI.Library.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.CodeDom, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.CodeDom.8.0.0\lib\net462\System.CodeDom.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SQLite, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.EF6, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SQLite.EF6.1.0.118.0\lib\net46\System.Data.SQLite.EF6.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.Linq, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SQLite.Linq.1.0.118.0\lib\net46\System.Data.SQLite.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Design" />
    <Reference Include="System.IO.Compression, Version=4.1.2.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream, Version=4.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.UnmanagedMemoryStream.4.3.0\lib\net46\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.5.0.0\lib\net45\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Text.Encoding.CodePages, Version=5.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encoding.CodePages.5.0.0\lib\net461\System.Text.Encoding.CodePages.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="TensorFlowSharp, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\TensorFlowSharp.dll</HintPath>
    </Reference>
    <Reference Include="TFCommon, Version=1.0.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\TFCommon.dll</HintPath>
    </Reference>
    <Reference Include="TFRunModel, Version=1.0.0.0, Culture=neutral, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\x64\Debug\TFRunModel.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Class\CardMag.cs" />
    <Compile Include="Class\clsUtility.cs" />
    <Compile Include="Class\common.cs" />
    <Compile Include="Class\CWin32Bitmaps.cs" />
    <Compile Include="Class\GlobalProperty.cs" />
    <Compile Include="Class\GxBitmap.cs" />
    <Compile Include="Class\IC64DLL.cs" />
    <Compile Include="Class\mifareone.cs" />
    <Compile Include="Class\ShowReport.cs" />
    <Compile Include="Class\SoftKeyYT88.cs" />
    <Compile Include="common\Drawing.cs" />
    <Compile Include="DataHelper\AdminServices.cs" />
    <Compile Include="DataHelper\MrfrulesServices.cs" />
    <Compile Include="DataHelper\SampleinfoServices.cs" />
    <Compile Include="DataHelper\SamplephotoServices.cs" />
    <Compile Include="DataHelper\SpermServices.cs" />
    <Compile Include="Data\DES_.cs" />
    <Compile Include="Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Login.Designer.cs">
      <DependentUpon>Login.cs</DependentUpon>
    </Compile>
    <Compile Include="Mains.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Mains.Designer.cs">
      <DependentUpon>Mains.cs</DependentUpon>
    </Compile>
    <Compile Include="Model\Admin.cs" />
    <Compile Include="Model\Mrfrules.cs" />
    <Compile Include="Model\Sampleinfo.cs" />
    <Compile Include="Model\Samplephoto.cs" />
    <Compile Include="Model\Sperm.cs" />
    <Compile Include="PageForm\About.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\About.Designer.cs">
      <DependentUpon>About.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\Authorizes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\Authorizes.Designer.cs">
      <DependentUpon>Authorizes.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\CellDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\CellDetail.Designer.cs">
      <DependentUpon>CellDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\CellDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\CellDetails.Designer.cs">
      <DependentUpon>CellDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\MorTests.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\MorTests.Designer.cs">
      <DependentUpon>MorTests.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\Quality.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\Quality.designer.cs">
      <DependentUpon>Quality.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\Sample.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\Sample.Designer.cs">
      <DependentUpon>Sample.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\Searchs.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\Searchs.Designer.cs">
      <DependentUpon>Searchs.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\AddUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\AddUser.Designer.cs">
      <DependentUpon>AddUser.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\Setting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\Setting.Designer.cs">
      <DependentUpon>Setting.cs</DependentUpon>
    </Compile>
    <Compile Include="PageForm\UserManage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PageForm\UserManage.Designer.cs">
      <DependentUpon>UserManage.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Segments\clsMrfSegment.cs" />
    <Compile Include="Segments\clsTailProcess.cs" />
    <Compile Include="Segments\MRFSegment.cs" />
    <Compile Include="Segments\TaskMgr.cs" />
    <Compile Include="TFmodel\common.cs" />
    <Compile Include="utils\Basic.cs" />
    <Compile Include="utils\Crypto.cs" />
    <Compile Include="utils\GraphicsUtils.cs" />
    <Compile Include="utils\IniFileUtils.cs" />
    <Compile Include="utils\LogHelper.cs" />
    <Compile Include="utils\PrivateImplementationDetails.cs" />
    <EmbeddedResource Include="Login.resx">
      <DependentUpon>Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Mains.resx">
      <DependentUpon>Mains.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\About.resx">
      <DependentUpon>About.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\Authorizes.resx">
      <DependentUpon>Authorizes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\CellDetail.resx">
      <DependentUpon>CellDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\CellDetails.resx">
      <DependentUpon>CellDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\MorTests.resx">
      <DependentUpon>MorTests.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\Quality.resx">
      <DependentUpon>Quality.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\Sample.resx">
      <DependentUpon>Sample.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\Searchs.resx">
      <DependentUpon>Searchs.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\AddUser.resx">
      <DependentUpon>AddUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\Setting.resx">
      <DependentUpon>Setting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageForm\UserManage.resx">
      <DependentUpon>UserManage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="CellPro.ico" />
    <Content Include="Resources\01.png" />
    <Content Include="Resources\02.png" />
    <Content Include="Resources\03.png" />
    <Content Include="Resources\11.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\12.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\13.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\14.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\15.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\21.png" />
    <Content Include="Resources\31.png" />
    <Content Include="Resources\32.png" />
    <Content Include="Resources\33.png" />
    <Content Include="Resources\34.png" />
    <Content Include="Resources\41.png" />
    <Content Include="Resources\42.png" />
    <Content Include="Resources\星博智造黑色2.png" />
    <Content Include="SunnyUI.Library.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\save.png" />
    <None Include="Resources\savexuanzhong.png" />
    <Content Include="Resources\星博智造.png" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Entity\" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.7.2">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.7.2 %28x86 和 x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用“NuGet 程序包还原”可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
</Project>