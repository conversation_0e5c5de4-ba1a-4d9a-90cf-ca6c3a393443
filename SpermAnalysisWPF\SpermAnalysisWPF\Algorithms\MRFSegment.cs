using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Linq;
using System.Threading;
using Emgu.CV;
using Emgu.CV.Structure;
using SpermAnalysisWPF.Model;
using SpermAnalysisWPF.DataHelper;
using SpermAnalysisWPF.Utils;

namespace SpermAnalysisWPF.Algorithms
{
    public static class MRFSegment
    {
        private static double _MIN_SCORE_FOR_OBJECT = 0.75;
        private static bool _useGpu = true;
        private static List<Mrfrules> mrf_validrules = new List<Mrfrules>();
        private static object locker = new object();

        public static double MIN_SCORE_FOR_OBJECT
        {
            get { return _MIN_SCORE_FOR_OBJECT; }
            set
            {
                _MIN_SCORE_FOR_OBJECT = value;
                TFRunModel.TFRunMrf.MIN_SCORE_FOR_OBJECT_HIGHLIGHTING = value;
            }
        }

        public static bool useGpu
        {
            get { return _useGpu; }
            set
            {
                _useGpu = value;
                // TFRunDfi.useGpu = value; // 如果需要的话
            }
        }

        // 执行分析
        public static List<Sperm> doSegment(string imagePath)
        {
            object obj = locker;
            bool lockTaken = false;
            List<Sperm> result;
            
            try
            {
                Monitor.Enter(obj, ref lockTaken);
                
                // 加载验证规则
                if (mrf_validrules.Count == 0)
                {
                    LoadValidationRules();
                }

                List<Sperm> sperms = new List<Sperm>();

                try
                {
                    // 设置检测阈值
                    TFRunModel.TFRunMrf.MIN_SCORE_FOR_OBJECT_HIGHLIGHTING = MIN_SCORE_FOR_OBJECT;

                    // 使用安全的TensorFlow模型运行方式
                    List<TFRunModel.common.dectResult> detectionResults = TensorFlowHelper.SafeRunModel(imagePath);

                    if (detectionResults == null || detectionResults.Count == 0)
                    {
                        LogHelper.WriteErrLog("TensorFlow模型未检测到任何目标");
                        return sperms;
                    }

                    // 加载图像
                    Image<Bgr, byte> imgMrfc = new Image<Bgr, byte>(imagePath);
                    Bgr average = imgMrfc.GetAverage();
                    Image<Gray, byte> imgMrf = SpermSegment.clsMrfSegment.AjustToLabGray(imgMrfc);
                    
                    // 计算阈值
                    double threshold = imgMrf.GetAverage().MCvScalar.V0 + SpermSegment.clsMrfSegment.outerT;
                    
                    // 处理每个检测到的精子
                    for (int i = 0; i < detectionResults.Count; i++)
                    {
                        var detectionResult = detectionResults[i];
                        var sperm = doSegmentTask(i, detectionResult, imgMrf, imgMrfc, threshold);
                        
                        if (sperm != null && sperm.vop_sperm != null && !string.IsNullOrEmpty(sperm.vop_sperm))
                        {
                            sperms.Add(sperm);
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"分析过程中出错: {ex.Message}", "错误");
                }

                result = sperms;
            }
            finally
            {
                if (lockTaken)
                {
                    Monitor.Exit(obj);
                }
            }

            return result;
        }

        private static Sperm doSegmentTask(int spermindex, TFRunModel.common.dectResult result, 
            Image<Gray, byte> imgMrf, Image<Bgr, byte> imgMrfc, double threshold)
        {
            Sperm sperm = new Sperm();
            
            try
            {
                // 基本信息
                sperm.spermindex = spermindex + 1;
                sperm.imageSource = result.imageSource;
                sperm.classid = result.classid;
                sperm.clsname = result.clsname;
                sperm.xmin = result.xmin;
                sperm.xmax = result.xmax;
                sperm.ymin = result.ymin;
                sperm.ymax = result.ymax;
                sperm.score = result.score;

                // 调用SpermSegment进行精细分割
                SpermSegment.clsBase.dectResult segmentResult = SpermSegment.clsMrfSegment.do_MrfSegment(
                    result, imgMrf, imgMrfc, threshold);

                if (segmentResult.spermArea >= 0.0)
                {
                    // 轮廓数据
                    sperm.vop_acrosome = ConvertPointArrayToString(segmentResult.vop_acrosome);
                    sperm.vop_kernel = ConvertPointArrayToString(segmentResult.vop_kernel);
                    sperm.vop_sperm = ConvertPointArrayToString(segmentResult.vop_sperm);
                    
                    // 面积和几何特征
                    sperm.acrosomeArea = segmentResult.acrosomeArea;
                    sperm.kernelArea = segmentResult.kernelArea;
                    sperm.spermArea = segmentResult.spermArea;
                    sperm.longAxis = segmentResult.longAxis;
                    sperm.shortAxis = segmentResult.shortAxis;
                    sperm.ellipsRatio = segmentResult.ellipsRatio;
                    sperm.acrosomeRatio = segmentResult.acrosomeRatio;

                    // 验证精子
                    sperm = validSperm(sperm);
                    
                    // 形态学分析
                    int acrosomeType = 0;
                    int kernelType = 0;
                    int middlePieceType = 0;
                    
                    if (sperm.valid == 1)
                    {
                        sperm.shapeType = recognizeSpermShapeAdvanced(segmentResult, out acrosomeType, out kernelType, out middlePieceType);
                    }
                    
                    sperm.acrosomeType = acrosomeType;
                    sperm.kernelType = kernelType;
                    sperm.middlePieceType = middlePieceType;

                    // 判断是否正常
                    bool isSubclinical = get_subclinical_num(sperm);
                    if (sperm.shapeType == 0 && sperm.acrosomeType == 32 && sperm.kernelType == 32 && sperm.middlePieceType == 0)
                    {
                        sperm.isNormal = 1;
                    }
                    else
                    {
                        sperm.isNormal = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不中断处理
                System.Diagnostics.Debug.WriteLine($"处理精子 {spermindex} 时出错: {ex.Message}");
            }

            return sperm;
        }

        private static string ConvertPointArrayToString(Point[] points)
        {
            if (points == null || points.Length == 0)
                return "";

            // 使用与原项目相同的格式：用空格分隔，每个点用逗号分隔坐标
            string result = "";
            foreach (Point point in points)
            {
                result += $"{point.X},{point.Y} ";
            }
            return result;
        }

        private static void LoadValidationRules()
        {
            try
            {
                var dataService = new SpermDataService();
                mrf_validrules = dataService.GetMrfRules();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载验证规则失败: {ex.Message}");
                mrf_validrules = new List<Mrfrules>();
            }
        }

        private static Sperm validSperm(Sperm sperm)
        {
            bool flag = true;
            sperm.valid = 1;

            foreach (var rule in mrf_validrules)
            {
                if (rule.Usefield == "prefilter") continue;
                
                double value = get_spermFieldValue(rule.Usefield, sperm);
                int validationResult = 0;

                if (value >= rule.Min_value && value <= rule.Max_value)
                {
                    validationResult = 1;
                }
                else if (value > rule.Max_value)
                {
                    validationResult = 2;
                }

                sperm = set_spermFieldValid(rule.Usefield, validationResult, sperm);
                flag = flag && (validationResult == 1);
            }

            sperm.valid = flag ? 1 : 0;
            return sperm;
        }

        private static double get_spermFieldValue(string field, Sperm sperm)
        {
            switch (field)
            {
                case "spermArea": return sperm.spermArea;
                case "longAxis": return sperm.longAxis;
                case "shortAxis": return sperm.shortAxis;
                case "ellipsRatio": return sperm.ellipsRatio;
                case "acrosomeRatio": return sperm.acrosomeRatio;
                case "kernelArea": return sperm.kernelArea;
                case "acrosomeArea": return sperm.acrosomeArea;
                default: return 0.0;
            }
        }

        private static Sperm set_spermFieldValid(string field, int validationResult, Sperm sperm)
        {
            // 根据字段设置验证结果
            // 这里可以根据需要扩展
            if (validationResult != 1)
            {
                sperm.valid = 0;
            }
            return sperm;
        }

        private static int recognizeSpermShapeAdvanced(SpermSegment.clsBase.dectResult segResult, 
            out int acrosomeType, out int kernelType, out int middlePieceType)
        {
            // 简化的形态学识别
            acrosomeType = 32; // 正常
            kernelType = 32;   // 正常
            middlePieceType = 0; // 正常
            
            // 基于规则的简单判断
            if (segResult.longAxis > 6.0 || segResult.shortAxis > 4.0)
            {
                return 1; // 异常形状
            }
            
            if (segResult.ellipsRatio > 2.0 || segResult.ellipsRatio < 1.2)
            {
                return 1; // 异常形状
            }
            
            return 0; // 正常形状
        }

        private static bool get_subclinical_num(Sperm sperm)
        {
            // 简化的亚临床判断
            return false;
        }
    }
}
