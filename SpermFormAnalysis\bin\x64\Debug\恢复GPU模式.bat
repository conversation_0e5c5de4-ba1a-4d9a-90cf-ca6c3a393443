@echo off
title 恢复GPU模式
color 0A

echo ========================================
echo           恢复GPU模式
echo ========================================
echo.

echo [1/2] 恢复CUDA库文件...
if exist "cudart64_110.dll.backup" (
    ren "cudart64_110.dll.backup" "cudart64_110.dll"
    echo     ✓ 已恢复 cudart64_110.dll
)

if exist "cublas64_11.dll.backup" (
    ren "cublas64_11.dll.backup" "cublas64_11.dll"
    echo     ✓ 已恢复 cublas64_11.dll
)

if exist "curand64_10.dll.backup" (
    ren "curand64_10.dll.backup" "curand64_10.dll"
    echo     ✓ 已恢复 curand64_10.dll
)

if exist "cudnn64_8.dll.backup" (
    ren "cudnn64_8.dll.backup" "cudnn64_8.dll"
    echo     ✓ 已恢复 cudnn64_8.dll
)

echo.
echo [2/2] 恢复GPU配置...
echo [GPU设置] > gpu_config.ini
echo ForceUseCPU=false >> gpu_config.ini
echo GPUMemoryOptimized=true >> gpu_config.ini
echo GPUMemoryLimit=1400 >> gpu_config.ini
echo EnableTailProcessing=true >> gpu_config.ini
echo AutoDetectGPU=true >> gpu_config.ini
echo     ✓ 已恢复GPU配置文件

echo.
echo ========================================
echo            恢复完成！
echo ========================================
echo.
echo 下次启动程序将尝试使用GPU模式
echo 如果仍有内存问题，请使用CPU模式启动器
echo.
pause
