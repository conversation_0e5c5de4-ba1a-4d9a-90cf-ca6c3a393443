﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;

namespace SpermFormAnalysis.Class
{
    class mifareone
    {
        public mifareone()
        {
            //
            // TODO: 在此处添加构造函数逻辑
            //
        }
        [DllImport("qtrf64.dll", EntryPoint = "rf_request", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_request(int icdev, int mode, out ushort tagtype);

        [DllImport("qtrf64.dll", EntryPoint = "rf_request_std", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_request_std(int icdev, int mode, out ushort tagtype);

        [DllImport("qtrf64.dll", EntryPoint = "rf_anticoll", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_anticoll(int icdev, int bcnt, out uint snr);

        [DllImport("qtrf64.dll", EntryPoint = "rf_select", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_select(int icdev, uint snr, out byte size);

        [DllImport("qtrf64.dll", EntryPoint = "rf_authentication", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_authentication(int icdev, int mode, int secnr);

        [DllImport("qtrf64.dll", EntryPoint = "rf_authentication_2", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_authentication_2(int icdev, int mode, int keynr, int blocknr);

        [DllImport("qtrf64.dll", EntryPoint = "rf_read", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_read(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        [DllImport("qtrf64.dll", EntryPoint = "rf_read_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]


        //说明：     返回设备当前状态
        public static extern short rf_read_hex(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);
        [DllImport("qtrf64.dll", EntryPoint = "rf_init", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：初始化通讯接口
        public static extern int rf_init(short port, int baud);
        [DllImport("qtrf64.dll", EntryPoint = "rf_write_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_write_hex(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        [DllImport("qtrf64.dll", EntryPoint = "rf_write", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_write(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);
        [DllImport("qtrf64.dll", EntryPoint = "rf_exit", SetLastError = true,
           CharSet = CharSet.Auto, ExactSpelling = false,
           CallingConvention = CallingConvention.StdCall)]
        //说明：    关闭通讯口
        public static extern short rf_exit(int icdev);
        [DllImport("qtrf64.dll", EntryPoint = "rf_halt", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_halt(int icdev);

        [DllImport("qtrf64.dll", EntryPoint = "rf_initval", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_initval(int icdev, int blocknr, uint val);

        [DllImport("qtrf64.dll", EntryPoint = "rf_readval", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_readval(int icdev, int blocknr, out uint val);

        [DllImport("qtrf64.dll", EntryPoint = "rf_increment", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_increment(int icdev, int blocknr, uint val);

        [DllImport("qtrf64.dll", EntryPoint = "rf_decrement", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_decrement(int icdev, int blocknr, uint val);

        [DllImport("qtrf64.dll", EntryPoint = "rf_restore", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_restore(int icdev, int blocknr);

        [DllImport("qtrf64.dll", EntryPoint = "rf_transfer", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_transfer(int icdev, int blocknr);

        [DllImport("qtrf64.dll", EntryPoint = "rf_reset", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_reset(int icdev, int msec);

        [DllImport("qtrf64.dll", EntryPoint = "a_hex", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态


        public static extern short a_hex([MarshalAs(UnmanagedType.LPArray)] byte[] asc, [MarshalAs(UnmanagedType.LPArray)] byte[] hex, int len);

        [DllImport("qtrf64.dll", EntryPoint = "rf_beep", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short rf_beep(int icdev, int msec);
        [DllImport("qtrf64.dll", EntryPoint = "hex_a", SetLastError = true,
             CharSet = CharSet.Auto, ExactSpelling = false,
             CallingConvention = CallingConvention.StdCall)]
        //说明：     返回设备当前状态
        public static extern short hex_a([MarshalAs(UnmanagedType.LPArray)] byte[] hex, [MarshalAs(UnmanagedType.LPArray)] byte[] asc, int len);


    }
    /****************************************************
* 模块名称：数据类型转化
* 模块说明：对数据进行转化使得普通数据能被卡片接受，
* 卡片里的数据最后也能被系统识别。
****************************************************/
    //class ByteArray
    //{
    //    /// <summary>
    //    /// 将字符串转化为字节数组
    //    /// </summary>
    //    /// <param name="data">源字符串</param>
    //    /// <param name="type">类型</param>
    //    /// <returns></returns>
    //    public static Byte[] StringToByteArray(String data, int type)
    //    {
    //        int temp;//临时整型变量
    //        Byte[] key1;//关键字1
    //        Byte[] key2;//关键字2

    //        if (type == 1)//第一类转换法
    //        {
    //            key1 = new Byte[32];//构造关键字1
    //            key2 = new Byte[16];//构造关键字2

    //            key1 = Encoding.UTF8.GetBytes(data);//将源字符串转化为字节数组并将其填充到关键字1中
    //            temp = mifareone.a_hex(ref key1[0], ref key2[0], 32);//调用dll进行转换
    //        }
    //        else//第二类转换法
    //        {
    //            key1 = new Byte[12];//构造关键字1
    //            key2 = new Byte[7];//构造关键字2

    //            key1 = Encoding.UTF8.GetBytes(data);//将源字符串转化为字节数组并将其填充到关键字1中
    //            temp = mifareone.a_hex(ref key1[0], ref key2[0], 12);//调用dll进行转换
    //        }

    //        if (temp != 0)//转换失败
    //        {
    //            MessageBox.Show("a_hex failed");
    //            return null;
    //        }

    //        return key2;//返回结果
    //    }

    //    public static String ByteArrayToString(Byte[] data)
    //    {
    //        int temp;//临时整型变量
    //        Byte[] key1 = new Byte[32];//构造关键字
    //        //StringBuilder sbKey1 = new StringBuilder();

    //        temp = mifareone.hex_a(ref data[0], ref key1[0], 16);//调用dll进行转换
    //        //temp = common.hex_a(ref data[0], sbKey1, 16);
    //        if (temp != 0)//转换失败
    //        {
    //            MessageBox.Show("hex_a failed");
    //        }

    //        return key1.ToString();//返回结果
    //    }
    //}
}
