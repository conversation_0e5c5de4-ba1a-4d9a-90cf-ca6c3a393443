﻿using Emgu.CV.Structure;
using Emgu.CV;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using TFRunModel;
using MySqlX.XDevAPI.Common;

namespace SpermFormAnalysis.Segments
{
    public static class MRFSegment
    {
        // Token: 0x170001A2 RID: 418
        // (get) Token: 0x0600057D RID: 1405 RVA: 0x0001F2C0 File Offset: 0x0001D4C0
        // (set) Token: 0x0600057E RID: 1406 RVA: 0x00004A12 File Offset: 0x00002C12
        public static double MIN_SCORE_FOR_OBJECT
        {
            get
            {
                return MRFSegment._MIN_SCORE_FOR_OBJECT;
            }
            set
            {
                MRFSegment._MIN_SCORE_FOR_OBJECT = value;
                TFRunMrf.MIN_SCORE_FOR_OBJECT_HIGHLIGHTING = value;
            }
        }

        // Token: 0x170001A3 RID: 419
        // (get) Token: 0x0600057F RID: 1407 RVA: 0x0001F2D8 File Offset: 0x0001D4D8
        // (set) Token: 0x06000580 RID: 1408 RVA: 0x00004A21 File Offset: 0x00002C21
        public static bool useGpu
        {
            get
            {
                return MRFSegment._useGpu;
            }
            set
            {
                MRFSegment._useGpu = value;
                TFRunDfi.useGpu = value;
            }
        }

        //执行分析
        public static List<Model.Sperm> doSegment(string f)
        {
            object obj = MRFSegment.locker;
            bool flag = false;
            List<Model.Sperm> sperms2;
            try
            {
                Monitor.Enter(obj, ref flag);
                bool flag2 = MRFSegment.mrf_validrules.Count == 0;
                if (flag2)
                {

                    MRFSegment.mrf_validrules = MrfrulesServices.GetObjects(); ;
                }
                List<Model.Sperm> sperms = new List<Model.Sperm>();

                try
                {
                    //LogHelper.WriteErrLog("开始分析:");
                    TFRunMrf.MIN_SCORE_FOR_OBJECT_HIGHLIGHTING = MRFSegment.MIN_SCORE_FOR_OBJECT;
                    List<TFRunModel.common.dectResult> list = TFRunMrf.RunModelOnce(f); 
                    //LogHelper.WriteErrLog("list:" + list.Count);
                    Image<Bgr, byte> imgMrfc = new Image<Bgr, byte>(f);
                    Bgr average = imgMrfc.GetAverage();
                    Image<Gray, byte> imgMrf = SpermSegment.clsMrfSegment.AjustToLabGray(imgMrfc);
                    //imgMrf.Save("D://22.jpg");
                    double thrd = imgMrf.GetAverage().MCvScalar.V0 + (double)clsMrfSegment.outerT;
                    int num = Basic.threadNum;
                    bool flag3 = num < 1;
                    if (flag3)
                    {
                        num = 1;
                    }
                    bool flag4 = num > 28;
                    if (flag4)
                    {
                        num = 28;
                    }
                    for (int i = 0; i < list.Count; i += num)
                    {
                        bool isExiting = TaskMgr.isExiting;
                        if (isExiting)
                        {
                            break;
                        }
                        bool flag5 = num == 1;
                        if (flag5)
                        {
                            TFRunModel.common.dectResult result2 = list[i];
                            //LogHelper.WriteErrLog("精子面积:"+ result2.spermArea);
                            var sp = MRFSegment.doSegmentTask(i, result2, imgMrf, imgMrfc, thrd);
                            if (sp.vop_sperm == null)
                            {

                            }
                            else
                            {
                                sperms.Add(sp);
                            }
                            
                        }
                        else
                        {
                            List<Task> list2 = new List<Task>();
                            for (int j = 0; j < num; j++)
                            {
                                bool flag6 = i + j < list.Count;
                                if (flag6)
                                {
                                    TFRunModel.common.dectResult result = list[i + j];
                                    int spindex = i + j;
                                    Task item = new Task(delegate ()
                                    {
                                        var sp2 = MRFSegment.doSegmentTask(spindex, result, imgMrf, imgMrfc, thrd);
                                        if (sp2.vop_sperm == null)
                                        {

                                        }
                                        else
                                        {
                                            sperms.Add(sp2);
                                        }

                                    });
                                    list2.Add(item);
                                }
                            }
                            try
                            {
                                foreach (Task task in list2)
                                {
                                    task.Start();
                                }
                                Task.WaitAll(list2.ToArray());
                            }
                            catch (AggregateException ex)
                            {
                                Console.WriteLine("\nThe following exceptions have been thrown by WaitAll(): (THIS WAS EXPECTED)");
                            }
                        }
                    }
                    imgMrfc.Dispose();
                    imgMrf.Dispose();
                    imgMrfc = null;
                    imgMrf = null;
                    GC.Collect();
                }
                catch (Exception ex)
                {
                    LogHelper.WriteErrLog("精子分析出错了："+ex.Message +ex.StackTrace);
                }
                sperms2 = sperms;
            }
            finally
            {
                if (flag)
                {
                    Monitor.Exit(obj);
                }
            }
            return sperms2;
        }

        // Token: 0x06000582 RID: 1410 RVA: 0x0001F5FC File Offset: 0x0001D7FC
        private static Model.Sperm doSegmentTask(int spermindex, TFRunModel.common.dectResult result, Image<Gray, byte> imgMrf, Image<Bgr, byte> imgMrfc, double t)
        {
            Model.Sperm sperm = new Model.Sperm();
            try
            {
                sperm.spermindex = spermindex;
                sperm.imageSource = result.imageSource;
                sperm.classid = result.classid;
                sperm.clsname = result.clsname;
                sperm.xmin = result.xmin;
                sperm.xmax = result.xmax;
                sperm.ymin = result.ymin;
                sperm.ymax = result.ymax;
                sperm.score = result.score;
                //LogHelper.WriteErrLog("————————————————————————");
                //LogHelper.WriteErrLog("精子编号" + "sperm.spermIndex:" + spermindex);
                //LogHelper.WriteErrLog("sperm.imageSource:" + result.imageSource);
                //LogHelper.WriteErrLog("sperm.clsname:" + result.clsname);
                //LogHelper.WriteErrLog("sperm.xmin:" + sperm.xmin);
                //LogHelper.WriteErrLog("sperm.xmax:" + result.xmax);
                //LogHelper.WriteErrLog("sperm.ymin:" + result.ymin);
                //LogHelper.WriteErrLog("sperm.ymax:" + sperm.ymax);
                //LogHelper.WriteErrLog("sperm.score:" + sperm.score);
                bool flag = sperm.clsname == "ERC";
                if (flag)
                {
                    sperm.middlePieceType += 4;
                    sperm.isNormal = 0;
                    //LogHelper.WriteErrLog("sperm.middlePieceType:" + sperm.middlePieceType);
                    //LogHelper.WriteErrLog("sperm.isNormal:" + sperm.isNormal);
                }
                bool flag2 = sperm.clsname == "Curl";
                if (flag2)
                {
                    sperm.middlePieceType += 32;
                    sperm.isNormal = 0;
                    //LogHelper.WriteErrLog("sperm.middlePieceType:" + sperm.middlePieceType);
                    //LogHelper.WriteErrLog("sperm.isNormal:" + sperm.isNormal);
                }
                SpermSegment.clsMrfSegment.bCrfAdjust = Basic.bCrfAdjust;
                SpermSegment.clsBase.dectResult dectResult = SpermSegment.clsMrfSegment.do_MrfSegment(result, imgMrf, imgMrfc, t);
                bool flag3 = dectResult.spermArea >= 0.0;
                if (flag3)
                {
                    sperm.vop_acrosome = dectResult.vop_acrosome;
                    sperm.vop_kernel = dectResult.vop_kernel;
                    sperm.vop_sperm = dectResult.vop_sperm;
                    sperm.acrosomeArea = dectResult.acrosomeArea;
                    sperm.kernelArea = dectResult.kernelArea;
                    sperm.spermArea = dectResult.spermArea;
                    sperm.spermGirth = dectResult.spermGirth;
                    sperm.longAxis = dectResult.longAxis;
                    sperm.shortAxis = dectResult.shortAxis;
                    sperm.center_x = (double)dectResult.center.X;
                    sperm.center_y = (double)dectResult.center.Y;
                    sperm.center = dectResult.center;
                    sperm.angle = dectResult.angle;
                    sperm.ellipsRatio = dectResult.ellipsRatio;
                    sperm.acrosomeRatio = dectResult.acrosomeRatio;
                    sperm.areaCV = dectResult.areaCV;
                    sperm.girthCV = dectResult.girthCV;
                    sperm.ellipseCV = dectResult.ellipseCV;
                    sperm.SymmetryRatio = dectResult.SymmetryRatio;
                    sperm.SymmetryRatioLongAxis = dectResult.SymmetryRatioLongAxis;

                    //LogHelper.WriteErrLog("sperm.vop_acrosome:" + sperm.vop_acrosome.ToArray().ToString());
                    //LogHelper.WriteErrLog("sperm.vop_kernel:" + sperm.vop_kernel.ToArray().ToString());
                    //LogHelper.WriteErrLog("sperm.vop_sperm:" + sperm.vop_sperm.ToArray().ToString());
                    //LogHelper.WriteErrLog("sperm.acrosomeArea:" + sperm.acrosomeArea);
                    //LogHelper.WriteErrLog("sperm.kernelArea:" + sperm.kernelArea);
                    //LogHelper.WriteErrLog("sperm.spermArea:" + sperm.spermArea);
                    //LogHelper.WriteErrLog("sperm.spermArea:" + sperm.spermArea);
                    //LogHelper.WriteErrLog("sperm.spermGirth:" + sperm.spermGirth);
                    //LogHelper.WriteErrLog("sperm.longAxis:" + sperm.longAxis);
                    //LogHelper.WriteErrLog("sperm.shortAxis:" + sperm.shortAxis);
                    //LogHelper.WriteErrLog("sperm.center_x:" + sperm.center_x);
                    //LogHelper.WriteErrLog("sperm.center_y:" + sperm.center_y);
                    //LogHelper.WriteErrLog("sperm.center:" + sperm.center);
                    //LogHelper.WriteErrLog("sperm.angle:" + sperm.angle);
                    //LogHelper.WriteErrLog("sperm.ellipsRatio:" + sperm.ellipsRatio);
                    //LogHelper.WriteErrLog("sperm.acrosomeRatio:" + sperm.acrosomeRatio);
                    //LogHelper.WriteErrLog("sperm.areaCV:" + sperm.areaCV);
                    //LogHelper.WriteErrLog("sperm.girthCV:" + sperm.girthCV);
                    //LogHelper.WriteErrLog("sperm.ellipseCV:" + sperm.ellipseCV);
                    //LogHelper.WriteErrLog("sperm.girthCV:" + sperm.girthCV);
                    //LogHelper.WriteErrLog("sperm.SymmetryRatio:" + sperm.SymmetryRatio);
                    //LogHelper.WriteErrLog("sperm.SymmetryRatioLongAxis:" + sperm.SymmetryRatioLongAxis);
                    sperm.valid = dectResult.valid;
                    bool flag4 = dectResult.kernelArea == 0.0;
                    if (flag4)
                    {
                        sperm.valid = false;
                    }
                    sperm.vacuolesNum = dectResult.vacuolesNum;
                    sperm.acrosomeVacuolesNum = dectResult.acrosomeVacuolesNum;
                    sperm.kernelVacuolesNum = dectResult.kernelVacuolesNum;
                    sperm.acrosomeVacuolesArea = dectResult.acrosomeVacuolesArea;
                    sperm.bigVaucolesNum = dectResult.bigVacuolesNum;
                    sperm.vvop_vacuoles = new List<Point[]>();
                    sperm.vvop_vacuoles = dectResult.vvop_vacuoles;
                    sperm.vop_middlepiece = dectResult.vop_middlepiece;
                    sperm.middlepieceAngle = dectResult.middlepieceAngle;
                    sperm.middlepieceWidth = dectResult.middlepieceWidth;
                    sperm.acrosome_uniformity = dectResult.acrosome_uniformity;
                    sperm.kernel_uniformity = dectResult.kernel_uniformity;
                    sperm.vacuolesValid = true;
                    sperm.middlePieceValid = true;

                    //LogHelper.WriteErrLog("sperm.vacuolesNum:" + sperm.vacuolesNum);
                    //LogHelper.WriteErrLog("sperm.acrosomeVacuolesNum:" + sperm.acrosomeVacuolesNum);
                    //LogHelper.WriteErrLog("sperm.kernelVacuolesNum:" + sperm.kernelVacuolesNum);
                    //LogHelper.WriteErrLog("sperm.acrosomeVacuolesArea:" + sperm.acrosomeVacuolesArea);
                    //LogHelper.WriteErrLog("sperm.bigVaucolesNum:" + sperm.bigVaucolesNum);
                    //LogHelper.WriteErrLog("sperm.vop_middlepiece:" + sperm.vop_middlepiece);
                    //LogHelper.WriteErrLog("sperm.middlepieceAngle:" + sperm.middlepieceAngle);
                    //LogHelper.WriteErrLog("sperm.middlepieceWidth:" + sperm.middlepieceWidth);
                    //LogHelper.WriteErrLog("sperm.acrosome_uniformity:" + sperm.acrosome_uniformity);
                    //LogHelper.WriteErrLog("sperm.kernel_uniformity:" + sperm.kernel_uniformity);

                    sperm = MRFSegment.validSperm(sperm);
                    int acrosomeType = 0;
                    int kernelType = 0;
                    int num = 0;
                    bool valid = sperm.valid;
                    if (valid)
                    {
                        sperm.shapeType = MRFSegment.recognizeSpermShapeAdvanced(dectResult, out acrosomeType, out kernelType, out num);
                        //LogHelper.WriteErrLog("sperm.shapeType:" + sperm.shapeType);
                    }
                    sperm.acrosomeType = acrosomeType;
                    sperm.kernelType = kernelType;
                    //LogHelper.WriteErrLog("sperm.acrosomeType:" + sperm.acrosomeType);
                    //LogHelper.WriteErrLog("sperm.kernelType:" + sperm.kernelType);
                    bool flag5 = MRFSegment.get_subclinical_num(sperm);
                    bool flag6 = sperm.shapeType == 0 && sperm.acrosomeType == 32 && sperm.kernelType == 32 && sperm.middlePieceType == 0;
                    if (flag6)
                    {
                        sperm.isNormal = 1;
                        //LogHelper.WriteErrLog("sperm.isNormal:" + sperm.isNormal);
                    }
                    else
                    {
                        bool flag7 = flag5;
                        if (flag7)
                        {
                            sperm.isNormal = 0;
                            //LogHelper.WriteErrLog("sperm.isNormal:" + sperm.isNormal);
                        }
                        else
                        {
                            sperm.isNormal = 0;
                            //LogHelper.WriteErrLog("sperm.isNormal:" + sperm.isNormal);
                        }
                    }
                }
            }
            catch
            {
            }
            return sperm;
        }

        // Token: 0x06000583 RID: 1411 RVA: 0x0001F9C0 File Offset: 0x0001DBC0
        public static int recognizeSpermShapeAdvanced(dectResult segResult, out int acrosomeType, out int kernelType, out int middlePieceType)
        {
            double num = 0.0;
            double num2 = 0.0;
            double num3 = 0.0;
            double num4 = 0.0;
            double num5 = 0.0;
            double num6 = 0.0;
            double num7 = 0.0;
            double num8 = 0.0;
            double num9 = 0.0;
            double num10 = 0.0;
            double num11 = 0.0;
            double num12 = 0.0;
            double num13 = 0.0;
            double num14 = 0.0;
            double num15 = 0.0;
            double num16 = 0.0;
            double num17 = 0.0;
            double num18 = 0.0;
            foreach (Model.Mrfrules mrf_rules in MRFSegment.mrf_validrules)
            {
                bool flag = mrf_rules.Usefield == "longAxis" && mrf_rules.Ruletype == "validation";
                if (flag)
                {
                    num = mrf_rules.Max_value;
                    num2 = mrf_rules.Min_value;
                }
                bool flag2 = mrf_rules.Usefield == "shortAxis" && mrf_rules.Ruletype == "validation";
                if (flag2)
                {
                    num3 = mrf_rules.Max_value;
                    num4 = mrf_rules.Min_value;
                }
                bool flag3 = mrf_rules.Usefield == "SymmetryRatioLongAxis" && mrf_rules.Ruletype == "validation";
                if (flag3)
                {
                    num5 = mrf_rules.Min_value;
                }
                bool flag4 = mrf_rules.Usefield == "SymmetryRatio" && mrf_rules.Ruletype == "validation";
                if (flag4)
                {
                    num6 = mrf_rules.Min_value;
                }
                bool flag5 = mrf_rules.Usefield == "acrosomeRatio" && mrf_rules.Ruletype == "validation";
                if (flag5)
                {
                    num7 = mrf_rules.Max_value;
                    num8 = mrf_rules.Min_value;
                }
                bool flag6 = mrf_rules.Usefield == "ellipsRatio" && mrf_rules.Ruletype == "validation";
                if (flag6)
                {
                    num9 = mrf_rules.Max_value;
                    num10 = mrf_rules.Min_value;
                }
                bool flag7 = mrf_rules.Usefield == "girthCV" && mrf_rules.Ruletype == "validation";
                if (flag7)
                {
                    num11 = mrf_rules.Max_value;
                    num12 = mrf_rules.Min_value;
                }
                bool flag8 = mrf_rules.Usefield == "ellipseCV" && mrf_rules.Ruletype == "validation";
                if (flag8)
                {
                    double maxvalue = mrf_rules.Max_value;
                    double minvalue = mrf_rules.Min_value;
                }
                bool flag9 = mrf_rules.Usefield == "acrosome_uniformity" && mrf_rules.Ruletype == "validation";
                if (flag9)
                {
                    num14 = mrf_rules.Max_value;
                    num13 = mrf_rules.Min_value;
                }
                bool flag10 = mrf_rules.Usefield == "middlepieceAngle" && mrf_rules.Ruletype == "validation";
                if (flag10)
                {
                    num17 = mrf_rules.Max_value;
                    double minvalue2 = mrf_rules.Min_value;
                }
                bool flag11 = mrf_rules.Usefield == "middlepieceWidth" && mrf_rules.Ruletype == "validation";
                if (flag11)
                {
                    num18 = mrf_rules.Max_value;
                    double minvalue3 = mrf_rules.Min_value;
                }
            }
            //判断必须需要的参数如果给0就给默认值。
            bool flag12 = num5 == 0.0;
            if (flag12)
            {
                num5 = 0.85;
            }
            bool flag13 = num6 == 0.0;
            if (flag13)
            {
                num6 = 0.85;
            }
            bool flag14 = num10 == 0.0;
            if (flag14)
            {
                num10 = 1.3;
            }
            bool flag15 = num9 == 0.0;
            if (flag15)
            {
                num9 = 1.8;
            }
            bool flag16 = num11 == 0.0;
            if (flag16)
            {
                num11 = 1.04;
            }
            bool flag17 = num12 == 0.0;
            if (flag17)
            {
                num12 = 0.96;
            }
            //分析判断出结果
            int num19 = 0;
            bool flag18 = segResult.longAxis < num2 && segResult.shortAxis < num4 && (num19 & 4) <= 0;
            if (flag18)
            {
                num19 += 4;
            }
            bool flag19 = segResult.longAxis > num && segResult.shortAxis > num3 && (num19 & 8) <= 0;
            if (flag19)
            {
                num19 += 8;
            }
            bool flag20 = segResult.defectType > 0 && segResult.defectType <= 2;
            if (flag20)
            {
                bool flag21 = segResult.defectType == 2;
                if (flag21)
                {
                    num19 += 32;
                }
                else
                {
                    bool flag22 = segResult.SymmetryRatio > num6;
                    if (flag22)
                    {
                        bool flag23 = segResult.ellipsRatio > num9 && (num19 & 1) <= 0;
                        if (flag23)
                        {
                            num19++;
                        }
                        else
                        {
                            bool flag24 = segResult.ellipsRatio < num10 && (num19 & 16) <= 0;
                            if (flag24)
                            {
                                num19 += 16;
                            }
                            else
                            {
                                num19 += 32;
                            }
                        }
                    }
                    else
                    {
                        bool flag25 = segResult.SymmetryRatio < segResult.SymmetryRatioLongAxis && (num19 & 32) <= 0;
                        if (flag25)
                        {
                            bool flag26 = segResult.ellipsRatio < num10 && (num19 & 16) <= 0;
                            if (flag26)
                            {
                                num19 += 16;
                            }
                            else
                            {
                                num19 += 32;
                            }
                        }
                        else
                        {
                            bool flag27 = segResult.ellipsRatio > num9 && segResult.SymmetryRatioLongAxis > num5 && (num19 & 1) <= 0;
                            if (flag27)
                            {
                                num19++;
                            }
                            else
                            {
                                num19 += 64;
                            }
                        }
                    }
                }
            }
            else
            {
                bool flag28 = segResult.defectType == 3;
                if (flag28)
                {
                    num19 += 1024;
                }
                bool flag29 = segResult.SymmetryRatio < num6 || segResult.SymmetryRatioLongAxis < num5;
                if (flag29)
                {
                    bool flag30 = segResult.ellipsRatio > num9 && (num19 & 1) <= 0;
                    if (flag30)
                    {
                        num19++;
                    }
                    else
                    {
                        bool flag31 = segResult.ellipsRatio < num10 && segResult.SymmetryRatioLongAxis >= num5 && (num19 & 16) <= 0;
                        if (flag31)
                        {
                            num19 += 16;
                        }
                        else
                        {
                            bool flag32 = segResult.SymmetryRatio < num6 && segResult.SymmetryRatioLongAxis >= segResult.SymmetryRatio && segResult.acrosomeArea > 0.0 && segResult.areaRatio > -0.05 && (num19 & 32) <= 0;
                            if (flag32)
                            {
                                num19 += 32;
                            }
                            else
                            {
                                bool flag33 = (num19 & 64) <= 0;
                                if (flag33)
                                {
                                    num19 += 64;
                                }
                            }
                        }
                    }
                }
                else
                {
                    bool flag34 = segResult.ellipsRatio >= num10 && segResult.ellipsRatio <= num9 && (segResult.longAxis >= num2 && segResult.longAxis <= num && segResult.shortAxis >= num4) && segResult.shortAxis <= num3;
                    if (flag34)
                    {
                        num19 = num19;
                    }
                    else
                    {
                        bool flag35 = segResult.ellipsRatio > num9 && (num19 & 1) <= 0;
                        if (flag35)
                        {
                            num19++;
                        }
                        else
                        {
                            bool flag36 = segResult.ellipsRatio < num10;
                            if (flag36)
                            {
                                bool flag37 = (num19 & 16) <= 0 && segResult.ellipsRatio < num10 && segResult.SymmetryRatio >= num6 && segResult.SymmetryRatioLongAxis >= num5;
                                if (flag37)
                                {
                                    num19 += 16;
                                }
                                else
                                {
                                    bool flag38 = (num19 & 128) <= 0;
                                    if (flag38)
                                    {
                                        num19 += 128;
                                    }
                                }
                            }
                            else
                            {
                                bool flag39 = segResult.longAxis >= num2 && segResult.longAxis <= num && segResult.shortAxis < num4 && segResult.ellipsRatio >= num10 && (num19 & 2) <= 0;
                                if (flag39)
                                {
                                    num19 += 2;
                                }
                                else
                                {
                                    bool flag40 = segResult.longAxis >= num2 && segResult.longAxis <= num && segResult.shortAxis > num3 && segResult.ellipsRatio >= num10 && (num19 & 512) <= 0;
                                    if (flag40)
                                    {
                                        num19 += 512;
                                    }
                                    else
                                    {
                                        bool flag41 = segResult.longAxis > num && segResult.shortAxis <= num3 && (num19 & 256) <= 0;
                                        if (flag41)
                                        {
                                            num19 += 256;
                                        }
                                        else
                                        {
                                            bool flag42 = segResult.longAxis < num2 && segResult.shortAxis >= num4 && segResult.shortAxis <= num3 && (num19 & 128) <= 0;
                                            if (flag42)
                                            {
                                                num19 += 128;
                                            }
                                            else
                                            {
                                                bool flag43 = (num19 & 64) <= 0 && (num19 & 4) <= 0 && (num19 & 8) <= 0;
                                                if (flag43)
                                                {
                                                    num19 += 64;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            bool flag44 = num8 == 0.0;
            if (flag44)
            {
                num8 = 0.4;
            }
            bool flag45 = num7 == 0.0;
            if (flag45)
            {
                num7 = 0.7;
            }
            bool flag46 = num13 == 0.0;
            if (flag46)
            {
                num13 = 0.85;
            }
            bool flag47 = num14 == 0.0;
            if (flag47)
            {
                num14 = 1.0;
            }
            acrosomeType = 0;
            bool flag48 = segResult.acrosomeRatio == 0.0;
            if (flag48)
            {
                acrosomeType = 1;
            }
            else
            {
                bool flag49 = segResult.acrosomeRatio >= num8 && segResult.acrosomeRatio <= num7;
                if (flag49)
                {
                    acrosomeType = 32;
                }
                else
                {
                    bool flag50 = segResult.acrosomeRatio < num8;
                    if (flag50)
                    {
                        acrosomeType = 2;
                    }
                    else
                    {
                        acrosomeType = 4;
                    }
                }
            }
            bool flag51 = segResult.acrosomeVacuolesNum > 2;
            if (flag51)
            {
                acrosomeType += 8;
            }
            bool flag52 = segResult.bigVacuolesNum > 0;
            if (flag52)
            {
                acrosomeType += 64;
            }
            bool flag53 = segResult.acrosome_uniformity < num13;
            if (flag53)
            {
                acrosomeType += 16;
            }
            bool flag54 = segResult.acrosome_uniformity < 0.0;
            if (flag54)
            {
                acrosomeType = 1;
            }
            double num20 = 1.0 - num8;
            double num21 = 1.0 - num7;
            bool flag55 = num15 == 0.0;
            if (flag55)
            {
                num15 = 0.85;
            }
            bool flag56 = num16 == 0.0;
            if (flag56)
            {
                num16 = 1.0;
            }
            kernelType = 0;
            double num22 = 1.0 - segResult.acrosomeRatio;
            bool flag57 = num22 == 0.0;
            if (flag57)
            {
                kernelType = 1;
            }
            else
            {
                bool flag58 = num22 >= num21 && num22 <= num20;
                if (flag58)
                {
                    kernelType = 32;
                }
                else
                {
                    bool flag59 = num22 < num21;
                    if (flag59)
                    {
                        kernelType = 2;
                    }
                    else
                    {
                        kernelType = 4;
                    }
                }
            }
            bool flag60 = segResult.kernelVacuolesNum > 0;
            if (flag60)
            {
                kernelType += 8;
            }
            bool flag61 = segResult.kernel_uniformity < num15;
            if (flag61)
            {
                kernelType += 16;
            }
            bool flag62 = segResult.kernel_uniformity < 0.0;
            if (flag62)
            {
                kernelType = 1;
            }
            bool flag63 = num18 == 0.0;
            if (flag63)
            {
                num18 = 1.0;
            }
            bool flag64 = num17 == 0.0;
            if (flag64)
            {
                num17 = 30.0;
            }
            middlePieceType = 0;
            bool flag65 = segResult.middlepieceWidth == -1.0;
            if (flag65)
            {
                middlePieceType += 4;
            }
            bool flag66 = segResult.middlepieceWidth >= num18;
            if (flag66)
            {
                middlePieceType += 2;
            }
            bool flag67 = segResult.middlepieceAngle >= num17;
            if (flag67)
            {
                middlePieceType++;
            }
            return num19;
        }


        public struct dectResult
        {
            // Token: 0x04000003 RID: 3
            public string imageSource;

            // Token: 0x04000004 RID: 4
            public float ymin;

            // Token: 0x04000005 RID: 5
            public float xmin;

            // Token: 0x04000006 RID: 6
            public float ymax;

            // Token: 0x04000007 RID: 7
            public float xmax;

            // Token: 0x04000008 RID: 8
            public float score;

            // Token: 0x04000009 RID: 9
            public int classid;

            // Token: 0x0400000A RID: 10
            public string clsname;

            // Token: 0x0400000B RID: 11
            public int isNormal;

            // Token: 0x0400000C RID: 12
            public Point[] vop_acrosome;

            // Token: 0x0400000D RID: 13
            public Point[] vop_kernel;

            // Token: 0x0400000E RID: 14
            public Point[] vop_sperm;

            // Token: 0x0400000F RID: 15
            public Point[] vop_middlepiece;

            // Token: 0x04000010 RID: 16
            public List<Point[]> vvop_vacuoles;

            // Token: 0x04000011 RID: 17
            public double acrosomeArea;

            // Token: 0x04000012 RID: 18
            public double kernelArea;

            // Token: 0x04000013 RID: 19
            public double spermArea;

            // Token: 0x04000014 RID: 20
            public double spermGirth;

            // Token: 0x04000015 RID: 21
            public double longAxis;

            // Token: 0x04000016 RID: 22
            public double shortAxis;

            // Token: 0x04000017 RID: 23
            public PointF center;

            // Token: 0x04000018 RID: 24
            public double angle;

            // Token: 0x04000019 RID: 25
            public double ellipsRatio;

            // Token: 0x0400001A RID: 26
            public double acrosomeRatio;

            // Token: 0x0400001B RID: 27
            public double areaCV;

            // Token: 0x0400001C RID: 28
            public double girthCV;

            // Token: 0x0400001D RID: 29
            public double ellipseCV;

            // Token: 0x0400001E RID: 30
            public double SymmetryRatio;

            // Token: 0x0400001F RID: 31
            public double SymmetryRatioLongAxis;

            // Token: 0x04000020 RID: 32
            public double SymmetryRatioLongAxisAdvanced;

            // Token: 0x04000021 RID: 33
            public double areaRatio;

            // Token: 0x04000022 RID: 34
            public double acrosome_uniformity;

            // Token: 0x04000023 RID: 35
            public double kernel_uniformity;

            // Token: 0x04000024 RID: 36
            public bool valid;

            // Token: 0x04000025 RID: 37
            public int vacuolesNum;

            // Token: 0x04000026 RID: 38
            public int bigVacuolesNum;

            // Token: 0x04000027 RID: 39
            public int acrosomeVacuolesNum;

            // Token: 0x04000028 RID: 40
            public int kernelVacuolesNum;

            // Token: 0x04000029 RID: 41
            public double acrosomeVacuolesArea;

            // Token: 0x0400002A RID: 42
            public double middlepieceAngle;

            // Token: 0x0400002B RID: 43
            public double middlepieceWidth;

            // Token: 0x0400002C RID: 44
            public double ellipseCV1;

            // Token: 0x0400002D RID: 45
            public double ellipseCV1Range;

            // Token: 0x0400002E RID: 46
            public int defectType;

            // Token: 0x0400002F RID: 47
            public double DFIValue;

            // Token: 0x04000030 RID: 48
            public RotatedRect innerRect;

            // Token: 0x04000031 RID: 49
            public RotatedRect outerRect;

            // Token: 0x04000032 RID: 50
            public double R;

            // Token: 0x04000033 RID: 51
            public double G;

            // Token: 0x04000034 RID: 52
            public double B;

            // Token: 0x04000035 RID: 53
            public double RBRatio;
        }

        // Token: 0x06000584 RID: 1412 RVA: 0x000205D4 File Offset: 0x0001E7D4
        public static int recognizeSpermShapeAdvanced(SpermSegment.clsBase.dectResult segResult, out int acrosomeType, out int kernelType, out int middlePieceType)
        {
            double num = 0.0;
            double num2 = 0.0;
            double num3 = 0.0;
            double num4 = 0.0;
            double num5 = 0.0;
            double num6 = 0.0;
            double num7 = 0.0;
            double num8 = 0.0;
            double num9 = 0.0;
            double num10 = 0.0;
            double num11 = 0.0;
            double num12 = 0.0;
            double num13 = 0.0;
            double num14 = 0.0;
            double num15 = 0.0;
            double num16 = 0.0;
            double num17 = 0.0;
            double num18 = 0.0;
            foreach (Model.Mrfrules mrf_rules in MRFSegment.mrf_validrules)
            {
                bool flag = mrf_rules.Usefield == "longAxis" && mrf_rules.Ruletype == "validation";
                if (flag)
                {
                    num = mrf_rules.Max_value;
                    num2 = mrf_rules.Min_value;
                }
                bool flag2 = mrf_rules.Usefield == "shortAxis" && mrf_rules.Ruletype == "validation";
                if (flag2)
                {
                    num3 = mrf_rules.Max_value;
                    num4 = mrf_rules.Min_value;
                }
                bool flag3 = mrf_rules.Usefield == "SymmetryRatioLongAxis" && mrf_rules.Ruletype == "validation";
                if (flag3)
                {
                    num5 = mrf_rules.Min_value;
                }
                bool flag4 = mrf_rules.Usefield == "SymmetryRatio" && mrf_rules.Ruletype == "validation";
                if (flag4)
                {
                    num6 = mrf_rules.Min_value;
                }
                bool flag5 = mrf_rules.Usefield == "acrosomeRatio" && mrf_rules.Ruletype == "validation";
                if (flag5)
                {
                    num7 = mrf_rules.Max_value;
                    num8 = mrf_rules.Min_value;
                }
                bool flag6 = mrf_rules.Usefield == "ellipsRatio" && mrf_rules.Ruletype == "validation";
                if (flag6)
                {
                    num9 = mrf_rules.Max_value;
                    num10 = mrf_rules.Min_value;
                }
                bool flag7 = mrf_rules.Usefield == "girthCV" && mrf_rules.Ruletype == "validation";
                if (flag7)
                {
                    num11 = mrf_rules.Max_value;
                    num12 = mrf_rules.Min_value;
                }
                bool flag8 = mrf_rules.Usefield == "ellipseCV" && mrf_rules.Ruletype == "validation";
                if (flag8)
                {
                    double maxvalue = mrf_rules.Max_value;
                    double minvalue = mrf_rules.Min_value;
                }
                bool flag9 = mrf_rules.Usefield == "acrosome_uniformity" && mrf_rules.Ruletype == "validation";
                if (flag9)
                {
                    num14 = mrf_rules.Max_value;
                    num13 = mrf_rules.Min_value;
                }
                bool flag10 = mrf_rules.Usefield == "middlepieceAngle" && mrf_rules.Ruletype == "validation";
                if (flag10)
                {
                    num17 = mrf_rules.Max_value;
                    double minvalue2 = mrf_rules.Min_value;
                }
                bool flag11 = mrf_rules.Usefield == "middlepieceWidth" && mrf_rules.Ruletype == "validation";
                if (flag11)
                {
                    num18 = mrf_rules.Max_value;
                    double minvalue3 = mrf_rules.Min_value;
                }
            }
            bool flag12 = num5 == 0.0;
            if (flag12)
            {
                num5 = 0.85;
            }
            bool flag13 = num6 == 0.0;
            if (flag13)
            {
                num6 = 0.85;
            }
            bool flag14 = num10 == 0.0;
            if (flag14)
            {
                num10 = 1.3;
            }
            bool flag15 = num9 == 0.0;
            if (flag15)
            {
                num9 = 1.8;
            }
            bool flag16 = num11 == 0.0;
            if (flag16)
            {
                num11 = 1.04;
            }
            bool flag17 = num12 == 0.0;
            if (flag17)
            {
                num12 = 0.96;
            }
            int num19 = 0;
            bool flag18 = segResult.longAxis < num2 && segResult.shortAxis < num4 && (num19 & 4) <= 0;
            if (flag18)
            {
                num19 += 4;
            }
            bool flag19 = segResult.longAxis > num && segResult.shortAxis > num3 && (num19 & 8) <= 0;
            if (flag19)
            {
                num19 += 8;
            }
            bool flag20 = segResult.defectType > 0 && segResult.defectType <= 2;
            if (flag20)
            {
                bool flag21 = segResult.defectType == 2;
                if (flag21)
                {
                    num19 += 32;
                }
                else
                {
                    bool flag22 = segResult.SymmetryRatio > num6;
                    if (flag22)
                    {
                        bool flag23 = segResult.ellipsRatio > num9 && (num19 & 1) <= 0;
                        if (flag23)
                        {
                            num19++;
                            bool bHuaXi = Basic.bHuaXi;
                            if (bHuaXi)
                            {
                                bool flag24 = segResult.shortAxis >= num4 && (num19 & 4096) <= 0;
                                if (flag24)
                                {
                                    num19 += 4096;
                                }
                            }
                        }
                        else
                        {
                            bool flag25 = segResult.ellipsRatio < num10 && (num19 & 16) <= 0;
                            if (flag25)
                            {
                                num19 += 16;
                                bool bHuaXi2 = Basic.bHuaXi;
                                if (bHuaXi2)
                                {
                                    bool flag26 = segResult.ellipsRatio > 1.17 && (num19 & 4096) <= 0;
                                    if (flag26)
                                    {
                                        num19 += 4096;
                                    }
                                }
                            }
                            else
                            {
                                bool flag27 = (num19 & 1024) <= 0;
                                if (flag27)
                                {
                                    num19 += 1024;
                                }
                                bool flag28 = (num19 & 4096) <= 0;
                                if (flag28)
                                {
                                    num19 += 4096;
                                }
                            }
                        }
                    }
                    else
                    {
                        bool flag29 = segResult.SymmetryRatio < segResult.SymmetryRatioLongAxis;
                        if (flag29)
                        {
                            bool flag30 = segResult.ellipsRatio < num10 && (num19 & 16) <= 0;
                            if (flag30)
                            {
                                num19 += 16;
                            }
                            else
                            {
                                bool flag31 = (num19 & 32) <= 0;
                                if (flag31)
                                {
                                    num19 += 32;
                                }
                            }
                        }
                        else
                        {
                            bool flag32 = segResult.ellipsRatio > num9 && segResult.SymmetryRatioLongAxis > num5 && (num19 & 1) <= 0;
                            if (flag32)
                            {
                                num19++;
                            }
                            else
                            {
                                bool flag33 = (num19 & 64) <= 0;
                                if (flag33)
                                {
                                    num19 += 64;
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                bool flag34 = segResult.SymmetryRatio < num6 || segResult.SymmetryRatioLongAxis < num5;
                if (flag34)
                {
                    bool flag35 = segResult.ellipsRatio > num9;
                    if (flag35)
                    {
                        bool flag36 = segResult.SymmetryRatio < num6 && (num19 & 32) <= 0;
                        if (flag36)
                        {
                            num19 += 32;
                            bool bHuaXi3 = Basic.bHuaXi;
                            if (bHuaXi3)
                            {
                                bool flag37 = (num19 & 4096) <= 0;
                                if (flag37)
                                {
                                    num19 += 4096;
                                }
                            }
                        }
                        else
                        {
                            bool flag38 = (num19 & 1) <= 0;
                            if (flag38)
                            {
                                num19++;
                                bool bHuaXi4 = Basic.bHuaXi;
                                if (bHuaXi4)
                                {
                                    bool flag39 = segResult.shortAxis >= num4 && (num19 & 4096) <= 0;
                                    if (flag39)
                                    {
                                        num19 += 4096;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        bool flag40 = segResult.ellipsRatio < num10 && segResult.SymmetryRatioLongAxis >= num5 && (num19 & 16) <= 0;
                        if (flag40)
                        {
                            num19 += 16;
                            bool bHuaXi5 = Basic.bHuaXi;
                            if (bHuaXi5)
                            {
                                bool flag41 = segResult.ellipsRatio > 1.17 && (num19 & 4096) <= 0;
                                if (flag41)
                                {
                                    num19 += 4096;
                                }
                            }
                        }
                        else
                        {
                            bool flag42 = segResult.SymmetryRatio < num6 && segResult.SymmetryRatioLongAxis >= segResult.SymmetryRatio && segResult.acrosomeArea > 0.0 && segResult.areaRatio > -0.05 && (num19 & 32) <= 0;
                            if (flag42)
                            {
                                num19 += 32;
                                bool bHuaXi6 = Basic.bHuaXi;
                                if (bHuaXi6)
                                {
                                    bool flag43 = (num19 & 4096) <= 0;
                                    if (flag43)
                                    {
                                        num19 += 4096;
                                    }
                                }
                            }
                            else
                            {
                                bool flag44 = (num19 & 64) <= 0;
                                if (flag44)
                                {
                                    num19 += 64;
                                }
                            }
                        }
                    }
                }
                else
                {
                    bool flag45 = segResult.ellipsRatio >= num10 && segResult.ellipsRatio <= num9 && (segResult.longAxis >= num2 && segResult.longAxis <= num && segResult.shortAxis >= num4) && segResult.shortAxis <= num3;
                    if (flag45)
                    {
                        num19 = num19;
                    }
                    else
                    {
                        bool flag46 = segResult.ellipsRatio > num9 && (num19 & 1) <= 0;
                        if (flag46)
                        {
                            num19++;
                            bool bHuaXi7 = Basic.bHuaXi;
                            if (bHuaXi7)
                            {
                                bool flag47 = segResult.shortAxis >= num4 && (num19 & 4096) <= 0;
                                if (flag47)
                                {
                                    num19 += 4096;
                                }
                            }
                        }
                        else
                        {
                            bool flag48 = segResult.ellipsRatio < num10;
                            if (flag48)
                            {
                                bool flag49 = (num19 & 16) <= 0 && segResult.ellipsRatio < num10 && segResult.SymmetryRatio >= num6 && segResult.SymmetryRatioLongAxis >= num5;
                                if (flag49)
                                {
                                    num19 += 16;
                                    bool bHuaXi8 = Basic.bHuaXi;
                                    if (bHuaXi8)
                                    {
                                        bool flag50 = segResult.ellipsRatio > 1.17 && (num19 & 4096) <= 0;
                                        if (flag50)
                                        {
                                            num19 += 4096;
                                        }
                                    }
                                }
                                else
                                {
                                    bool flag51 = (num19 & 64) <= 0;
                                    if (flag51)
                                    {
                                        num19 += 64;
                                    }
                                }
                            }
                            else
                            {
                                bool flag52 = segResult.longAxis >= num2 && segResult.longAxis <= num && segResult.shortAxis < num4 && segResult.ellipsRatio >= num10;
                                if (flag52)
                                {
                                    bool flag53 = (num19 & 2) <= 0;
                                    if (flag53)
                                    {
                                        num19 += 2;
                                    }
                                    bool flag54 = (num19 & 4096) <= 0;
                                    if (flag54)
                                    {
                                        num19 += 4096;
                                    }
                                }
                                else
                                {
                                    bool flag55 = segResult.longAxis >= num2 && segResult.longAxis <= num && segResult.shortAxis > num3 && segResult.ellipsRatio >= num10;
                                    if (flag55)
                                    {
                                        bool flag56 = (num19 & 512) <= 0;
                                        if (flag56)
                                        {
                                            num19 += 512;
                                        }
                                        bool flag57 = (num19 & 4096) <= 0;
                                        if (flag57)
                                        {
                                            num19 += 4096;
                                        }
                                    }
                                    else
                                    {
                                        bool flag58 = segResult.longAxis > num && segResult.shortAxis <= num3;
                                        if (flag58)
                                        {
                                            bool flag59 = (num19 & 256) <= 0;
                                            if (flag59)
                                            {
                                                num19 += 256;
                                            }
                                            bool flag60 = (num19 & 4096) <= 0;
                                            if (flag60)
                                            {
                                                num19 += 4096;
                                            }
                                        }
                                        else
                                        {
                                            bool flag61 = segResult.longAxis < num2 && segResult.shortAxis >= num4 && segResult.shortAxis <= num3;
                                            if (flag61)
                                            {
                                                bool flag62 = (num19 & 128) <= 0;
                                                if (flag62)
                                                {
                                                    num19 += 128;
                                                }
                                                bool flag63 = (num19 & 4096) <= 0;
                                                if (flag63)
                                                {
                                                    num19 += 4096;
                                                }
                                            }
                                            else
                                            {
                                                bool flag64 = (num19 & 64) <= 0 && (num19 & 4) <= 0 && (num19 & 8) <= 0;
                                                if (flag64)
                                                {
                                                    num19 += 64;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                bool flag65 = segResult.defectType == 3 && (num19 == 0 || (num19 & 4096) > 0);
                if (flag65)
                {
                    bool flag66 = (num19 & 1024) <= 0;
                    if (flag66)
                    {
                        num19 += 1024;
                    }
                    bool flag67 = (num19 & 4096) <= 0;
                    if (flag67)
                    {
                        num19 += 4096;
                    }
                }
            }
            bool flag68 = num8 == 0.0;
            if (flag68)
            {
                num8 = 0.4;
            }
            bool flag69 = num7 == 0.0;
            if (flag69)
            {
                num7 = 0.7;
            }
            bool flag70 = num13 == 0.0;
            if (flag70)
            {
                num13 = 0.85;
            }
            bool flag71 = num14 == 0.0;
            if (flag71)
            {
                num14 = 1.0;
            }
            acrosomeType = 0;
            bool flag72 = segResult.acrosomeRatio == 0.0;
            if (flag72)
            {
                acrosomeType = 1;
            }
            else
            {
                bool flag73 = segResult.acrosomeRatio >= num8 && segResult.acrosomeRatio <= num7;
                if (flag73)
                {
                    acrosomeType = 32;
                }
                else
                {
                    bool flag74 = segResult.acrosomeRatio < num8;
                    if (flag74)
                    {
                        acrosomeType = 2;
                    }
                    else
                    {
                        acrosomeType = 4;
                    }
                }
            }
            bool flag75 = segResult.acrosomeVacuolesNum > 2;
            if (flag75)
            {
                acrosomeType += 8;
            }
            bool flag76 = segResult.bigVacuolesNum > 0;
            if (flag76)
            {
                acrosomeType += 64;
            }
            bool flag77 = segResult.acrosome_uniformity < num13;
            if (flag77)
            {
                acrosomeType += 16;
            }
            bool flag78 = segResult.acrosome_uniformity < 0.0;
            if (flag78)
            {
                acrosomeType = 1;
            }
            double num20 = 1.0 - num8;
            double num21 = 1.0 - num7;
            bool flag79 = num15 == 0.0;
            if (flag79)
            {
                num15 = 0.85;
            }
            bool flag80 = num16 == 0.0;
            if (flag80)
            {
                num16 = 1.0;
            }
            kernelType = 0;
            double num22 = 1.0 - segResult.acrosomeRatio;
            bool flag81 = num22 == 0.0;
            if (flag81)
            {
                kernelType = 1;
            }
            else
            {
                bool flag82 = num22 >= num21 && num22 <= num20;
                if (flag82)
                {
                    kernelType = 32;
                }
                else
                {
                    bool flag83 = num22 < num21;
                    if (flag83)
                    {
                        kernelType = 2;
                    }
                    else
                    {
                        kernelType = 4;
                    }
                }
            }
            bool flag84 = segResult.kernelVacuolesNum > 0;
            if (flag84)
            {
                kernelType += 8;
            }
            bool flag85 = segResult.kernel_uniformity < num15;
            if (flag85)
            {
                kernelType += 16;
            }
            bool flag86 = segResult.kernel_uniformity < 0.0;
            if (flag86)
            {
                kernelType = 1;
            }
            bool flag87 = num18 == 0.0;
            if (flag87)
            {
                num18 = 1.0;
            }
            bool flag88 = num17 == 0.0;
            if (flag88)
            {
                num17 = 30.0;
            }
            middlePieceType = 0;
            return num19;
        }

        // Token: 0x06000585 RID: 1413 RVA: 0x000214A8 File Offset: 0x0001F6A8
        private static bool get_subclinical_num(Model.Sperm sp)
        {
            int num = 0;
            int num2 = 0;
            int num3 = 0;
            bool flag = sp.shapeType == 2;
            if (flag)
            {
                num++;
            }
            bool flag2 = sp.shapeType == 128;
            if (flag2)
            {
                num++;
            }
            bool flag3 = sp.shapeType == 256;
            if (flag3)
            {
                num++;
            }
            bool flag4 = sp.shapeType == 512;
            if (flag4)
            {
                num++;
            }
            bool flag5 = sp.acrosomeType == 48;
            if (flag5)
            {
                num2++;
            }
            bool flag6 = sp.kernelType == 48;
            if (flag6)
            {
                num3++;
            }
            bool flag7 = false;
            bool flag8 = num >= 1 && sp.acrosomeType == 32 && sp.kernelType == 32;
            bool result;
            if (flag8)
            {
                result = true;
            }
            else
            {
                bool flag9 = num2 == 1 && sp.shapeType == 0 && sp.kernelType == 32;
                if (flag9)
                {
                    result = true;
                }
                else
                {
                    bool flag10 = num3 == 1 && sp.shapeType == 0 && sp.acrosomeType == 32;
                    result = (flag10 || flag7);
                }
            }
            return result;
        }

        // Token: 0x06000586 RID: 1414 RVA: 0x000215B4 File Offset: 0x0001F7B4
        private static Model.Sperm validSperm(Model.Sperm sperm)
        {
            bool flag = true;

            foreach (Model.Mrfrules mrf_rules in MRFSegment.mrf_validrules)
            {
                double num = MRFSegment.get_spermFieldValue(mrf_rules.Usefield, sperm);
                int num2 = 0;
                bool positive = mrf_rules.Positive;
                if (positive)
                {
                    bool flag2 = num < mrf_rules.Min_value;
                    if (flag2)
                    {
                        num2 = 0;
                    }
                }
                bool flag3 = num >= mrf_rules.Min_value && num <= mrf_rules.Max_value;
                if (flag3)
                {
                    num2 = 1;
                }
                bool flag4 = num > mrf_rules.Max_value;
                if (flag4)
                {
                    num2 = 2;
                }
                bool flag5 = mrf_rules.Ruletype == "prefilter";
                if (flag5)
                {
                    sperm = MRFSegment.set_spermFieldValid("valid", num2, sperm);
                }
                else
                {
                    bool flag6 = mrf_rules.Ruletype == "validation";
                    if (flag6)
                    {
                        sperm = MRFSegment.set_spermFieldValid(mrf_rules.Usefield, num2, sperm);
                        flag = (flag && num2 == 1);
                    }
                }
            }
            return sperm;
        }

        // Token: 0x06000587 RID: 1415 RVA: 0x000216CC File Offset: 0x0001F8CC
        private static double get_spermFieldValue(string field, Model.Sperm sperm)
        {
            double result = 0.0;
            uint num = PrivateImplementationDetails.ComputeStringHash(field);
            if (num <= 1625565073U)
            {
                if (num <= 756233749U)
                {
                    if (num <= 520971537U)
                    {
                        if (num != 139002689U)
                        {
                            if (num == 520971537U)
                            {
                                if (field == "kernelArea")
                                {
                                    result = sperm.kernelArea;
                                }
                            }
                        }
                        else if (field == "SymmetryRatioLongAxis")
                        {
                            bool flag = sperm.SymmetryRatioLongAxis > 1.0;
                            if (flag)
                            {
                                sperm.SymmetryRatioLongAxis = 1.0;
                            }
                            result = sperm.SymmetryRatioLongAxis;
                        }
                    }
                    else if (num != 632100094U)
                    {
                        if (num == 756233749U)
                        {
                            if (field == "ellipsRatio")
                            {
                                result = sperm.ellipsRatio;
                            }
                        }
                    }
                    else if (field == "longAxis")
                    {
                        result = sperm.longAxis;
                    }
                }
                else if (num <= 1365730627U)
                {
                    if (num != 1355739734U)
                    {
                        if (num == 1365730627U)
                        {
                            if (field == "acrosomeArea")
                            {
                                result = sperm.acrosomeArea;
                            }
                        }
                    }
                    else if (field == "SymmetryRatio")
                    {
                        bool flag2 = sperm.SymmetryRatio > 1.0;
                        if (flag2)
                        {
                            sperm.SymmetryRatio = 1.0;
                        }
                        result = sperm.SymmetryRatio;
                    }
                }
                else if (num != 1487795236U)
                {
                    if (num != 1530455080U)
                    {
                        if (num == 1625565073U)
                        {
                            if (field == "bigVaucolesNum")
                            {
                                result = (double)sperm.bigVaucolesNum;
                            }
                        }
                    }
                    else if (field == "girthCV")
                    {
                        result = sperm.girthCV;
                    }
                }
                else if (field == "shortAxis")
                {
                    result = sperm.shortAxis;
                }
            }
            else if (num <= 2370703233U)
            {
                if (num <= 1761631401U)
                {
                    if (num != 1709015450U)
                    {
                        if (num == 1761631401U)
                        {
                            if (field == "areaCV")
                            {
                                bool flag3 = sperm.areaCV > 1.0;
                                if (flag3)
                                {
                                    sperm.areaCV = 1.0;
                                }
                                result = sperm.areaCV;
                            }
                        }
                    }
                    else if (field == "spermGirth")
                    {
                        result = sperm.spermGirth;
                    }
                }
                else if (num != 1793217280U)
                {
                    if (num != 2034176064U)
                    {
                        if (num == 2370703233U)
                        {
                            if (field == "middlepieceAngle")
                            {
                                result = ((sperm.middlepieceAngle == 360.0) ? 0.0 : sperm.middlepieceAngle);
                            }
                        }
                    }
                    else if (field == "acrosomeVacuolesNum")
                    {
                        result = (double)sperm.acrosomeVacuolesNum;
                    }
                }
                else if (field == "ellipseCV")
                {
                    bool flag4 = sperm.ellipseCV > 1.0;
                    if (flag4)
                    {
                        sperm.ellipseCV = 1.0;
                    }
                    result = sperm.ellipseCV;
                }
            }
            else if (num <= 3182818389U)
            {
                if (num != 2846316930U)
                {
                    if (num == 3182818389U)
                    {
                        if (field == "vacuolesNum")
                        {
                            result = (double)sperm.vacuolesNum;
                        }
                    }
                }
                else if (field == "kernelVacuolesNum")
                {
                    result = (double)sperm.kernelVacuolesNum;
                }
            }
            else if (num != 3260669877U)
            {
                if (num != 3542396125U)
                {
                    if (num == 3958131438U)
                    {
                        if (field == "middlepieceWidth")
                        {
                            result = sperm.middlepieceWidth;
                        }
                    }
                }
                else if (field == "acrosomeRatio")
                {
                    bool flag5 = sperm.acrosomeRatio > 1.0;
                    if (flag5)
                    {
                        sperm.acrosomeRatio = 1.0;
                    }
                    result = sperm.acrosomeRatio;
                }
            }
            else if (field == "spermArea")
            {
                result = sperm.spermArea;
            }
            return result;
        }

        // Token: 0x06000588 RID: 1416 RVA: 0x00021B70 File Offset: 0x0001FD70
        private static Model.Sperm set_spermFieldValid(string field, int value, Model.Sperm sperm)
        {
            uint num = PrivateImplementationDetails.ComputeStringHash(field);
            if (num <= 1625565073U)
            {
                if (num <= 1132259953U)
                {
                    if (num <= 520971537U)
                    {
                        if (num != 139002689U)
                        {
                            if (num == 520971537U)
                            {
                                if (field == "kernelArea")
                                {
                                    sperm.kernelAreaValid = value;
                                }
                            }
                        }
                        else if (field == "SymmetryRatioLongAxis")
                        {
                            sperm.SymmetryRatioLongAxisValid = value;
                        }
                    }
                    else if (num != 632100094U)
                    {
                        if (num != 756233749U)
                        {
                            if (num == 1132259953U)
                            {
                                if (field == "valid")
                                {
                                    sperm.valid = (sperm.valid && value == 1);
                                }
                            }
                        }
                        else if (field == "ellipsRatio")
                        {
                            sperm.ellipseRatioValid = value;
                        }
                    }
                    else if (field == "longAxis")
                    {
                        sperm.longAxisValid = value;
                    }
                }
                else if (num <= 1365730627U)
                {
                    if (num != 1355739734U)
                    {
                        if (num == 1365730627U)
                        {
                            if (field == "acrosomeArea")
                            {
                                sperm.acrosomeAreaValid = value;
                            }
                        }
                    }
                    else if (field == "SymmetryRatio")
                    {
                        sperm.SymmetryRatioValid = value;
                    }
                }
                else if (num != 1487795236U)
                {
                    if (num != 1530455080U)
                    {
                        if (num == 1625565073U)
                        {
                            if (field == "bigVaucolesNum")
                            {
                                sperm.vacuolesValid = (sperm.vacuolesValid && value == 1);
                                sperm.bigVacuolesNumValid = (value == 1);
                            }
                        }
                    }
                    else if (field == "girthCV")
                    {
                        sperm.girthCVValid = ((value == 1) ? value : 0);
                    }
                }
                else if (field == "shortAxis")
                {
                    sperm.shortAxisValid = value;
                }
            }
            else if (num <= 2370703233U)
            {
                if (num <= 1761631401U)
                {
                    if (num != 1709015450U)
                    {
                        if (num == 1761631401U)
                        {
                            if (field == "areaCV")
                            {
                                sperm.areaCVValid = value;
                            }
                        }
                    }
                    else if (field == "spermGirth")
                    {
                        sperm.spermGirthValid = value;
                    }
                }
                else if (num != 1793217280U)
                {
                    if (num != 2034176064U)
                    {
                        if (num == 2370703233U)
                        {
                            if (field == "middlepieceAngle")
                            {
                                sperm.middlepieceAngleValid = (value == 1);
                                sperm.middlePieceValid = (sperm.middlePieceValid && value == 1);
                            }
                        }
                    }
                    else if (field == "acrosomeVacuolesNum")
                    {
                        sperm.vacuolesValid = (sperm.vacuolesValid && value == 1);
                    }
                }
                else if (field == "ellipseCV")
                {
                    sperm.ellipseCVValid = ((value == 1) ? value : 0);
                }
            }
            else if (num <= 3260669877U)
            {
                if (num != 2846316930U)
                {
                    if (num != 3182818389U)
                    {
                        if (num == 3260669877U)
                        {
                            if (field == "spermArea")
                            {
                                sperm.spermAreaValid = value;
                            }
                        }
                    }
                    else if (field == "vacuolesNum")
                    {
                        sperm.vacuolesValid = (sperm.vacuolesValid && value == 1);
                        sperm.vacuolesNumValid = (value == 1);
                    }
                }
                else if (field == "kernelVacuolesNum")
                {
                    sperm.vacuolesValid = (sperm.vacuolesValid && value == 1);
                    sperm.kernelVacuolesNumValid = (value == 1);
                }
            }
            else if (num != 3542396125U)
            {
                if (num != 3780968789U)
                {
                    if (num == 3958131438U)
                    {
                        if (field == "middlepieceWidth")
                        {
                            sperm.middlepieceWidthValid = (value == 1);
                            sperm.middlePieceValid = (sperm.middlePieceValid && value == 1);
                        }
                    }
                }
                else if (field == "kernel_uniformity")
                {
                    sperm.kernel_uniformityValid = (value == 1);
                }
            }
            else if (field == "acrosomeRatio")
            {
                sperm.acrosomeRatioValid = value;
            }
            return sperm;
        }

        // Token: 0x0400031C RID: 796
        private static double _MIN_SCORE_FOR_OBJECT = 0.75;

        // Token: 0x0400031D RID: 797
        private static bool _useGpu = true;

        // Token: 0x0400031E RID: 798
        //private static clsSpermMySqlDb spermdb = new clsSpermMySqlDb();

        // Token: 0x0400031F RID: 799
        public static ObservableCollection<Model.Mrfrules> mrf_validrules = new ObservableCollection<Model.Mrfrules>();

        // Token: 0x04000320 RID: 800
        private static object locker = new object();
    }
}
