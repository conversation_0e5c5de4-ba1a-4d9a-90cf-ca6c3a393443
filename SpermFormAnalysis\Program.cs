﻿using SpermFormAnalysis.Class;
using SpermFormAnalysis.PageForm;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            log4net.Config.XmlConfigurator.Configure(new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "//log4net.config"));
            GlobalProperty.InitParams();

            // 初始化GPU内存管理
            try
            {
                SpermFormAnalysis.utils.GPUMemoryManager.AutoConfigureGPUMemory();
                SpermFormAnalysis.utils.LogHelper.WriteInfoLog("GPU内存管理初始化完成");
                SpermFormAnalysis.utils.LogHelper.WriteInfoLog(SpermFormAnalysis.utils.GPUMemoryManager.GetGPUConfigInfo());
            }
            catch (Exception ex)
            {
                SpermFormAnalysis.utils.LogHelper.WriteErrLog($"GPU内存管理初始化失败: {ex.Message}");
            }

            bool initiallyOwned = true;
            bool isCreated;
            Mutex m = new Mutex(initiallyOwned, "Sperm", out isCreated);
            if (!(initiallyOwned && isCreated))//如果已启动一个FCMCAS实例
            {
                MessageBox.Show("您已经启动了一个精子形态分析管理软件 ，此软件不能同时启动多个！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Application.Exit();
            }
            else
            {
                //检测数据库 可以在这个地方自动安装数据库

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                //Login login = new Login();
                //if (login.ShowDialog() == DialogResult.OK)
                //{

                //}
                Application.EnableVisualStyles();
                Application.Run(new Mains());
            }
        }
    }
}
