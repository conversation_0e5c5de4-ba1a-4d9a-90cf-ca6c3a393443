﻿using SpermFormAnalysis.Class;
using SpermFormAnalysis.PageForm;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis
{
    internal static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            log4net.Config.XmlConfigurator.Configure(new FileInfo(AppDomain.CurrentDomain.BaseDirectory + "//log4net.config"));
            GlobalProperty.InitParams();

            // 初始化GPU内存管理
            try
            {
                // 简化的GPU配置初始化
                InitializeGPUConfig();
            }
            catch (Exception ex)
            {
                // 使用MessageBox显示错误，因为LogHelper可能还没初始化
                System.Console.WriteLine($"GPU内存管理初始化失败: {ex.Message}");
            }

            bool initiallyOwned = true;
            bool isCreated;
            Mutex m = new Mutex(initiallyOwned, "Sperm", out isCreated);
            if (!(initiallyOwned && isCreated))//如果已启动一个FCMCAS实例
            {
                MessageBox.Show("您已经启动了一个精子形态分析管理软件 ，此软件不能同时启动多个！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Application.Exit();
            }
            else
            {
                //检测数据库 可以在这个地方自动安装数据库

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                //Login login = new Login();
                //if (login.ShowDialog() == DialogResult.OK)
                //{

                //}
                Application.EnableVisualStyles();
                Application.Run(new Mains());
            }
        }

        /// <summary>
        /// 简化的GPU配置初始化
        /// </summary>
        private static void InitializeGPUConfig()
        {
            try
            {
                // 设置基本的GPU环境变量
                System.Environment.SetEnvironmentVariable("TF_CPP_MIN_LOG_LEVEL", "2");
                System.Environment.SetEnvironmentVariable("TF_FORCE_GPU_ALLOW_GROWTH", "true");
                System.Environment.SetEnvironmentVariable("TF_GPU_MEMORY_LIMIT", "1400");

                // 读取配置文件
                string configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "gpu_config.ini");
                if (System.IO.File.Exists(configPath))
                {
                    var lines = System.IO.File.ReadAllLines(configPath);
                    foreach (var line in lines)
                    {
                        if (line.StartsWith("ForceUseCPU=true"))
                        {
                            System.Environment.SetEnvironmentVariable("CUDA_VISIBLE_DEVICES", "");
                            break;
                        }
                    }
                }

                System.Console.WriteLine("GPU配置初始化完成");
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"GPU配置初始化失败: {ex.Message}");
            }
        }
    }
}
