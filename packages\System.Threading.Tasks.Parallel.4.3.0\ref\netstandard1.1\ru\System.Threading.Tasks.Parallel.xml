﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Tasks.Parallel</name>
  </assembly>
  <members>
    <member name="T:System.Threading.Tasks.Parallel">
      <summary>Предоставляет поддержку параллельных циклов и областей.</summary>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Action{System.Int32})">
      <summary>Выполняет цикл for (For в Visual Basic), обеспечивая возможность параллельного выполнения итераций.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Action{System.Int32,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет цикл for (For в Visual Basic), обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.  </summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int32,System.Int32,System.Func{``0},System.Func{System.Int32,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Выполняет цикл for (For в Visual Basic) с локальными данными потока, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Action{System.Int32})">
      <summary>Выполняет цикл for (For в Visual Basic), обеспечивая возможность параллельного выполнения итераций и настройки параметров цикла.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Action{System.Int32,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет цикл for (For в Visual Basic), обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int32,System.Int32,System.Threading.Tasks.ParallelOptions,System.Func{``0},System.Func{System.Int32,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Выполняет цикл for (For в Visual Basic) с локальными данными потока, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Action{System.Int64})">
      <summary>Выполняет цикл for (For в Visual Basic) с 64-разрядными индексами, в котором итерации могут выполняться параллельно.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Action{System.Int64,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет цикл for (For в Visual Basic) с 64-разрядными индексами, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура <see cref="T:System.Threading.Tasks.ParallelLoopResult" />, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int64,System.Int64,System.Func{``0},System.Func{System.Int64,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Выполняет цикл for (For в Visual Basic) с 64-разрядными индексами и локальными данными потока, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Action{System.Int64})">
      <summary>Выполняет цикл for (For в Visual Basic) с 64-разрядными индексами, обеспечивая возможность параллельного выполнения итераций и настройки параметров цикла.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Action{System.Int64,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет цикл for (For в Visual Basic) с 64-разрядными индексами, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.For``1(System.Int64,System.Int64,System.Threading.Tasks.ParallelOptions,System.Func{``0},System.Func{System.Int64,System.Threading.Tasks.ParallelLoopState,``0,``0},System.Action{``0})">
      <summary>Выполняет цикл for (For в Visual Basic) с 64-разрядными индексами и локальными данными потока, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="fromInclusive">Начальный индекс, включительно.</param>
      <param name="toExclusive">Конечный индекс, не включительно.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждого потока.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждого потока.</param>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.OrderablePartitioner{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Упорядочиваемый разделитель, содержащий исходный источник данных.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The <see cref="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized" /> property in the source orderable partitioner returns false.-or-Any methods in the source orderable partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.OrderablePartitioner{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с локальными данными потока для объекта <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Упорядочиваемый разделитель, содержащий исходный источник данных.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.OrderablePartitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Упорядочиваемый разделитель, содержащий исходный источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is  null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The <see cref="P:System.Collections.Concurrent.OrderablePartitioner`1.KeysNormalized" /> property in the <paramref name="source" /> orderable partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> orderable partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.OrderablePartitioner{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с 64-разрядными индексами и локальными данными потока для объекта <see cref="T:System.Collections.Concurrent.OrderablePartitioner`1" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Упорядочиваемый разделитель, содержащий исходный источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> or <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null  partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.Concurrent.Partitioner" />, обеспечивая возможность параллельного выполнения итераций.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Разделитель, содержащий исходный источник данных.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is  null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.-or-The <see cref="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)" /> method in the <paramref name="source" /> partitioner does not return the correct number of partitions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.Concurrent.Partitioner" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Разделитель, содержащий исходный источник данных.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-A method in the <paramref name="source" /> partitioner returns null.-or-The <see cref="M:System.Collections.Concurrent.Partitioner`1.GetPartitions(System.Int32)" /> method in the <paramref name="source" /> partitioner does not return the correct number of partitions.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.Partitioner{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с локальными данными потока для объекта <see cref="T:System.Collections.Concurrent.Partitioner" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Разделитель, содержащий исходный источник данных.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.Concurrent.Partitioner" />, обеспечивая возможность параллельного выполнения итераций и настройки параметров цикла.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Разделитель, содержащий исходный источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.Concurrent.Partitioner" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Разделитель, содержащий исходный источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /> partitioner returns false.-or-The exception that is thrown when any methods in the <paramref name="source" /> partitioner return null.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Concurrent.Partitioner{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с локальными данными потока для объекта <see cref="T:System.Collections.Concurrent.Partitioner" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Разделитель, содержащий исходный источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип элементов в объекте <paramref name="source" />.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Collections.Concurrent.Partitioner`1.SupportsDynamicPartitions" /> property in the <paramref name="source" /><see cref="T:System.Collections.Concurrent.Partitioner" /> returns false or the partitioner returns null partitions.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с 64-разрядными индексами для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с локальными данными потока для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с локальными данными потока для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций и настройки параметров цикла.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Action{``0,System.Threading.Tasks.ParallelLoopState,System.Int64})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с 64-разрядными индексами для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с локальными данными потока для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.ForEach``2(System.Collections.Generic.IEnumerable{``0},System.Threading.Tasks.ParallelOptions,System.Func{``1},System.Func{``0,System.Threading.Tasks.ParallelLoopState,System.Int64,``1,``1},System.Action{``1})">
      <summary>Выполняет операцию foreach (For Each в Visual Basic) с 64-разрядными индексами и локальными данными потока для объекта <see cref="T:System.Collections.IEnumerable" />, обеспечивая возможность параллельного выполнения итераций, настройки параметров цикла, а также контроля состояния цикла и управления этим состоянием.</summary>
      <returns>Структура, в которой содержатся сведения о выполненной части цикла.</returns>
      <param name="source">Перечислимый источник данных.</param>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="localInit">Делегат функции, который возвращает начальное состояние локальных данных для каждой задачи.</param>
      <param name="body">Делегат, который вызывается один раз за итерацию.</param>
      <param name="localFinally">Делегат, который выполняет финальное действие с локальным состоянием каждой задачи.</param>
      <typeparam name="TSource">Тип данных в источнике.</typeparam>
      <typeparam name="TLocal">Тип данных, локальных для потока.</typeparam>
      <exception cref="T:System.ArgumentNullException">The <paramref name="source" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.-or-The <paramref name="body" /> argument is null.-or-The <paramref name="localInit" /> argument is null.-or-The <paramref name="localFinally" /> argument is null.</exception>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> argument is canceled.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
      <exception cref="T:System.AggregateException">The exception that contains all the individual exceptions thrown on all threads.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.Invoke(System.Action[])">
      <summary>Выполняет все предоставленные действия, по возможности в параллельном режиме.</summary>
      <param name="actions">Массив действий <see cref="T:System.Action" /> для выполнения.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="actions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that is thrown when any action in the <paramref name="actions" /> array throws an exception.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="actions" /> array contains a null element.</exception>
    </member>
    <member name="M:System.Threading.Tasks.Parallel.Invoke(System.Threading.Tasks.ParallelOptions,System.Action[])">
      <summary>Выполняет каждое из указанных действий по возможности в параллельном режиме, если операция не отменена пользователем.</summary>
      <param name="parallelOptions">Объект, используемый для настройки поведения этой операции.</param>
      <param name="actions">Массив действий для выполнения.</param>
      <exception cref="T:System.OperationCanceledException">The <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> is set.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="actions" /> argument is null.-or-The <paramref name="parallelOptions" /> argument is null.</exception>
      <exception cref="T:System.AggregateException">The exception that is thrown when any action in the <paramref name="actions" /> array throws an exception.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="actions" /> array contains a null element.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Threading.CancellationTokenSource" /> associated with the <see cref="T:System.Threading.CancellationToken" /> in the <paramref name="parallelOptions" /> has been disposed.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ParallelLoopResult">
      <summary>Предоставляет состояние выполнения цикла <see cref="T:System.Threading.Tasks.Parallel" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopResult.IsCompleted">
      <summary>Получает значение, указывающее, дошел ли цикл до завершения, то есть все итерации цикла выполнены и он не получил запроса на преждевременное прерывание работы.</summary>
      <returns>Значение true, если цикл был завершен; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopResult.LowestBreakIteration">
      <summary>Получает индекс нижней итерации, из которой был вызван метод <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" />.</summary>
      <returns>Возвращает целое число, представляющее самую нижнюю итерацию, из которой был вызван оператор Break.</returns>
    </member>
    <member name="T:System.Threading.Tasks.ParallelLoopState">
      <summary>Позволяет итерациям параллельных циклов взаимодействовать с другими итерациями.Экземпляр этого класса предоставляется каждому циклу классом <see cref="T:System.Threading.Tasks.Parallel" />; невозможно создавать экземпляры в коде.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ParallelLoopState.Break">
      <summary>Сообщает, что цикл <see cref="T:System.Threading.Tasks.Parallel" /> должен прекратить выполнение итераций после текущей в первый удобный для системы момент. </summary>
      <exception cref="T:System.InvalidOperationException">Был ранее вызван метод <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" />.<see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> и <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> не могут использоваться в сочетании итерациями одного и того же цикла.</exception>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.IsExceptional">
      <summary>Получает значение, указывающее, возникло ли в какой-либо итерации цикла исключение, не обработанное данной итерацией. </summary>
      <returns>Значение true, если было вызвано необработанное исключение; в противном случае — значение false.  </returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.IsStopped">
      <summary>Получает значение, указывающее, вызывала ли какая-либо итерация цикла метод <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" />. </summary>
      <returns>Значение true, если какая-либо итерация остановила цикл, вызвав метод <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" />; в противном случае — значение false. </returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.LowestBreakIteration">
      <summary>Получает первую итерацию цикла, из которой был вызван метод <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" />. </summary>
      <returns>Получает первую итерацию, из которой был вызван метод <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" />.В случае цикла <see cref="M:System.Threading.Tasks.Parallel.ForEach``1(System.Collections.Concurrent.Partitioner{``0},System.Action{``0})" /> значение основано на внутренне создаваемом индексе.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelLoopState.ShouldExitCurrentIteration">
      <summary>Получает значение, указывающее, следует ли текущей итерации цикла завершить работу на основе запросов от этой или других итераций.</summary>
      <returns>Значение true, если текущая итерация должна завершать работу; в противном случае — значение false. </returns>
    </member>
    <member name="M:System.Threading.Tasks.ParallelLoopState.Stop">
      <summary>Сообщает, что цикл <see cref="T:System.Threading.Tasks.Parallel" /> должен прекратить выполнение в первый удобный для системы момент.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> Метод был вызван ранее.<see cref="M:System.Threading.Tasks.ParallelLoopState.Break" /> и <see cref="M:System.Threading.Tasks.ParallelLoopState.Stop" /> не могут использоваться в сочетании итерациями одного и того же цикла.</exception>
    </member>
    <member name="T:System.Threading.Tasks.ParallelOptions">
      <summary>Хранит параметры, настраивающие работу методов класса <see cref="T:System.Threading.Tasks.Parallel" />.</summary>
    </member>
    <member name="M:System.Threading.Tasks.ParallelOptions.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Threading.Tasks.ParallelOptions" />.</summary>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.CancellationToken">
      <summary>Получает или задает свойство <see cref="T:System.Threading.CancellationToken" />, связанное с экземпляром <see cref="T:System.Threading.Tasks.ParallelOptions" />.</summary>
      <returns>Токен, связанный с данным экземпляром.</returns>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.MaxDegreeOfParallelism">
      <summary>Возвращает или задает максимальное число параллельных задач включается этим экземпляром <see cref="T:System.Threading.Tasks.ParallelOptions" />.</summary>
      <returns>Целое число, представляющее максимальную степень параллелизма.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Свойство устанавливается значение ноль или значение, которое меньше -1.</exception>
    </member>
    <member name="P:System.Threading.Tasks.ParallelOptions.TaskScheduler">
      <summary>Получает или задает свойство <see cref="T:System.Threading.Tasks.TaskScheduler" />, связанное с экземпляром <see cref="T:System.Threading.Tasks.ParallelOptions" />.Установка этого свойства на значение NULL означает, что следует использовать текущий планировщик.</summary>
      <returns>Планировщик заданий, связанный с данным экземпляром.</returns>
    </member>
  </members>
</doc>