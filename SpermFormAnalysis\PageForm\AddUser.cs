﻿using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.PageForm
{
    public partial class AddUser : UIEditForm
    {
        public AddUser()
        {
            InitializeComponent();
            this.levelBox.SelectedIndex = 0;
            if (UserManage.flag == 1)
            {
                //编辑
                userBox.Text = UserManage.admin.Admin_ID;
                passwordBox.Text = UserManage.admin.Admin_PWD;
                this.levelBox.Text = UserManage.admin.Admin_Level.ToString();
            }
        }
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        protected override bool CheckData()
        {

            UserManage.admin.Admin_ID = userBox.Text;
            UserManage.admin.Admin_PWD = passwordBox.Text;
            if (this.levelBox.Text == "超级操作员")
            {
                UserManage.admin.Admin_Level = 0;
            }
            else
            {
                UserManage.admin.Admin_Level = 1;
            }
            
            return CheckEmpty(userBox, "请输入用户名")
                   && CheckEmpty(passwordBox, "请输入密码");
        }
    }
}
