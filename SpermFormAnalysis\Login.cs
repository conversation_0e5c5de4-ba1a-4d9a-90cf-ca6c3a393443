﻿using MySql.Data.MySqlClient;
using SpermFormAnalysis.Class;
using SpermFormAnalysis.DataHelper;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis
{
    public partial class Login : Sunny.UI.UILoginForm
    {
        //DBUtils DBUtils = new DBUtils();
        public Login()
        {
            InitializeComponent();
            if (GlobalProperty.Remember_User)
            {
                this.Password = GlobalProperty.Admin_PWD;
            }
            if (GlobalProperty.Mode != "auto")
            {
                lblTitle.Text = "精子形态分析处理软件";
            }
            this.ControlBox = false;
        }

        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        private void Login_ButtonCancelClick(object sender, EventArgs e)
        {
            this.Close();
        }

        private void Login_ButtonLoginClick(object sender, EventArgs e)
        {

            if (String.IsNullOrEmpty(UserName))//检测用户名
            {
                ShowErrorNotifier("登录用户不许为空！");
                return;
            }
            if (String.IsNullOrEmpty(Password))//检测密码
            {
                ShowErrorNotifier("登录密码不许为空！");
                return;
            }
            //用户编码不重复
            try
            {
                var user = AdminServices.GetAdmin(UserName);
                if (user != null)
                {
                    GlobalProperty.Admin_ID = user.Admin_ID;
                    GlobalProperty.Admin_Name = user.Admin_Name;
                    if (GlobalProperty.Remember_User) GlobalProperty.Admin_PWD = user.Admin_PWD;
                    GlobalProperty.Admin_Level = user.Admin_Level.ToString() == "0" ? "超级操作员" : "普通操作员";
                    this.DialogResult = DialogResult.OK;
                }
                else
                {
                    ShowErrorNotifier("登录用户不正确！");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "软件提示");
            }



        }
    }
}
