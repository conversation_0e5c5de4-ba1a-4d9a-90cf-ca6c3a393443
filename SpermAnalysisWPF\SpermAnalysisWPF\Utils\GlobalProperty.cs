using System;
using System.IO;

namespace SpermAnalysisWPF.Utils
{
    public static class GlobalProperty
    {
        public static void InitParams()
        {
            try
            {
                // 确保configure目录存在
                string configureDir = Path.Combine(Environment.CurrentDirectory, "configure");
                if (!Directory.Exists(configureDir))
                {
                    Directory.CreateDirectory(configureDir);
                }

                // 创建manager.config文件
                string managerConfigPath = Path.Combine(configureDir, "manager.config");
                string rootPath = Environment.CurrentDirectory.Replace("\\", "/");
                
                string configContent = $"[system]\nroot={rootPath}";
                File.WriteAllText(managerConfigPath, configContent);

                // 确保其他必要目录存在
                EnsureDirectoryExists("tfmodel");
                EnsureDirectoryExists("database");
                EnsureDirectoryExists("images");
                EnsureDirectoryExists("images/sample");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GlobalProperty初始化失败: {ex.Message}");
            }
        }

        private static void EnsureDirectoryExists(string relativePath)
        {
            string fullPath = Path.Combine(Environment.CurrentDirectory, relativePath);
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }
        }
    }
}
