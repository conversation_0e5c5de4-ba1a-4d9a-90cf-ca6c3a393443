﻿using NPOI.SS.Formula.Functions;
using QtrfControl;
using SpermFormAnalysis.Class;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.PageForm
{
    public partial class Authorizes : UIPage
    {
        List<InterfCard> interfCards;
        public Authorizes()
        {
            InitializeComponent();
            if ("init" == GlobalProperty.Id)
            {
                this.btn.Text = "加密";
            }
            else
            {
                this.btn.Text = "确定";
                this.AuthorizeBox.Text = GlobalProperty.AutCod;
            }
            uiComboBox1.SelectedIndex = GlobalProperty.QtrfIndex;

            interfCards = CardMag.GetInterfCard();
        }
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        /// <summary>
        /// 注册授权
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_Click(object sender, EventArgs e)
        {
            string autCod = this.AuthorizeBox.Text.TrimEnd();
            if (string.IsNullOrEmpty(autCod))
            {
                ShowErrorTip("不能为空");
                return;
            }
            if ("init" == GlobalProperty.Id)
            {
                string jqm = clsUtility.GetCpuID();
                SoftKeyYT88 ytsoftkey = new SoftKeyYT88();
                string KeyPath = "";
                if (ytsoftkey.FindPort(0, ref KeyPath) != 0)
                {
                    MessageBox.Show("未找到加密锁,请插入加密锁后，再进行操作。");
                    return;
                }
                //设置ID
                short ver = 0;
                if (ytsoftkey.NT_GetVersionEx(ref ver, KeyPath) != 0) { MessageBox.Show("返回加密锁扩展版本号错误"); return; }
                if (ver < 32) { MessageBox.Show("锁的扩展版本少于32,不支持设置锁的ID"); return; }
                //String seed = jqm;//ID种子

                if (ytsoftkey.SetID(autCod, KeyPath) != 0)
                {
                    MessageBox.Show("设置ID错误。");
                    return;
                }
                int id_11 = 0, id_21 = 0;
                if (ytsoftkey.GetID(ref id_11, ref id_21, KeyPath) != 0) { return; }
                string res1 = id_11.ToString("X8") + id_21.ToString("X8");

                //设置机器码
                //iniFileUtil = new IniFileUtils(file);
                GlobalProperty.Id = res1 + jqm;
                GlobalProperty.Machine_Code = jqm;
                GlobalProperty.AutCod = autCod;//修改全局变量中的授权码
                //MessageBox.Show("加密成功！");
                ShowWarningDialog("加密成功");
                this.btn.Text = "确定";
            }
            else
            {
                GlobalProperty.AutCod = autCod;//修改全局变量中的授权码
                ShowSuccessDialog("修改成功");
            }
        }
        int icdev = 0;
        /// <summary>
        /// 卡次读取
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn1_Click(object sender, EventArgs e)
        {
            try
            {
            //    //读卡
            //    CardMag cm = new CardMag();
                string info = "";
                int restNumone = 0;
                if (!interfCards[uiComboBox1.SelectedIndex].ReadCard(ref info, ref restNumone, GlobalProperty.AutCod))
                {
                    //common.rf_beep(icdev, 30);
                    mifareone.rf_exit(icdev);
                    ShowErrorDialog(info);
                    return;
                }
                this.dis.Text = restNumone.ToString();

            }
            catch (Exception ex)
            {

                throw;
            }
        }

        private void uiComboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {
            GlobalProperty.QtrfIndex = uiComboBox1.SelectedIndex;
        }
    }
}
