using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SpermAnalysisWPF.Model
{
    [Table("Sampleinfo")]
    public class Sampleinfo
    {
        [Key]
        public string sampleId { get; set; }

        public string patientName { get; set; } = string.Empty;

        public string patientId { get; set; } = string.Empty;

        public int patientAge { get; set; }

        public string doctorName { get; set; } = string.Empty;

        public string hospitalName { get; set; } = string.Empty;

        public string department { get; set; } = string.Empty;

        public DateTime sampleDate { get; set; }

        public DateTime createTime { get; set; }

        public DateTime updateTime { get; set; }

        public string remarks { get; set; } = string.Empty;

        public int totalSpermCount { get; set; }

        public int normalSpermCount { get; set; }

        public double normalRate { get; set; }

        public int abnormalSpermCount { get; set; }

        public double abnormalRate { get; set; }

        public string analysisStatus { get; set; } = string.Empty;

        public Sampleinfo()
        {
            sampleId = Guid.NewGuid().ToString();
            createTime = DateTime.Now;
            updateTime = DateTime.Now;
            sampleDate = DateTime.Now;
            analysisStatus = "未分析";
        }
    }
}
