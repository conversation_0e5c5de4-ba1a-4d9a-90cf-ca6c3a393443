﻿using QtrfControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


namespace SpermFormAnalysis.Class
{
    class CardMag
    {
        int icdev = 0;//设备号
        uint tagtype = 0;//目标类型
        UInt32 snr = 0;//卡序号
        Byte size1 = 0;//数据长度
        int ordaut = 0;//剩余次数

        /// <summary>
        /// 读卡
        /// </summary>
        /// <returns></returns>
        public bool ReadCard(ref string info, ref int restNumone)
        {
            bool result;
            if (string.IsNullOrEmpty(GlobalProperty.AutCod))//查看实验是否已注册
            {
                info = "请先注册相应实验授权码！";
                result = false;
            }

            else
            {
                Int16 port = 100;
                //this.icdev = (int)IC64DLL.rf_init(251, 0);//初始化读卡器
                this.icdev = IC64DLL.rf_init(port, 115200);
                if (this.icdev < 0)
                {
                    info = "初始化读卡器失败 ";
                    IC64DLL.rf_beep(this.icdev, 50);
                    result = false;
                }
                else
                {
                    int temp = (int)IC64DLL.rf_reset(this.icdev, 3);//重启读卡器
                    if (temp != 0)
                    {
                        info = "重新加载读取失败";
                        IC64DLL.rf_beep(this.icdev, 50);
                        result = false;
                    }
                    else
                    {
                        temp = (int)IC64DLL.rf_request(this.icdev, 0, out this.tagtype);//检测是否有卡
                        if (temp != 0)
                        {
                            info = "未检测到授权卡";
                            IC64DLL.rf_beep(this.icdev, 50);
                            IC64DLL.rf_exit(icdev);
                            result = false;
                        }
                        else
                        {
                            temp = (int)IC64DLL.rf_anticoll(this.icdev, 0, out this.snr);//防冲突处理                        
                            if (temp != 0)
                            {
                                info = "请不要同时放置多张卡片";
                                IC64DLL.rf_beep(this.icdev, 50);
                                IC64DLL.rf_exit(icdev);
                                result = false;
                            }
                            else
                            {
                                temp = (int)IC64DLL.rf_select(this.icdev, this.snr, out this.size1);//选卡
                                if (temp != 0)
                                {
                                    info = "请选择正确的卡片";
                                    IC64DLL.rf_beep(this.icdev, 50);
                                    IC64DLL.rf_exit(icdev);
                                    result = false;
                                }
                                else
                                {
                                    byte[] key1 = new byte[17];
                                    byte[] key2 = new byte[7];
                                    key1 = Encoding.ASCII.GetBytes("1e2f3c4d5a6b");
                                    IC64DLL.a_hex(key1, key2, 12);
                                    temp = (int)IC64DLL.rf_authentication_pass(this.icdev, 0, 1, key2);//验证读卡器的密码是否与卡片密码一致
                                    if (temp != 0)
                                    {
                                        info = "读卡器密码与卡片密码不符";
                                        IC64DLL.rf_beep(this.icdev, 50);
                                        IC64DLL.rf_exit(icdev);
                                        result = false;
                                    }
                                    else
                                    {
                                        byte[] databuff = new byte[16];
                                        for (int i = 0; i < 16; i++)
                                            databuff[i] = 0;
                                        temp = (int)IC64DLL.rf_read(this.icdev, 4, databuff);//读取授权码

                                        if (temp != 0)//读取失败
                                        {
                                            info = "读取卡片授权码失败";
                                            IC64DLL.rf_beep(this.icdev, 50);
                                            IC64DLL.rf_exit(icdev);
                                            result = false;
                                        }
                                        else
                                        {
                                            //转换授权码
                                            byte[] bTemp = new byte[32];
                                            for (int i = 0; i < 32; i++)
                                                bTemp[i] = 0;
                                            IC64DLL.hex_a(databuff, bTemp, 16);  //转换格式

                                            //获得最终的授权码
                                            GlobalProperty.StrAutcod = Encoding.ASCII.GetString(bTemp, 0, bTemp.Length).ToLower();
                                            if (
                                               !GlobalProperty.StrAutcod.ToString().Equals(GlobalProperty.AutCod)
                                              )

                                            {
                                                info = "请使用本单位的卡！";
                                                IC64DLL.rf_beep(this.icdev, 50);
                                                IC64DLL.rf_exit(icdev);
                                                result = false;
                                            }
                                            else
                                            {
                                                temp = (int)IC64DLL.rf_read(this.icdev, 5, databuff);
                                                if (temp != 0)
                                                {
                                                    info = "读取授权码失败";
                                                    IC64DLL.rf_beep(this.icdev, 50);
                                                    IC64DLL.rf_exit(icdev);
                                                    result = false;
                                                }
                                                else
                                                {
                                                    IC64DLL.hex_a(databuff, bTemp, 16);
                                                    string strOrdaut = Encoding.ASCII.GetString(bTemp, 0, bTemp.Length).ToLower();
                                                    this.ordaut = Convert.ToInt32(strOrdaut);
                                                    restNumone = this.ordaut;
                                                    if (this.ordaut < 1)
                                                    {
                                                        info = "没有授权次数，请换卡再试！";
                                                        IC64DLL.rf_beep(this.icdev, 50);
                                                        IC64DLL.rf_exit(icdev);
                                                        result = false;
                                                    }
                                                    else
                                                    {
                                                        IC64DLL.rf_exit(this.icdev);//exit读卡器
                                                        result = true;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 写卡(将授权数减1)
        /// </summary>
        /// <returns></returns>
        public bool WriteCard(ref string info, ref int restNum)
        {
            //     this.ordaut=this.ordaut-119700;
            restNum = this.ordaut - 1;
            Int16 port = 100;
            int baud = 115200;
            //将授权数转换成读卡器能识别的格式
            this.icdev = (int)IC64DLL.rf_init(port, baud);//初始化读卡器
            string str_Ordaut = "00000000000000000000000000000000" + restNum.ToString();//构建初始授权数字符串
            str_Ordaut = str_Ordaut.Substring(str_Ordaut.Length - 32, 32);//将初始授权数字符串处理为卡能接受的格式
            byte[] buff = new byte[32];
            byte[] databuff = new byte[16];
            buff = Encoding.ASCII.GetBytes(str_Ordaut);
            IC64DLL.a_hex(buff, databuff, 32);
            int temp = (int)IC64DLL.rf_write(this.icdev, 5, databuff); //将新的授权数写入卡片中      
            bool result;
            GlobalProperty.Renumber = restNum;
            if (temp != 0)//写入失败
            {
                info = "写卡失败";
                IC64DLL.rf_beep(this.icdev, 50);
                IC64DLL.rf_exit(this.icdev);//exit读卡器
                result = false;
            }
            else
            {
                //IC64DLL.rf_beep(this.icdev, 10);
                IC64DLL.rf_beep(this.icdev, 10);
                IC64DLL.rf_exit(this.icdev);
                result = true;
            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="info"></param>
        /// <param name="deduct_num"> 扣卡次数</param>
        /// <returns></returns>
        public bool WriteCard2(ref string info, int deduct_num)
        {
            int restNum = 0;
            if (this.ReadCard(ref info, ref restNum) == false)
            {
                return false;
            }
            if ((restNum -= deduct_num) < 0)
            {
                info = "卡次不足";
                return false;
            }
            Int16 port = 100;
            int baud = 115200;
            //将授权数转换成读卡器能识别的格式
            this.icdev = (int)IC64DLL.rf_init(port, baud);//初始化读卡器
            string str_Ordaut = "00000000000000000000000000000000" + restNum.ToString();//构建初始授权数字符串
            str_Ordaut = str_Ordaut.Substring(str_Ordaut.Length - 32, 32);//将初始授权数字符串处理为卡能接受的格式
            byte[] buff = new byte[32];
            byte[] databuff = new byte[16];
            buff = Encoding.ASCII.GetBytes(str_Ordaut);
            IC64DLL.a_hex(buff, databuff, 32);
            int temp = (int)IC64DLL.rf_write(this.icdev, 5, databuff); //将新的授权数写入卡片中      
            bool result;
            GlobalProperty.Renumber = restNum;
            if (temp != 0)//写入失败
            {
                info = "写卡失败";
                IC64DLL.rf_beep(this.icdev, 50);
                IC64DLL.rf_exit(this.icdev);//exit读卡器
                result = false;
            }
            else
            {
                IC64DLL.rf_beep(this.icdev, 10);
                IC64DLL.rf_exit(this.icdev);
                result = true;
            }
            return result;
        }

        public static List<InterfCard> GetInterfCard()
        {
            List<InterfCard> interfCards = new List<InterfCard>();
            foreach (var assembly in AppDomain.CurrentDomain.GetAssemblies())
            {
                var types = assembly.GetTypes().Where(t => typeof(InterfCard).IsAssignableFrom(t) && !t.IsInterface);
                foreach (var type in types)
                {
                    interfCards.Add(Activator.CreateInstance(type) as InterfCard);
                }
            }
            return interfCards;
        }
    }
}
