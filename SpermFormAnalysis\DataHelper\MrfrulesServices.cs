﻿using MySql.Data.MySqlClient;
using SpermFormAnalysis.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Data.SqlClient;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.DataHelper
{
    public static class MrfrulesServices
    {
        private static string connectionString = @"Data Source=" + Application.StartupPath + @"\database\morsperm.db;Initial Catalog=sqlite;Integrated Security=True; =10";
        public static Mrfrules GetObject(long id)
        {
            Mrfrules obj = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from User where id=@id", conn);
                cmd.Parameters.Add(new SQLiteParameter("@id", id));
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        obj = new Mrfrules()
                        {
                            Id = Convert.ToInt32(dr["id"]),
                            Rulename = dr["rulename"].ToString(),
                            Ruletype = dr["ruletype"].ToString(),
                            Min_value = Convert.ToDouble(dr["min_value"]),
                            Max_value = Convert.ToDouble(dr["max_value"]),
                            Labels = dr["labels"].ToString(),
                            Usefield = dr["usefield"].ToString(),
                        };
                    }
                }
                conn.Close();
            }
            return obj;
        }

        public static ObservableCollection<Mrfrules> GetObjects()
        {
            ObservableCollection<Mrfrules> lists = new ObservableCollection<Mrfrules>();
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from Mrfrules", conn);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        Mrfrules obj = new Mrfrules();
                        obj.Id = Convert.ToInt32(dr["id"]);
                        bool flag2 = !Convert.IsDBNull(dr["rulename"]);
                        if (flag2)
                        {
                            obj.Rulename = Convert.ToString(dr["rulename"]);
                        }
                        bool flag3 = !Convert.IsDBNull(dr["ruletype"]);
                        if (flag3)
                        {
                            obj.Ruletype = Convert.ToString(dr["ruletype"]);
                        }
                        bool flag4 = !Convert.IsDBNull(dr["min_value"]);
                        if (flag4)
                        {
                            obj.Min_value = Convert.ToDouble(dr["min_value"]);
                        }
                        bool flag5 = !Convert.IsDBNull(dr["max_value"]);
                        if (flag5)
                        {
                            obj.Max_value = Convert.ToDouble(dr["max_value"]);
                        }
                        bool flag6 = !Convert.IsDBNull(dr["usefield"]);
                        if (flag6)
                        {
                            obj.Usefield = Convert.ToString(dr["usefield"]);
                        }
                        bool flag7 = !Convert.IsDBNull(dr["positive"]);
                        if (flag7)
                        {
                            obj.Positive = Convert.ToBoolean(dr["positive"]);
                        }
                        lists.Add(obj);
                    }
                }
                conn.Close();
            }
            return lists;
        }

        public static bool UpdateObject(Mrfrules obj)
        {
            int num = 0;
            string cmdText = "update mrf_rules set min_value=@min_value,max_value=@max_value where id=@id";
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand(cmdText);
                cmd.Parameters.Add(new SQLiteParameter("@id", obj.Id));
                cmd.Parameters.Add(new SQLiteParameter("@min_value", obj.Min_value));
                cmd.Parameters.Add(new SQLiteParameter("@max_value", obj.Max_value));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }

        public static bool InsertMrfRules(Mrfrules obj)
        {
            int num = 0;
            string cmdText = "insert into mrf_rules(rulename,ruletype,min_value,max_value,usefield)values(@rulename,@ruletype,@min_value,@max_value,@usefield)";
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand(cmdText);
                cmd.Parameters.Add(new SQLiteParameter("@id", obj.Id));
                cmd.Parameters.Add(new SQLiteParameter("@min_value", obj.Min_value));
                cmd.Parameters.Add(new SQLiteParameter("@max_value", obj.Max_value));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }

        public static bool ExistMrfRules(Mrfrules par)
        {
            int num = 0;
            ObservableCollection<Mrfrules> lists = new ObservableCollection<Mrfrules>();
            string cmdText = "select * from mrf_rules where rulename='" + par.Rulename + "'";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlCommand cmd = new SqlCommand(cmdText, conn);
                conn.Open();
                using (SqlDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        Mrfrules obj = new Mrfrules();
                        obj.Id = Convert.ToInt32(dr["id"]);
                        bool flag2 = !Convert.IsDBNull(dr["rulename"]);
                        if (flag2)
                        {
                            obj.Rulename = Convert.ToString(dr["rulename"]);
                        }
                        bool flag3 = !Convert.IsDBNull(dr["ruletype"]);
                        if (flag3)
                        {
                            obj.Ruletype = Convert.ToString(dr["ruletype"]);
                        }
                        bool flag4 = !Convert.IsDBNull(dr["min_value"]);
                        if (flag4)
                        {
                            obj.Min_value = Convert.ToDouble(dr["min_value"]);
                        }
                        bool flag5 = !Convert.IsDBNull(dr["max_value"]);
                        if (flag5)
                        {
                            obj.Max_value = Convert.ToDouble(dr["max_value"]);
                        }
                        bool flag6 = !Convert.IsDBNull(dr["usefield"]);
                        if (flag6)
                        {
                            obj.Usefield = Convert.ToString(dr["usefield"]);
                        }
                        bool flag7 = !Convert.IsDBNull(dr["positive"]);
                        if (flag7)
                        {
                            obj.Positive = Convert.ToBoolean(dr["positive"]);
                        }
                        lists.Add(obj);
                    }
                }
                conn.Close();
            }
            return lists.Count > 0;
        }
    }
}
