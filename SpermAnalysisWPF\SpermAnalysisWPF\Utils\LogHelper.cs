using System;
using System.IO;

namespace SpermAnalysisWPF.Utils
{
    public static class LogHelper
    {
        private static readonly object lockObj = new object();
        private static string logDirectory = Path.Combine(Environment.CurrentDirectory, "Log");

        static LogHelper()
        {
            // 确保日志目录存在
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
        }

        public static void WriteErrLog(string message)
        {
            WriteLog("ERROR", message);
        }

        public static void WriteInfoLog(string message)
        {
            WriteLog("INFO", message);
        }

        public static void WriteDebugLog(string message)
        {
            WriteLog("DEBUG", message);
        }

        private static void WriteLog(string level, string message)
        {
            lock (lockObj)
            {
                try
                {
                    string fileName = $"Log{DateTime.Now:yyyyMMdd}.txt";
                    string filePath = Path.Combine(logDirectory, fileName);
                    string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss},{level}: {message}";
                    
                    File.AppendAllText(filePath, logEntry + Environment.NewLine);
                    
                    // 同时输出到调试控制台
                    System.Diagnostics.Debug.WriteLine(logEntry);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"写入日志失败: {ex.Message}");
                }
            }
        }

        public static string GetTodayLogContent()
        {
            try
            {
                string fileName = $"Log{DateTime.Now:yyyyMMdd}.txt";
                string filePath = Path.Combine(logDirectory, fileName);

                if (File.Exists(filePath))
                {
                    var lines = File.ReadAllLines(filePath);
                    if (lines.Length == 0)
                    {
                        return "今日暂无日志记录";
                    }

                    // 只显示最近的100条日志，避免界面卡顿
                    var recentLines = lines.Length > 100 ? lines.Skip(lines.Length - 100) : lines;

                    var result = string.Join(Environment.NewLine, recentLines);

                    if (lines.Length > 100)
                    {
                        result = $"... (显示最近100条，共{lines.Length}条日志) ...\n\n" + result;
                    }

                    return result;
                }

                return "今日暂无日志记录";
            }
            catch (Exception ex)
            {
                return $"读取日志失败: {ex.Message}";
            }
        }

        public static void WriteAnalysisLog(string imageName, int spermCount, int normalCount, int abnormalCount)
        {
            string message = $"分析完成 - 图像:{imageName}, 总数:{spermCount}, 正常:{normalCount}, 异常:{abnormalCount}";
            WriteInfoLog(message);
        }

        public static void WritePerformanceLog(string operation, TimeSpan duration)
        {
            string message = $"性能统计 - {operation}: {duration.TotalMilliseconds:F2}ms";
            WriteDebugLog(message);
        }
    }
}
