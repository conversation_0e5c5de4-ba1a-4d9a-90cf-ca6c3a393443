# TensorFlow CPU-only configuration
# This script forces Tensor<PERSON><PERSON> to use CPU only
import os
import sys

# Set environment variables before importing tensorflow
os.environ['CUDA_VISIBLE_DEVICES'] = '-1'
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
os.environ['TF_FORCE_GPU_ALLOW_GROWTH'] = 'false'

print("TensorFlow configured for CPU-only mode")
print("CUDA_VISIBLE_DEVICES =", os.environ.get('CUDA_VISIBLE_DEVICES', 'not set'))

try:
    import tensorflow as tf
    print("TensorFlow version:", tf.__version__)
    print("Available devices:", tf.config.list_physical_devices())
    print("GPU available:", tf.config.list_physical_devices('GPU'))
    print("CPU available:", tf.config.list_physical_devices('CPU'))
except ImportError:
    print("TensorFlow not available in Python environment")
except Exception as e:
    print("Error:", str(e))
