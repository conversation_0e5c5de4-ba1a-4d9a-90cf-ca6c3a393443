[GPU设置]
# GPU内存管理配置文件
# 如果您的显卡内存不足导致尾巴信息无法绘制，请修改以下设置

# 强制使用CPU模式 (true/false)
# 设置为true将完全禁用GPU，使用CPU进行计算
# 优点：兼容性好，不会出现内存不足问题
# 缺点：计算速度较慢
# MX450显卡推荐：先尝试false，如果出现内存错误再改为true
ForceUseCPU=false

# GPU内存优化模式 (true/false)
# 设置为true将启用GPU内存优化，限制内存使用
# 适合显存较小的显卡（如GTX1050、GTX1060、MX450等）
# MX450显卡推荐：true（必须启用）
GPUMemoryOptimized=true

# GPU内存限制 (MB)
# 限制TensorFlow使用的GPU内存大小
# 建议设置为显卡总内存的70-80%
# MX450 (2GB显存) 推荐设置：1400-1536MB
GPUMemoryLimit=1400

# 尾巴处理功能 (true/false)
# 如果经常出现内存不足，可以临时禁用尾巴处理
# 注意：禁用后将无法显示精子尾巴信息
EnableTailProcessing=true

# 自动检测GPU配置 (true/false)
# 启用后程序会尝试自动检测GPU并配置合适的参数
AutoDetectGPU=true

[故障排除]
# 如果遇到以下错误，请尝试对应的解决方案：
# 
# 1. "OOM when allocating tensor" 错误：
#    - 设置 ForceUseCPU=true 或
#    - 设置 GPUMemoryOptimized=true 并降低 GPUMemoryLimit 值
#
# 2. 尾巴信息不显示：
#    - 检查 EnableTailProcessing=true
#    - 尝试设置 ForceUseCPU=true
#
# 3. 程序运行缓慢：
#    - 如果使用CPU模式，这是正常现象
#    - 可以尝试设置 ForceUseCPU=false 和 GPUMemoryOptimized=true
#
# 4. 不同显卡的推荐设置：
#    RTX2060 (6GB): ForceUseCPU=false, GPUMemoryLimit=4096
#    GTX1060 (6GB): ForceUseCPU=false, GPUMemoryLimit=4096
#    GTX1050 (2GB): ForceUseCPU=false, GPUMemoryLimit=1536
#    MX450 (2GB):   ForceUseCPU=false, GPUMemoryLimit=1400 (当前配置)
#    集成显卡:      ForceUseCPU=true
