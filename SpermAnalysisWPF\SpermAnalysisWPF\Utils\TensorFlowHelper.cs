using System;
using System.IO;
using System.Threading;

namespace SpermAnalysisWPF.Utils
{
    public static class TensorFlowHelper
    {
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();
        private static Exception _lastInitError = null;

        public static bool IsInitialized => _isInitialized;
        public static Exception LastInitError => _lastInitError;

        /// <summary>
        /// 尝试初始化TensorFlow模型
        /// </summary>
        /// <returns>是否初始化成功</returns>
        public static bool TryInitialize()
        {
            lock (_lockObject)
            {
                if (_isInitialized)
                    return true;

                try
                {
                    LogHelper.WriteInfoLog("开始初始化TensorFlow环境...");

                    // 检查必要的文件
                    if (!CheckRequiredFiles())
                    {
                        _lastInitError = new FileNotFoundException("缺少必要的TensorFlow文件");
                        return false;
                    }

                    // 尝试设置TensorFlow环境变量
                    SetTensorFlowEnvironment();

                    // 尝试预加载模型
                    if (TryPreloadModel())
                    {
                        _isInitialized = true;
                        LogHelper.WriteInfoLog("TensorFlow初始化成功");
                        return true;
                    }
                    else
                    {
                        LogHelper.WriteErrLog("TensorFlow模型预加载失败");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    _lastInitError = ex;
                    LogHelper.WriteErrLog($"TensorFlow初始化失败: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 检查必要的文件是否存在
        /// </summary>
        private static bool CheckRequiredFiles()
        {
            string currentDir = Environment.CurrentDirectory;
            string appDir = AppDomain.CurrentDomain.BaseDirectory;
            string assemblyDir = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? "";

            LogHelper.WriteInfoLog($"当前工作目录: {currentDir}");
            LogHelper.WriteInfoLog($"应用程序基目录: {appDir}");
            LogHelper.WriteInfoLog($"程序集目录: {assemblyDir}");

            // 尝试多个可能的目录
            string[] possibleDirs = { currentDir, appDir, assemblyDir };
            string workingDir = currentDir;

            // 找到包含dll文件的正确目录
            foreach (string dir in possibleDirs)
            {
                string testFile = Path.Combine(dir, "libtensorflow.dll");
                LogHelper.WriteInfoLog($"测试目录: {dir}, 检查文件: {testFile}");
                if (File.Exists(testFile))
                {
                    workingDir = dir;
                    LogHelper.WriteInfoLog($"找到正确的工作目录: {workingDir}");
                    break;
                }
            }

            // 检查TensorFlow DLL文件
            string[] requiredDlls = {
                "libtensorflow.dll",
                "TFRunModel.dll",
                "TFCommon.dll",
                "TensorFlowSharp.dll"
            };

            foreach (string dll in requiredDlls)
            {
                string dllPath = Path.Combine(workingDir, dll);
                LogHelper.WriteInfoLog($"检查文件: {dllPath}");
                if (!File.Exists(dllPath))
                {
                    LogHelper.WriteErrLog($"缺少必要文件: {dll} (完整路径: {dllPath})");
                    return false;
                }
                else
                {
                    LogHelper.WriteInfoLog($"文件存在: {dll}");
                }
            }

            // 检查模型文件
            string modelPath = Path.Combine(workingDir, "tfmodel", "frozen_inference_graph_mrf.pb");
            LogHelper.WriteInfoLog($"检查模型文件: {modelPath}");
            if (!File.Exists(modelPath))
            {
                LogHelper.WriteErrLog($"缺少模型文件: {modelPath}");
                return false;
            }
            else
            {
                LogHelper.WriteInfoLog("模型文件存在");
            }

            // 检查配置文件
            string configPath = Path.Combine(workingDir, "configure", "manager.config");
            LogHelper.WriteInfoLog($"检查配置文件: {configPath}");
            if (!File.Exists(configPath))
            {
                LogHelper.WriteErrLog($"缺少配置文件: {configPath}");
                return false;
            }
            else
            {
                LogHelper.WriteInfoLog("配置文件存在");
            }

            // 如果工作目录不是当前目录，则设置当前目录
            if (workingDir != currentDir)
            {
                LogHelper.WriteInfoLog($"设置工作目录从 {currentDir} 到 {workingDir}");
                Environment.CurrentDirectory = workingDir;
            }

            LogHelper.WriteInfoLog("所有必要文件检查通过");
            return true;
        }

        /// <summary>
        /// 设置TensorFlow环境变量
        /// </summary>
        private static void SetTensorFlowEnvironment()
        {
            try
            {
                string currentDir = Environment.CurrentDirectory;
                
                // 设置PATH环境变量
                string path = Environment.GetEnvironmentVariable("PATH") ?? "";
                if (!path.Contains(currentDir))
                {
                    Environment.SetEnvironmentVariable("PATH", $"{currentDir};{path}");
                    LogHelper.WriteInfoLog("已设置PATH环境变量");
                }

                // 设置TensorFlow相关环境变量
                Environment.SetEnvironmentVariable("TF_CPP_MIN_LOG_LEVEL", "2"); // 减少日志输出
                Environment.SetEnvironmentVariable("CUDA_VISIBLE_DEVICES", ""); // 禁用GPU（如果有问题）
                Environment.SetEnvironmentVariable("TF_FORCE_GPU_ALLOW_GROWTH", "true"); // 允许GPU内存增长
                Environment.SetEnvironmentVariable("TF_GPU_MEMORY_LIMIT", "1024"); // 限制GPU内存使用为1GB
                
                LogHelper.WriteInfoLog("TensorFlow环境变量设置完成");
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"设置环境变量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 尝试预加载模型
        /// </summary>
        private static bool TryPreloadModel()
        {
            try
            {
                // 使用样本图像进行预加载
                string sampleImagePath = Path.Combine(Environment.CurrentDirectory, "images", "sample", "1.jpg");
                
                if (!File.Exists(sampleImagePath))
                {
                    LogHelper.WriteErrLog($"样本图像不存在: {sampleImagePath}");
                    return false;
                }

                LogHelper.WriteInfoLog("开始预加载TensorFlow模型...");

                // 尝试运行一次模型
                var results = TFRunModel.TFRunMrf.RunModelOnce(sampleImagePath);
                
                LogHelper.WriteInfoLog($"模型预加载成功，检测到 {results?.Count ?? 0} 个目标");
                return true;
            }
            catch (Exception ex)
            {
                _lastInitError = ex;
                LogHelper.WriteErrLog($"模型预加载失败: {ex.Message}");
                LogHelper.WriteErrLog($"详细错误: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// 安全地运行TensorFlow模型
        /// </summary>
        public static System.Collections.Generic.List<TFRunModel.common.dectResult> SafeRunModel(string imagePath)
        {
            try
            {
                // 如果未初始化，先尝试初始化
                if (!_isInitialized && !TryInitialize())
                {
                    LogHelper.WriteErrLog("TensorFlow未初始化，无法运行模型");
                    return new System.Collections.Generic.List<TFRunModel.common.dectResult>();
                }

                // 运行模型
                LogHelper.WriteInfoLog($"运行TensorFlow模型: {Path.GetFileName(imagePath)}");
                var results = TFRunModel.TFRunMrf.RunModelOnce(imagePath);
                LogHelper.WriteInfoLog($"模型运行完成，检测到 {results?.Count ?? 0} 个目标");
                
                return results ?? new System.Collections.Generic.List<TFRunModel.common.dectResult>();
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"TensorFlow模型运行失败: {ex.Message}");
                LogHelper.WriteErrLog($"错误详情: {ex.StackTrace}");
                return new System.Collections.Generic.List<TFRunModel.common.dectResult>();
            }
        }

        /// <summary>
        /// 重置初始化状态（用于重试）
        /// </summary>
        public static void Reset()
        {
            lock (_lockObject)
            {
                _isInitialized = false;
                _lastInitError = null;
                LogHelper.WriteInfoLog("TensorFlow状态已重置");
            }
        }

        /// <summary>
        /// 获取初始化状态信息
        /// </summary>
        public static string GetStatusInfo()
        {
            if (_isInitialized)
            {
                return "TensorFlow已初始化并准备就绪";
            }
            else if (_lastInitError != null)
            {
                return $"TensorFlow初始化失败: {_lastInitError.Message}";
            }
            else
            {
                return "TensorFlow尚未初始化";
            }
        }
    }
}
