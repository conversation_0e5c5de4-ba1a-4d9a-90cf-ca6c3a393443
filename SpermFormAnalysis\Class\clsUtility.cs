﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace SpermFormAnalysis.Class
{
    public static class clsUtility
    {
        public static string key = "A3A4B73496E9473D";


        public static string TO32MD5(string srcstr)
        {
            MD5 md5 = MD5.Create();
            string md5str = "";//加密后的string
            byte[] s = md5.ComputeHash(Encoding.UTF8.GetBytes(srcstr));
            for (int i = 0; i < s.Length; i++)
            {
                string btos = s[i].ToString("X");
                if (btos.Length == 1)
                {
                    //每次转换得到的都是2位
                    btos = "0" + btos;
                }
                md5str = md5str + btos;//转换成十六进制
            }
            return md5str;

        }
        public static List<string> getMacAddress()
        {
            List<string> list = new List<string>();
            try
            {
                ManagementClass managementClass = new ManagementClass("Win32_NetworkAdapterConfiguration");
                ManagementObjectCollection instances = managementClass.GetInstances();
                foreach (ManagementObject item in instances)
                {
                    if ((bool)item["IPEnabled"])
                    {
                        list.Add(item["MacAddress"].ToString());
                    }
                }
                instances = null;
                managementClass = null;
            }
            catch
            {
            }
            return list;
        }

        public static string GetDiskID()
        {
            try
            {
                string result = string.Empty;
                ManagementClass managementClass = new ManagementClass("Win32_DiskDrive");
                ManagementObjectCollection instances = managementClass.GetInstances();
                foreach (ManagementObject item in instances)
                {
                    result = item.Properties["Model"].Value.ToString();
                }
                instances = null;
                managementClass = null;
                return result;
            }
            catch
            {
                return "unknown";
            }
        }

        public static string GetCpuID()
        {
            try
            {
                string result = string.Empty;
                ManagementClass managementClass = new ManagementClass("Win32_Processor");
                ManagementObjectCollection instances = managementClass.GetInstances();
                foreach (ManagementObject item in instances)
                {
                    result = item.Properties["ProcessorId"].Value.ToString();
                }
                instances = null;
                managementClass = null;
                return result;
            }
            catch
            {
                return "unknown";
            }
        }

        public static List<string> GetDiskIDs()
        {
            List<string> list = new List<string>();
            ManagementObjectSearcher managementObjectSearcher = new ManagementObjectSearcher("SELECT * FROM Win32_LogicalDiskToPartition");
            foreach (ManagementObject item in managementObjectSearcher.Get())
            {
                getValueInQuotes(item["Dependent"].ToString());
                string[] array = getValueInQuotes(item["Antecedent"].ToString()).Split(',');
                string str = array[0].Remove(0, 6).Trim();
                ManagementObjectSearcher managementObjectSearcher2 = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive");
                foreach (ManagementObject item2 in managementObjectSearcher2.Get())
                {
                    if (item2["Name"].ToString() == "\\\\.\\PHYSICALDRIVE" + str & item2["InterfaceType"].ToString() == "USB")
                    {
                        list.Add(parseSerialFromDeviceID(item2["PNPDeviceID"].ToString()));
                    }
                }
            }
            return list;
        }

        private static string parseSerialFromDeviceID(string deviceId)
        {
            string[] array = deviceId.Split('\\');
            int num = array.Length - 1;
            string[] array2 = array[num].Split('&');
            return array2[0];
        }

        private static string getValueInQuotes(string inValue)
        {
            int num = inValue.IndexOf("\"");
            int num2 = inValue.IndexOf("\"", num + 1);
            return inValue.Substring(num + 1, num2 - num - 1);
        }

        public static string getMD5Encrypt(string strText)
        {
            string text = "";
            MD5 mD = new MD5CryptoServiceProvider();
            byte[] array = mD.ComputeHash(Encoding.Default.GetBytes(strText));
            byte[] array2 = array;
            foreach (byte b in array2)
            {
                text += b;
            }
            return text;
        }

        public static bool validComputer()
        {
            string key = "";
            bool result = false;
            List<string> macAddress = getMacAddress();
            new List<string>();
            foreach (string item in macAddress)
            {
                string mD5Encrypt = getMD5Encrypt(item + "cellpro");
                if (mD5Encrypt == key)
                {
                    return true;
                }
            }
            return result;
        }

        public static bool validComputerByDiskCPU()
        {
            string key = "";
            string cpuID = GetCpuID();
            bool result = false;
            List<string> diskIDs = GetDiskIDs();
            foreach (string item in diskIDs)
            {
                string mD5Encrypt = getMD5Encrypt(cpuID + item + "cellpro");
                if (mD5Encrypt == key)
                {
                    return true;
                }
            }
            return result;
        }

        public static bool validTime()
        {
            bool result = false;
            DateTime now = DateTime.Now;
            DateTime t = Convert.ToDateTime("2021-9-1");
            if (now < t)
            {
                result = true;
            }
            return result;
        }

        public static string genKey()
        {
            List<string> macAddress = getMacAddress();
            new List<string>();
            string result = "";
            foreach (string item in macAddress)
            {
                if (item != "")
                {
                    result = getMD5Encrypt(item + "<EMAIL>");
                }
            }
            return result;
        }

        public static string genKeyByDisk()
        {
            string diskID = GetDiskID();
            return getMD5Encrypt(diskID + "<EMAIL>");
        }

        public static List<string> genKeyByDisks()
        {
            List<string> diskIDs = GetDiskIDs();
            string cpuID = GetCpuID();
            List<string> list = new List<string>();
            foreach (string item in diskIDs)
            {
                list.Add(getMD5Encrypt(cpuID + item + "<EMAIL>"));
            }
            return list;
        }
    }
}
