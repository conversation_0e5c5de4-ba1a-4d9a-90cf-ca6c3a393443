using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SpermAnalysisWPF.Model
{
    [Table("Mrfrules")]
    public class Mrfrules
    {
        [Key]
        public int id { get; set; }

        public string Usefield { get; set; } = string.Empty;

        public double Min_value { get; set; }

        public double Max_value { get; set; }

        public string Description { get; set; } = string.Empty;

        public int IsActive { get; set; }
    }
}
