﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.Model
{
    public class Sampleinfo
    {
        public int Id { get; set; }

        public string sampleId { get; set; }

        public string patientId { get; set; }

        public string patientName { get; set; }

        public int patientage { get; set; }

        public string inspectItem { get; set; }

        public string getSpermWay { get; set; }

        public int abstinenceDays { get; set; }

        public double dilutionRatio { get; set; }

        public string sampleSource { get; set; }

        public string department { get; set; }

        /// <summary>
        /// 就诊医生
        /// </summary>
        public string inspectionDoctor { get; set; }

        public string sendtime { get; set; }

        public string sendDoctor { get; set; }

        public string scanmode { get; set; }

        public int scanned { get; set; }

        public string scantime { get; set; }

        public int processed { get; set; }

        public string processed_time { get; set; }

        public int reviewed { get; set; }

        public string reviewer { get; set; }


        public string review_time { get; set; }

        public int processed_np { get; set; }

        public string processed_np_time { get; set; }

        public int reviewed_np { get; set; }

        public string reviewer_np { get; set; }

        public string review_np_time { get; set; }

        public int reportprinted { get; set; }

        public string reporttime { get; set; }

        public string reportcontent { get; set; }

        public string diagnosis { get; set; }

        public int field_num { get; set; }

        public int sperm_num { get; set; }

        public string purpose { get; set; }

        public double qualification_rate { get; set; }

        public int normal_num { get; set; }

        public int abnormal_num { get; set; }

        public int microhead_num { get; set; }

        public int macrohead_num { get; set; }

        public int normalhead_num { get; set; }

        public int tallhead_num { get; set; }

        public int shorthead_num { get; set; }

        public int normalheight_num { get; set; }

        public int fathead_num { get; set; }

        public int thinhead_num { get; set; }

        public int normalwidth_num { get; set; }

        public int sharphead_num { get; set; }

        public int roundhead_num { get; set; }

        public int normalratio_num { get; set; }

        public int bigacrosome_num { get; set; }

        public int smallacrosome_num { get; set; }

        public int normalacrosome_num { get; set; }

        public int normalvacuoles_num { get; set; }

        public int abnormalvacuoles_num { get; set; }

        public int _0vacuoles_num { get; set; }

        public int _12vacuoles_num { get; set; }

        public int _3plusvacuoles_num { get; set; }

        public int kernelvacuoles_num { get; set; }

        public int bigvacuoles_num { get; set; }

        public int normalmiddlepiece_num { get; set; }

        public int abnormalmiddlepiece_num { get; set; }

        public int normalmiddlepieceangle_num { get; set; }

        public int abnormalmiddlepieceangle_num { get; set; }

        public int normalmiddlepiecewidth_num { get; set; }

        public int abnormalmiddlepiecewidth_num { get; set; }

        public int normalshape_num { get; set; }

        public int abnormalshape_num { get; set; }

        public int irregularshape_num { get; set; }

        public int pyriformconeshape_num { get; set; }

        public int asymmetryshape_num { get; set; }

        public int userid { get; set; }

        public int normalkernel_num { get; set; }

        public int noacrosome_num { get; set; }

        public int subclinical_num { get; set; }

        public int lheadlacro_num { get; set; }


        public int rheadnacro_num { get; set; }

        public int rheadlacro_num { get; set; }

        public int theadlacro_num { get; set; }

        public int pheadbacro_num { get; set; }

        public int normalTail_num { get; set; }

        public int abnormalTail_num { get; set; }

        public int ERC_num { get; set; }

        public int notail_num { get; set; }

        public int shorttail_num { get; set; }

        public int curvetail_num { get; set; }

        public int bendtail_num { get; set; }

        public string checkcode { get; set; }




        //无数据库字段
        public string inspectItemCHN { get; set; }

        public double abnormal_rate { get; set; }

        public double subclinical_rate { get; set; }

        public double totalPercent { get; set; }

        public int abnormalkernel_num { get; set; }

        public int abnormalacrosome_num { get; set; }

        public double tallhead_rate { get; set; }

        public double shorthead_rate { get; set; }

        public double normalheight_rate { get; set; }

        public double fathead_rate { get; set; }

        public double thinhead_rate { get; set; }

        public double normalwidth_rate { get; set; }

        public double roundhead_rate { get; set; }

        public double normalratio_rate { get; set; }

        public double normalvacuoles_rate { get; set; }

        public double _0vacuoles_rate { get; set; }

        public double _12vacuoles_rate { get; set; }

        public double _3plusvacuoles_rate { get; set; }

        public double kernelvacuoles_rate { get; set; }

        public double bigvacuoles_rate { get; set; }

        public double normalmiddlepiece_rate { get; set; }

        public double abnormalmiddlepiece_rate { get; set; }

        public double normalmiddlepieceangle_rate { get; set; }

        public double abnormalmiddlepieceangle_rate { get; set; }

        public double normalmiddlepiecewidth_rate { get; set; }

        public double abnormalmiddlepiecewidth_rate { get; set; }

        public double abnormalshape_rate { get; set; }

        public double asymmetryshape_rate { get; set; }

        public double normalshape_rate { get; set; }

        public double normalhead_rate { get; set; }

        public double microhead_rate { get; set; }

        public double macrohead_rate { get; set; }

        public double sharphead_rate { get; set; }

        public double pyriformconeshape_rate { get; set; }

        public double irregularshape_rate { get; set; }

        public double noacrosome_rate { get; set; }

        public double bigacrosome_rate { get; set; }

        public double smallacrosome_rate { get; set; }

        public double normalacrosome_rate { get; set; }

        public double abnormalvacuoles_rate { get; set; }

        public double lheadlacro_rate { get; set; }

        public double rheadlacro_rate { get; set; }

        public double rheadnacro_rate { get; set; }

        public double theadlacro_rate { get; set; }

        public double pheadbacro_rate { get; set; }
    }
}
