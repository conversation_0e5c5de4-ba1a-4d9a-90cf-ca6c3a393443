﻿namespace SpermFormAnalysis.PageForm
{
    partial class Authorizes
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.ucGroupHead1 = new SunnyUI.Library.UCGroupHead();
            this.ucGroupHead2 = new SunnyUI.Library.UCGroupHead();
            this.btn = new Sunny.UI.UISymbolButton();
            this.label1 = new System.Windows.Forms.Label();
            this.AuthorizeBox = new Sunny.UI.UITextBox();
            this.btn1 = new Sunny.UI.UISymbolButton();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.dis = new Sunny.UI.UITextBox();
            this.ucGroupHead3 = new SunnyUI.Library.UCGroupHead();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.uiComboBox1 = new Sunny.UI.UIComboBox();
            this.SuspendLayout();
            // 
            // ucGroupHead1
            // 
            this.ucGroupHead1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ucGroupHead1.HeadColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(144)))), ((int)(((byte)(255)))));
            this.ucGroupHead1.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(233)))), ((int)(((byte)(252)))));
            this.ucGroupHead1.Location = new System.Drawing.Point(12, 8);
            this.ucGroupHead1.Name = "ucGroupHead1";
            this.ucGroupHead1.Size = new System.Drawing.Size(776, 40);
            this.ucGroupHead1.TabIndex = 0;
            this.ucGroupHead1.Text = "注册授权";
            this.ucGroupHead1.TextColor = System.Drawing.Color.Black;
            // 
            // ucGroupHead2
            // 
            this.ucGroupHead2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ucGroupHead2.HeadColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(144)))), ((int)(((byte)(255)))));
            this.ucGroupHead2.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(233)))), ((int)(((byte)(252)))));
            this.ucGroupHead2.Location = new System.Drawing.Point(12, 264);
            this.ucGroupHead2.Name = "ucGroupHead2";
            this.ucGroupHead2.Size = new System.Drawing.Size(776, 40);
            this.ucGroupHead2.TabIndex = 1;
            this.ucGroupHead2.Text = "卡次查询";
            this.ucGroupHead2.TextColor = System.Drawing.Color.Black;
            // 
            // btn
            // 
            this.btn.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btn.Location = new System.Drawing.Point(526, 50);
            this.btn.MinimumSize = new System.Drawing.Size(1, 1);
            this.btn.Name = "btn";
            this.btn.Radius = 35;
            this.btn.Size = new System.Drawing.Size(103, 38);
            this.btn.Style = Sunny.UI.UIStyle.Custom;
            this.btn.Symbol = 0;
            this.btn.TabIndex = 53;
            this.btn.Text = "确定";
            this.btn.Click += new System.EventHandler(this.btn_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(34, 67);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(74, 21);
            this.label1.TabIndex = 52;
            this.label1.Text = "授权码：";
            // 
            // AuthorizeBox
            // 
            this.AuthorizeBox.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.AuthorizeBox.FillColor = System.Drawing.Color.White;
            this.AuthorizeBox.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.AuthorizeBox.Location = new System.Drawing.Point(128, 56);
            this.AuthorizeBox.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.AuthorizeBox.Maximum = 2147483647D;
            this.AuthorizeBox.Minimum = -2147483648D;
            this.AuthorizeBox.MinimumSize = new System.Drawing.Size(1, 1);
            this.AuthorizeBox.Name = "AuthorizeBox";
            this.AuthorizeBox.Padding = new System.Windows.Forms.Padding(5);
            this.AuthorizeBox.Size = new System.Drawing.Size(366, 29);
            this.AuthorizeBox.Style = Sunny.UI.UIStyle.Custom;
            this.AuthorizeBox.TabIndex = 51;
            // 
            // btn1
            // 
            this.btn1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.btn1.Location = new System.Drawing.Point(526, 360);
            this.btn1.MinimumSize = new System.Drawing.Size(1, 1);
            this.btn1.Name = "btn1";
            this.btn1.Radius = 35;
            this.btn1.Size = new System.Drawing.Size(103, 35);
            this.btn1.Style = Sunny.UI.UIStyle.Custom;
            this.btn1.Symbol = 0;
            this.btn1.TabIndex = 60;
            this.btn1.Text = "查看";
            this.btn1.Click += new System.EventHandler(this.btn1_Click);
            // 
            // uiLabel2
            // 
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel2.Location = new System.Drawing.Point(34, 372);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(90, 23);
            this.uiLabel2.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel2.TabIndex = 59;
            this.uiLabel2.Text = "剩余次数：";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel1
            // 
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel1.Location = new System.Drawing.Point(34, 318);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(386, 23);
            this.uiLabel1.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel1.TabIndex = 57;
            this.uiLabel1.Text = "请连接读卡器，放上授权卡，点击查看次数";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // dis
            // 
            this.dis.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.dis.FillColor = System.Drawing.Color.White;
            this.dis.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dis.Location = new System.Drawing.Point(143, 366);
            this.dis.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.dis.Maximum = 2147483647D;
            this.dis.Minimum = -2147483648D;
            this.dis.MinimumSize = new System.Drawing.Size(1, 1);
            this.dis.Name = "dis";
            this.dis.Padding = new System.Windows.Forms.Padding(5);
            this.dis.Size = new System.Drawing.Size(366, 29);
            this.dis.Style = Sunny.UI.UIStyle.Custom;
            this.dis.TabIndex = 61;
            // 
            // ucGroupHead3
            // 
            this.ucGroupHead3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.ucGroupHead3.HeadColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(144)))), ((int)(((byte)(255)))));
            this.ucGroupHead3.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(233)))), ((int)(((byte)(252)))));
            this.ucGroupHead3.Location = new System.Drawing.Point(12, 112);
            this.ucGroupHead3.Name = "ucGroupHead3";
            this.ucGroupHead3.Size = new System.Drawing.Size(776, 40);
            this.ucGroupHead3.TabIndex = 2;
            this.ucGroupHead3.Text = "读卡器切换";
            this.ucGroupHead3.TextColor = System.Drawing.Color.Black;
            // 
            // uiLabel3
            // 
            this.uiLabel3.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel3.Location = new System.Drawing.Point(34, 168);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(386, 23);
            this.uiLabel3.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel3.TabIndex = 62;
            this.uiLabel3.Text = "可以查看读卡器背面的型号进行选择";
            this.uiLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiLabel4
            // 
            this.uiLabel4.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiLabel4.Location = new System.Drawing.Point(34, 224);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(112, 23);
            this.uiLabel4.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel4.TabIndex = 63;
            this.uiLabel4.Text = "读卡器种类：";
            this.uiLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiComboBox1
            // 
            this.uiComboBox1.FillColor = System.Drawing.Color.White;
            this.uiComboBox1.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uiComboBox1.Items.AddRange(new object[] {
            "CC-LB",
            "X3读卡器"});
            this.uiComboBox1.Location = new System.Drawing.Point(143, 218);
            this.uiComboBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiComboBox1.MinimumSize = new System.Drawing.Size(63, 0);
            this.uiComboBox1.Name = "uiComboBox1";
            this.uiComboBox1.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.uiComboBox1.Size = new System.Drawing.Size(351, 29);
            this.uiComboBox1.Style = Sunny.UI.UIStyle.Custom;
            this.uiComboBox1.TabIndex = 66;
            this.uiComboBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.uiComboBox1.SelectedIndexChanged += new System.EventHandler(this.uiComboBox1_SelectedIndexChanged);
            // 
            // Authorizes
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 21F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(800, 572);
            this.Controls.Add(this.uiComboBox1);
            this.Controls.Add(this.uiLabel4);
            this.Controls.Add(this.uiLabel3);
            this.Controls.Add(this.ucGroupHead3);
            this.Controls.Add(this.dis);
            this.Controls.Add(this.btn1);
            this.Controls.Add(this.uiLabel2);
            this.Controls.Add(this.uiLabel1);
            this.Controls.Add(this.btn);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.AuthorizeBox);
            this.Controls.Add(this.ucGroupHead2);
            this.Controls.Add(this.ucGroupHead1);
            this.Name = "Authorizes";
            this.Style = Sunny.UI.UIStyle.Custom;
            this.Text = "Authorizes";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private SunnyUI.Library.UCGroupHead ucGroupHead1;
        private SunnyUI.Library.UCGroupHead ucGroupHead2;
        private Sunny.UI.UISymbolButton btn;
        private System.Windows.Forms.Label label1;
        private Sunny.UI.UITextBox AuthorizeBox;
        private Sunny.UI.UISymbolButton btn1;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UITextBox dis;
        private SunnyUI.Library.UCGroupHead ucGroupHead3;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UIComboBox uiComboBox1;
    }
}