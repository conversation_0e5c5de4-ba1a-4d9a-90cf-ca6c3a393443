2025-08-03 11:55:50,INFO: 系统初始化完成
2025-08-03 11:55:50,INFO: 开始TensorFlow预加载...
2025-08-03 11:55:50,INFO: 开始初始化TensorFlow环境...
2025-08-03 11:55:50,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 11:55:50,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 11:55:50,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 11:55:50,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 11:55:50,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 11:55:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 11:55:50,INFO: 文件存在: libtensorflow.dll
2025-08-03 11:55:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 11:55:50,INFO: 文件存在: TFRunModel.dll
2025-08-03 11:55:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 11:55:50,INFO: 文件存在: TFCommon.dll
2025-08-03 11:55:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 11:55:50,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 11:55:50,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 11:55:50,INFO: 模型文件存在
2025-08-03 11:55:50,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 11:55:50,INFO: 配置文件存在
2025-08-03 11:55:50,INFO: 所有必要文件检查通过
2025-08-03 11:55:50,INFO: 已设置PATH环境变量
2025-08-03 11:55:50,INFO: TensorFlow环境变量设置完成
2025-08-03 11:55:50,INFO: 开始预加载TensorFlow模型...
2025-08-03 11:55:55,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 11:55:55,DEBUG: 已清除所有绘制痕迹
2025-08-03 11:55:55,INFO: 图像加载成功: 1920x1200
2025-08-03 11:55:55,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 11:55:56,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 11:55:56,INFO: 开始TensorFlow模型检测...
2025-08-03 11:55:56,DEBUG: SpermAnalysisService.AnalyzeImage 被调用: D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\bin\x64\Debug\images\20240411\20240411-1\2024_04_11_14_45_14_087.jpg
2025-08-03 11:56:01,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 11:56:01,INFO: TensorFlow初始化成功
2025-08-03 11:56:01,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 11:56:01,INFO: TensorFlow预加载成功
2025-08-03 11:56:02,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 11:56:03,DEBUG: 开始处理尾巴数据，精子数量: 11
2025-08-03 11:56:03,DEBUG: 生成尾巴数据: 精子1
2025-08-03 11:56:03,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 11:56:03,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 11:56:03,DEBUG: 尾巴数据处理完成
2025-08-03 11:56:03,DEBUG: 性能统计 - 图像分析: 6777.54ms
2025-08-03 11:56:03,INFO: 分析完成，检测到 11 个精子
2025-08-03 11:56:03,INFO: 开始在图像上标记分析结果...
2025-08-03 11:56:03,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 11:56:03,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 11:56:03,DEBUG: 解析轮廓: 215个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 161个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 144个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 11:56:03,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5' (长度:79)
2025-08-03 11:56:03,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5' (长度:151)
2025-08-03 11:56:03,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 11:56:03,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 221个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 66个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 198个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 213个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 138个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 164个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 251个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 191个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 154个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 180个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 164个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 124个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 205个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 178个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 141个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 200个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 137个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 146个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 242个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 136个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 188个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 228个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 179个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 159个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 254个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 159个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 189个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 解析轮廓: 284个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 解析轮廓: 284个点
2025-08-03 11:56:03,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,DEBUG: 尾巴解析结果: 0个点
2025-08-03 11:56:03,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 11:56:03,INFO: 图像标记完成
2025-08-03 11:56:03,INFO: 保存分析结果到数据库...
2025-08-03 11:56:03,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 11:56:07,INFO: 用户请求检查TensorFlow状态
2025-08-03 11:56:11,INFO: 用户清空了日志显示
2025-08-03 11:56:13,INFO: 用户请求检查TensorFlow状态
