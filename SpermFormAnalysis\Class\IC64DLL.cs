﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

namespace SpermFormAnalysis.Class
{
    internal class IC64DLL
    {
        /// <summary>
        /// 初始化读卡器
        /// </summary>
        /// <param name="port">通讯口号(0～3)</param>
        /// <param name="baud">通讯波特率(9600～115200)</param>
        /// <returns>返回设备描述符</returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_init", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern int rf_init(Int16 port, int baud);
        /// <summary>
        /// 读写器的版本号
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="state">读写器版本信息</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_get_status", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_get_status(int icdev, [MarshalAs(UnmanagedType.LPArray)] byte[] state);
        /// <summary>
        /// 获取API版本号
        /// </summary>
        /// <param name="ver">API版本号</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "lib_ver", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_lib_ver([MarshalAs(UnmanagedType.LPArray)] byte[] ver);
        /// <summary>
        /// 射频头复位
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="_Msec">复位时间，0～500毫秒有效</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_reset", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_reset(int icdev, Int16 _Msec);
        /// <summary>
        /// 卡片发出寻卡
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="mode">0 IDLE mode(只检测IDLE状态)  1 ALL mode(全部状态[HALT状态也检测])</param>
        /// <param name="snr">返回卡片类型</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_request", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_request(int icdev, int mode, out uint snr);
        /// <summary>
        /// 卡片发出寻卡
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="mode">0 IDLE mode(只检测IDLE状态)  1 ALL mode(全部状态[HALT状态也检测])</param>
        /// <param name="snr">返回卡片类型</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_anticoll", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_anticoll(int icdev, int mode, out uint snr);
        /// <summary>
        /// 卡片发出寻卡
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="mode">0 IDLE mode(只检测IDLE状态)  1 ALL mode(全部状态[HALT状态也检测])</param>
        /// <param name="tagtype">返回卡片类型</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_select", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_select(int icdev, uint _Snr, out byte _Size);
        /// <summary>
        /// 寻卡高级函数
        /// 完成低级函数 rf_request, rf_anticoll 和 rf_select 的功能
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="mode">0 IDLE mode(只检测IDLE状态)  1 ALL mode(全部状态[HALT状态也检测])</param>
        /// <param name="tagtype">返回卡片类型</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_card", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_card(int icdev, string mode, out long _Snr);
        /// <summary>
        /// ASCII 字符转换为 16 进制数
        /// </summary>
        /// <param name="asc">ASCII 字符</param>
        /// <param name="hex">输出的 16 进制数</param>
        /// <param name="len">ASCII 字符的长度</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "a_hex", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 a_hex([MarshalAs(UnmanagedType.LPArray)] byte[] asc, [MarshalAs(UnmanagedType.LPArray)] byte[] hex, int len);

        /// <summary>
        /// 16 进制数转换为 ASCII 字符
        /// </summary>
        /// <param name="hex">ASCII 字符</param>
        /// <param name="asc">输出的 16 进制数</param>
        /// <param name="len">ASCII 字符的长度</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "hex_a", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 hex_a([MarshalAs(UnmanagedType.LPArray)] byte[] hex, [MarshalAs(UnmanagedType.LPArray)] byte[] asc, int len);
        /// <summary>
        /// 核对密码
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="_Mode">验证密码类型</param>
        /// <param name="secnr">扇区</param>
        /// <param name="keybuff">密码</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_authenticate_key", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_authentication_pass(int icdev, int _Mode, int secnr, [MarshalAs(UnmanagedType.LPArray)] byte[] keybuff);
        /// <summary>
        /// 核对密码
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="_Mode">验证密码类型</param>
        /// <param name="secnr">扇区</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_authentication", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_authentication(int icdev, int _Mode, int secnr);
        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="_Mode">验证密码类型</param>
        /// <param name="secnr">扇区</param>
        /// <param name="keybuff">密码</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_load_key", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_load_key(int icdev, int _Mode, int secnr, [MarshalAs(UnmanagedType.LPArray)] byte[] keybuff);

        /// <summary>
        /// 16 进制数转换为 ASCII 字符
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="blocknr">阅读行数</param>
        /// <param name="databuff">输出数据</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_read", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_read(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);
        /// <summary>
        /// 16 进制数转换为 ASCII 字符
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="blocknr">阅读行数</param>
        /// <param name="databuff">输出数据</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_write", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_write(int icdev, int blocknr, [MarshalAs(UnmanagedType.LPArray)] byte[] databuff);

        /// <summary>
        /// 关闭读卡器
        /// </summary>
        /// <param name="icdev">返回的设备描述符</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_exit", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        public static extern Int16 rf_exit(int icdev);
        /// <summary>
        /// 蜂鸣
        /// </summary>
        /// <param name="icdev">设备描述符</param>
        /// <param name="_Msec">蜂鸣时间，单位：毫秒</param>
        /// <returns></returns>
        [DllImport("qtrf64.dll", EntryPoint = "rf_beep", SetLastError = true, CharSet = CharSet.Auto, ExactSpelling = false, CallingConvention = CallingConvention.StdCall)]
        //说明：初始化通讯接口
        public static extern Int16 rf_beep(int icdev, int _Msec);
    }
}
