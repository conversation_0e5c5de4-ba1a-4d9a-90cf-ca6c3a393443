﻿using SpermFormAnalysis.Class;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.PageForm
{
    public partial class Setting : UIPage
    {
        public Setting()
        {
            InitializeComponent();
        }
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        private void btn3_Click(object sender, EventArgs e)
        {
            GlobalProperty.Hospital = this.hospital.Text;
            GlobalProperty.Ks = this.ks.Text;
            GlobalProperty.MaxPic = (int)maxPics.Value;
            GlobalProperty.MaxCellNum = (int)maxCellNums.Value;
            ShowSuccessNotifier("修改成功！");
        }

        private void Setting_Load(object sender, EventArgs e)
        {
            this.hospital.Text = GlobalProperty.Hospital;
            this.ks.Text = GlobalProperty.Ks;
            maxCellNums.Value = GlobalProperty.MaxCellNum;
            maxPics.Value = GlobalProperty.MaxPic;
        }
    }
}
