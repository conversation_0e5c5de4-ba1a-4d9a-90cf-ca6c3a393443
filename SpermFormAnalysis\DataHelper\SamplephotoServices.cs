﻿using MySql.Data.MySqlClient;
using SpermFormAnalysis.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Data.SqlClient;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.SQLite;

namespace SpermFormAnalysis.DataHelper
{
    public static class SamplephotoServices
    {
        private static string connectionString = @"Data Source=" + Application.StartupPath + @"\database\morsperm.db;Initial Catalog=sqlite;Integrated Security=True; =10";

        public static Samplephoto GetObject(int id)
        {
            Samplephoto obj = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from Samplephoto where id=@id", conn);
                cmd.Parameters.Add(new SqlParameter("@id", id));
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        obj = new Samplephoto()
                        {
                            abnormal_num = Convert.ToInt32(dr["abnormal_num"]),
                            block = Convert.ToInt32(dr["block"]),
                            format = dr["format"].ToString(),
                            id = Convert.ToInt32(dr["id"]),
                            imageSource = dr["imageSource"].ToString(),
                            marker = dr["marker"].ToString(),
                            normal_num = Convert.ToInt32(dr["normal_num"]),
                            number = Convert.ToInt32(dr["number"]),
                            processed = Convert.ToInt16(dr["processed"]),
                            sampleId = dr["sampleId"].ToString(),
                            sperm_num = Convert.ToInt32(dr["sperm_num"]),
                            type = dr[""].ToString(),
                        };
                    }
                }
                conn.Close();
            }
            return obj;
        }

        public static ObservableCollection<Samplephoto> GetMRFTaskList(string sampleId)
        {
            string cmdText = "select * from samplephoto where processed=0 and sampleId='" + sampleId + "' order by block asc";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Samplephoto> GetSamplePhoto(string sampleId)
        {
            string cmdText = "select * from samplephoto where sampleId='" + sampleId + "'";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Samplephoto> GetModelObjects(string cmdText)
        {
            ObservableCollection<Samplephoto> observableCollection = new ObservableCollection<Samplephoto>();
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        Samplephoto samplephoto = new Samplephoto();
                        samplephoto.id = Convert.ToInt32(dr["id"]);
                        samplephoto.sampleId = Convert.ToString(dr["sampleId"]);
                        samplephoto.type = Convert.ToString(dr["type"]);
                        samplephoto.number = Convert.ToInt32(dr["number"]);
                        samplephoto.block = Convert.ToInt32(dr["block"]);
                        samplephoto.format = Convert.ToString(dr["format"]);
                        samplephoto.imageSource = Convert.ToString(dr["imageSource"]);
                        samplephoto.marker = Convert.ToString(dr["marker"]);

                        bool flag2 = !Convert.IsDBNull(dr["processed"]);
                        if (flag2)
                        {
                            samplephoto.processed = Convert.ToInt16(dr["processed"]);
                        }

                        samplephoto.fieldName = samplephoto.imageSource.Substring(samplephoto.imageSource.LastIndexOf("\\") + 1);
                        string fullPath = Path.GetFullPath(samplephoto.imageSource);
                        string imagePath = fullPath.Substring(0, fullPath.LastIndexOf("\\")) + "\\thumbnail\\" + fullPath.Substring(1 + fullPath.LastIndexOf("\\")).Replace(".bmp", ".jpg");
                        //samplephoto.bitmapImageSource = getImage(imagePath);
                        observableCollection.Add(samplephoto);
                    }
                }
                conn.Close();
            }
            return observableCollection;
        }

        public static int GetMaxBlockNum(string sampleid)
        {
            string cmdText = "select max(block) from samplephoto where sampleId=@sampleId";
            int result;
            using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
            {
                using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                {
                    mySqlCommand.Parameters.Add(new SQLiteParameter
                    {
                        ParameterName = "@sampleId",
                        Value = sampleid,
                        DbType = DbType.String
                    });
                    mySqlConnection.Open();
                    object value = mySqlCommand.ExecuteScalar();
                    bool flag = Convert.IsDBNull(value);
                    if (flag)
                    {
                        result = -1;
                    }
                    else
                    {
                        result = Convert.ToInt32(value);
                    }
                }
            }
            return result;
        }


        public static bool UpdatePhotoProcessed(Samplephoto sp, int _processed)
        {
            bool flag = sp == null;
            bool result;
            if (flag)
            {
                result = false;
            }
            else
            {
                int num = 0;
                string cmdText = "update samplephoto set processed=@processed,sperm_num=(select count(id) from sperm where photoId=@photoId and valid=1),normal_num=(select count(id) from sperm where photoId=@photoId and isNormal=1 and valid=1) ,abnormal_num=(select count(id) from sperm where photoId=@photoId and isNormal=0 and valid=1) where sampleId=@sampleId and id=@id";
                try
                {
                    using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
                    {
                        using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                        {
                            mySqlCommand.Parameters.AddWithValue("@id", sp.id);
                            mySqlCommand.Parameters.AddWithValue("@photoId", sp.id);
                            mySqlCommand.Parameters.AddWithValue("@sampleId", sp.sampleId);
                            mySqlCommand.Parameters.AddWithValue("@processed", _processed);
                            mySqlConnection.Open();
                            num += mySqlCommand.ExecuteNonQuery();
                        }
                    }
                }
                catch
                {
                }
                bool flag2 = num == 0;
                result = !flag2;
            }
            return result;
        }


        public static bool AddPhoto(Samplephoto sp)
        {
            int num = 0;
            string cmdText = "insert into samplephoto (sampleId,type,number,block,format,imageSource,processed)values(@sampleId,@type,@number,@block,@format,@imageSource,0)";
            using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
            {
                using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                {
                    mySqlCommand.Parameters.AddWithValue("@sampleId", sp.sampleId);
                    mySqlCommand.Parameters.AddWithValue("@type", sp.type);
                    mySqlCommand.Parameters.AddWithValue("@number", sp.number);
                    mySqlCommand.Parameters.AddWithValue("@block", sp.block);
                    mySqlCommand.Parameters.AddWithValue("@format", sp.format);
                    mySqlCommand.Parameters.AddWithValue("@imageSource", sp.imageSource);
                    mySqlConnection.Open();
                    try
                    {
                        num += mySqlCommand.ExecuteNonQuery();
                    }
                    catch
                    {
                    }
                }
            }
            bool flag = num == 0;
            return !flag;
        }

        public static void DeletePhotos(string sampleId)
        {
            //using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            //{
            //    SQLiteCommand cmd = new SQLiteCommand("delete from Samplephoto where sampleId='"+sampleId+"'");
            //    cmd.CommandTimeout = 60;
            //    conn.Open();
            //    cmd.ExecuteNonQuery();
            //    conn.Close();
            //}
        }

        //private static BitmapImage getImage(string imagePath)
        //{
        //    BitmapImage bitmapImage = new BitmapImage();
        //    try
        //    {
        //        bool flag = File.Exists(imagePath);
        //        if (flag)
        //        {
        //            bitmapImage.BeginInit();
        //            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
        //            using (Stream stream = new MemoryStream(File.ReadAllBytes(imagePath)))
        //            {
        //                bitmapImage.StreamSource = stream;
        //                bitmapImage.EndInit();
        //                bitmapImage.Freeze();
        //            }
        //        }
        //    }
        //    catch
        //    {
        //    }
        //    return bitmapImage;
        //}
    }
}
