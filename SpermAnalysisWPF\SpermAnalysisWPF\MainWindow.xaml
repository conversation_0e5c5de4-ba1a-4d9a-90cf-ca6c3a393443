﻿<Window x:Class="SpermAnalysisWPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SpermAnalysisWPF"
        mc:Ignorable="d"
        Title="精子形态分析系统 - .NET 8.0 WPF x64" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2E3440" Padding="20">
            <TextBlock Text="精子形态分析系统"
                       FontSize="24"
                       FontWeight="Bold"
                       Foreground="White"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <Border Grid.Column="0" Background="#F8F9FA" Padding="15" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="精子形态分析" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                    <!-- 图像导入区域 -->
                    <GroupBox Header="图像导入" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <Button x:Name="ImportImageButton"
                                    Content="导入图片"
                                    Padding="10,5"
                                    Background="#4CAF50"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Margin="0,0,0,10"
                                    Click="ImportImageButton_Click"/>

                            <Button x:Name="AnalyzeButton"
                                    Content="开始分析"
                                    Padding="10,5"
                                    Background="#2196F3"
                                    Foreground="White"
                                    BorderThickness="0"
                                    IsEnabled="False"
                                    Click="AnalyzeButton_Click"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- 分析结果 -->
                    <GroupBox Header="分析结果" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <TextBlock Text="总数:" FontWeight="Bold" Margin="0,2"/>
                            <TextBlock x:Name="TotalCountText" Text="0" Margin="0,0,0,5"/>

                            <TextBlock Text="正常:" FontWeight="Bold" Margin="0,2"/>
                            <TextBlock x:Name="NormalCountText" Text="0" Margin="0,0,0,5"/>

                            <TextBlock Text="异常:" FontWeight="Bold" Margin="0,2"/>
                            <TextBlock x:Name="AbnormalCountText" Text="0" Margin="0,0,0,5"/>

                            <TextBlock Text="正常率:" FontWeight="Bold" Margin="0,2"/>
                            <TextBlock x:Name="NormalRateText" Text="0%" Margin="0,0,0,10"/>

                            <Button x:Name="SaveResultButton"
                                    Content="保存结果"
                                    Padding="10,5"
                                    Background="#FF9800"
                                    Foreground="White"
                                    BorderThickness="0"
                                    IsEnabled="False"
                                    Margin="0,0,0,5"
                                    Click="SaveResultButton_Click"/>

                            <Button x:Name="ViewResultsButton"
                                    Content="查看结果"
                                    Padding="10,5"
                                    Background="#2196F3"
                                    Foreground="White"
                                    BorderThickness="0"
                                    IsEnabled="False"
                                    Click="ViewResultsButton_Click"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- 系统信息 -->
                    <GroupBox Header="系统信息">
                        <StackPanel Margin="10">
                            <TextBlock Text="框架版本:" FontWeight="Bold" Margin="0,2"/>
                            <TextBlock x:Name="FrameworkVersionText" Text=".NET 8.0" Margin="0,0,0,5"/>

                            <TextBlock Text="架构:" FontWeight="Bold" Margin="0,2"/>
                            <TextBlock x:Name="ArchitectureText" Text="x64" Margin="0,0,0,5"/>

                            <Button x:Name="TestButton"
                                    Content="测试系统"
                                    Padding="10,5"
                                    Background="#6C757D"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Margin="0,10,0,0"
                                    Click="TestButton_Click"/>

                            <Button x:Name="CheckTensorFlowButton"
                                    Content="检查TensorFlow"
                                    Padding="10,5"
                                    Background="#17A2B8"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Margin="0,5,0,0"
                                    Click="CheckTensorFlowButton_Click"/>

                            <Button x:Name="ResetTensorFlowButton"
                                    Content="重置TensorFlow"
                                    Padding="10,5"
                                    Background="#FFC107"
                                    Foreground="Black"
                                    BorderThickness="0"
                                    Margin="0,5,0,0"
                                    Click="ResetTensorFlowButton_Click"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </Border>

            <!-- 中间图像显示区域 -->
            <Border Grid.Column="1" Background="#FFFFFF" Padding="10" Margin="10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 图像标题 -->
                    <TextBlock Grid.Row="0" Text="图像分析" FontSize="18" FontWeight="Bold"
                               Margin="0,0,0,10" Foreground="#2E3440" HorizontalAlignment="Center"/>

                    <!-- 图像显示区域 -->
                    <Border Grid.Row="1" BorderBrush="#DEE2E6" BorderThickness="1">
                        <Grid x:Name="ImageContainer" Background="#F8F9FA">
                            <!-- 图像层 -->
                            <Image x:Name="DisplayImage"
                                   Stretch="Uniform"
                                   RenderOptions.BitmapScalingMode="HighQuality"/>
                            <!-- 绘制层 -->
                            <Canvas x:Name="DrawingCanvas" Background="Transparent"/>
                        </Grid>
                    </Border>

                    <!-- 图像控制按钮 -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                        <CheckBox x:Name="ShowDetectionBoxes" Content="显示检测框" IsChecked="True" Margin="0,0,15,0"/>
                        <CheckBox x:Name="ShowSegmentation" Content="显示分割轮廓" IsChecked="True" Margin="0,0,15,0"/>
                        <CheckBox x:Name="ShowLabels" Content="显示标签" IsChecked="True" Margin="0,0,15,0"/>
                        <Button x:Name="ZoomInButton" Content="放大" Padding="8,4" Margin="0,0,5,0"/>
                        <Button x:Name="ZoomOutButton" Content="缩小" Padding="8,4" Margin="0,0,5,0"/>
                        <Button x:Name="ZoomResetButton" Content="重置" Padding="8,4"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 右侧日志和结果面板 -->
            <Border Grid.Column="2" Background="#F8F9FA" Padding="15" Margin="10,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 分析结果区域 -->
                    <GroupBox Grid.Row="0" Header="分析结果" Margin="0,0,0,10">
                        <TextBox x:Name="OutputText"
                                 Text="请导入图像文件开始分析..."
                                 FontSize="12"
                                 TextWrapping="Wrap"
                                 Foreground="#495057"
                                 Background="Transparent"
                                 BorderThickness="0"
                                 IsReadOnly="True"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Auto"
                                 AcceptsReturn="True"
                                 SelectionBrush="#007ACC"/>
                    </GroupBox>

                    <!-- 系统日志区域 -->
                    <GroupBox Grid.Row="1" Header="系统日志">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox x:Name="LogText"
                                     Grid.Row="0"
                                     Text="系统启动..."
                                     FontSize="11"
                                     FontFamily="Consolas"
                                     TextWrapping="Wrap"
                                     Foreground="#6C757D"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     IsReadOnly="True"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Auto"
                                     AcceptsReturn="True"
                                     SelectionBrush="#007ACC"/>

                            <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,5,0,0">
                                <Button x:Name="RefreshLogButton"
                                        Content="刷新日志"
                                        Padding="8,4"
                                        Margin="0,0,5,0"
                                        Click="RefreshLogButton_Click"/>
                                <Button x:Name="CopyLogButton"
                                        Content="复制日志"
                                        Padding="8,4"
                                        Margin="0,0,5,0"
                                        Click="CopyLogButton_Click"/>
                                <Button x:Name="ClearLogButton"
                                        Content="清空显示"
                                        Padding="8,4"
                                        Click="ClearLogButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#E5E7EB" Padding="10">
            <TextBlock x:Name="StatusText"
                       Text="就绪"
                       FontSize="12"
                       Foreground="#6B7280"/>
        </Border>
    </Grid>
</Window>
