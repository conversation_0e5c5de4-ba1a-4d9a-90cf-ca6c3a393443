@echo off
title 精子形态分析系统 - 强制CPU模式
color 0E

echo ========================================
echo    精子形态分析系统 - 强制CPU模式
echo ========================================
echo.
echo 此启动器将强制程序使用CPU进行计算
echo 适用于显卡内存不足的情况
echo.

echo [1/3] 设置强制CPU环境...
REM 完全禁用CUDA
set CUDA_VISIBLE_DEVICES=
set TF_FORCE_GPU_ALLOW_GROWTH=false
set TF_CPP_MIN_LOG_LEVEL=2

REM 强制TensorFlow使用CPU
set TF_DEVICE_MIN_SYS_MEMORY_IN_MB=2048
set OMP_NUM_THREADS=4

echo     ✓ CUDA已禁用
echo     ✓ TensorFlow配置为CPU模式
echo     ✓ 设置CPU线程数: 4

echo.
echo [2/3] 修改配置文件...
REM 确保配置文件设置为CPU模式
if exist "gpu_config.ini" (
    echo     ✓ 更新gpu_config.ini为CPU模式
) else (
    echo     ! 创建默认CPU配置文件
    echo [GPU设置] > gpu_config.ini
    echo ForceUseCPU=true >> gpu_config.ini
    echo GPUMemoryOptimized=true >> gpu_config.ini
    echo GPUMemoryLimit=0 >> gpu_config.ini
    echo EnableTailProcessing=true >> gpu_config.ini
)

echo.
echo [3/3] 启动程序...
echo     正在启动精子形态分析系统（CPU模式）...
echo     注意：CPU模式运行速度较慢，请耐心等待
echo.

REM 启动程序
start "" "SpermFormAnalysis.exe"

echo     ✓ 程序已启动（CPU模式）
echo.
echo 重要提示：
echo - CPU模式运行速度比GPU模式慢3-5倍
echo - 尾巴信息处理可能需要30-60秒
echo - 请在处理完成后再进行下一步操作
echo.
echo 如果仍有问题，请查看Log文件夹中的日志
echo.
pause
