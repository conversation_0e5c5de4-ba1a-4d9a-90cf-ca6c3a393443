using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Drawing;

namespace SpermAnalysisWPF.Utils
{
    public static class Basic
    {
        // 基本配置参数
        public static int threadNum { get; set; } = 10;
        public static Size imageSize { get; set; } = new Size(1920, 1200);
        public static bool bTailused { get; set; } = true;
        public static bool bCrfAdjust { get; set; } = false;
        public static bool bUseKey { get; set; } = true;
        public static bool bUseICCard { get; set; } = false;
        public static bool bUseMachine { get; set; } = false;
        public static bool bICCardConnected { get; set; } = false;
        public static bool useCheckcode { get; set; } = false;
        public static bool bPwdCrypto { get; set; } = true;
        public static long Balance { get; set; } = 0L;

        // 产品信息
        public static string ProductName = "精子形态分析处理软件—星博智造";
        public static string ProductVer = "";
        public static string CompanyName = "";
        public static string title = ProductName;

        // 检测项目列表
        public static List<string> inpectItems = new List<string>
        {
            "形态"
        };

        // 样本状态列表
        public static List<string> sampleStats = new List<string>
        {
            "新建",
            "采集中",
            "采集完成",
            "已完成"
        };

        // 精子类型枚举
        public enum enumSpermTypeBasic
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal = 1,
            [Description("异常")]
            abnormal = 0,
            [Description("亚临床")]
            subclinical = 2
        }

        // 头部类型枚举
        public enum enumHeadType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal,
            [Description("梨形头")]
            pyriform = 32,
            [Description("锥形头")]
            tapered = 1,
            [Description("瘦头")]
            thin,
            [Description("圆头")]
            round = 16,
            [Description("小头")]
            mirco = 4,
            [Description("大头")]
            macro = 8,
            [Description("不定形头")]
            irregular = 64,
            [Description("矮")]
            _short = 128,
            [Description("高")]
            tall = 256,
            [Description("胖")]
            fat = 512
        }

        // 顶体类型枚举
        public enum enumAcroType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal,
            [Description("小")]
            small,
            [Description("极小")]
            extremly_little,
            [Description("过大")]
            excessive,
            [Description("空泡>2")]
            vaculesGT2,
            [Description("大空泡")]
            bigVacules,
            [Description("质地异常")]
            texture_abnormal
        }

        // 核类型枚举
        public enum enumKernelType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal,
            [Description("空泡>0")]
            vaculesGT0,
            [Description("质地异常")]
            texture_abnormal
        }

        // 中段尾部类型枚举
        public enum enumMDPTailType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal,
            [Description("角度")]
            angle,
            [Description("粗大")]
            big,
            [Description("胞质")]
            cytoso
        }

        // 尾部类型枚举
        public enum enumTailType
        {
            [Description("全部")]
            all = -1,
            [Description("正常")]
            normal,
            [Description("无尾")]
            notail,
            [Description("短尾")]
            shorttail,
            [Description("弯尾")]
            curvetail,
            [Description("折尾")]
            bendtail
        }

        // 获取枚举描述
        public static string GetDes(Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
            return attribute?.Description ?? value.ToString();
        }

        // 获取精子类型列表
        public static ObservableCollection<string> getSpermType(string type)
        {
            if (type == "DFI" || type == "MAT")
            {
                return spermTypeBasic;
            }
            else
            {
                return spermTypeBasicMrf;
            }
        }

        // 获取头部类型列表
        public static ObservableCollection<string> getHeadType(string type)
        {
            if (type == "MRF")
            {
                return spermHeadType;
            }
            else
            {
                return null;
            }
        }

        // 精子类型集合
        public static ObservableCollection<string> spermTypeBasic = new ObservableCollection<string>
        {
            GetDes(enumSpermTypeBasic.all),
            GetDes(enumSpermTypeBasic.normal),
            GetDes(enumSpermTypeBasic.abnormal)
        };

        public static ObservableCollection<string> spermTypeBasicMrf = new ObservableCollection<string>
        {
            GetDes(enumSpermTypeBasic.all),
            GetDes(enumSpermTypeBasic.normal),
            GetDes(enumSpermTypeBasic.subclinical),
            GetDes(enumSpermTypeBasic.abnormal)
        };

        public static ObservableCollection<string> spermHeadType = new ObservableCollection<string>
        {
            GetDes(enumHeadType.all),
            GetDes(enumHeadType.normal),
            GetDes(enumHeadType.pyriform),
            GetDes(enumHeadType.tapered),
            GetDes(enumHeadType.round),
            GetDes(enumHeadType.mirco),
            GetDes(enumHeadType.macro),
            GetDes(enumHeadType.irregular),
            GetDes(enumHeadType.thin),
            GetDes(enumHeadType._short),
            GetDes(enumHeadType.tall),
            GetDes(enumHeadType.fat)
        };

        public static ObservableCollection<string> spermAcroType = new ObservableCollection<string>
        {
            GetDes(enumAcroType.all),
            GetDes(enumAcroType.normal),
            GetDes(enumAcroType.small),
            GetDes(enumAcroType.extremly_little),
            GetDes(enumAcroType.excessive),
            GetDes(enumAcroType.vaculesGT2),
            GetDes(enumAcroType.bigVacules),
            GetDes(enumAcroType.texture_abnormal)
        };

        public static ObservableCollection<string> spermKernelType = new ObservableCollection<string>
        {
            GetDes(enumKernelType.all),
            GetDes(enumKernelType.normal),
            GetDes(enumKernelType.vaculesGT0),
            GetDes(enumKernelType.texture_abnormal)
        };

        public static ObservableCollection<string> spermMDPTailType = new ObservableCollection<string>
        {
            GetDes(enumMDPTailType.all),
            GetDes(enumMDPTailType.normal),
            GetDes(enumMDPTailType.angle),
            GetDes(enumMDPTailType.big),
            GetDes(enumMDPTailType.cytoso)
        };

        public static ObservableCollection<string> spermTailType = new ObservableCollection<string>
        {
            GetDes(enumTailType.all),
            GetDes(enumTailType.normal),
            GetDes(enumTailType.notail),
            GetDes(enumTailType.shorttail),
            GetDes(enumTailType.curvetail),
            GetDes(enumTailType.bendtail)
        };

        // 创建目录
        public static void createDir(string dir)
        {
            string path = Environment.CurrentDirectory + dir;
            if (!System.IO.Directory.Exists(path))
            {
                System.IO.Directory.CreateDirectory(path);
            }
        }

        public static void createFullDir(string dir)
        {
            if (!System.IO.Directory.Exists(dir))
            {
                System.IO.Directory.CreateDirectory(dir);
            }
        }
    }
}
