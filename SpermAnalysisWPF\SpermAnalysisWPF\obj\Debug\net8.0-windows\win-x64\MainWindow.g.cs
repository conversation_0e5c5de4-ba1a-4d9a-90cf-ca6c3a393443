﻿#pragma checksum "..\..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B1E0AB1932015CC781D1C4481BFAE35747660EAC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SpermAnalysisWPF;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SpermAnalysisWPF {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ImportImageButton;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnalyzeButton;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCountText;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NormalCountText;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AbnormalCountText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NormalRateText;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveResultButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewResultsButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrameworkVersionText;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ArchitectureText;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CheckTensorFlowButton;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetTensorFlowButton;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ImageContainer;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image DisplayImage;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas DrawingCanvas;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowDetectionBoxes;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowSegmentation;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowLabels;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomInButton;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomOutButton;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomResetButton;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OutputText;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogText;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshLogButton;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyLogButton;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearLogButton;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SpermAnalysisWPF;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ImportImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\MainWindow.xaml"
            this.ImportImageButton.Click += new System.Windows.RoutedEventHandler(this.ImportImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AnalyzeButton = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\MainWindow.xaml"
            this.AnalyzeButton.Click += new System.Windows.RoutedEventHandler(this.AnalyzeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TotalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.NormalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.AbnormalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.NormalRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.SaveResultButton = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\..\..\MainWindow.xaml"
            this.SaveResultButton.Click += new System.Windows.RoutedEventHandler(this.SaveResultButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ViewResultsButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\..\MainWindow.xaml"
            this.ViewResultsButton.Click += new System.Windows.RoutedEventHandler(this.ViewResultsButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.FrameworkVersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ArchitectureText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TestButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\MainWindow.xaml"
            this.TestButton.Click += new System.Windows.RoutedEventHandler(this.TestButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.CheckTensorFlowButton = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\MainWindow.xaml"
            this.CheckTensorFlowButton.Click += new System.Windows.RoutedEventHandler(this.CheckTensorFlowButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ResetTensorFlowButton = ((System.Windows.Controls.Button)(target));
            
            #line 132 "..\..\..\..\MainWindow.xaml"
            this.ResetTensorFlowButton.Click += new System.Windows.RoutedEventHandler(this.ResetTensorFlowButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ImageContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.DisplayImage = ((System.Windows.Controls.Image)(target));
            return;
            case 16:
            this.DrawingCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 17:
            this.ShowDetectionBoxes = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.ShowSegmentation = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.ShowLabels = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.ZoomInButton = ((System.Windows.Controls.Button)(target));
            return;
            case 21:
            this.ZoomOutButton = ((System.Windows.Controls.Button)(target));
            return;
            case 22:
            this.ZoomResetButton = ((System.Windows.Controls.Button)(target));
            return;
            case 23:
            this.OutputText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 24:
            this.LogText = ((System.Windows.Controls.TextBox)(target));
            return;
            case 25:
            this.RefreshLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\MainWindow.xaml"
            this.RefreshLogButton.Click += new System.Windows.RoutedEventHandler(this.RefreshLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.CopyLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\MainWindow.xaml"
            this.CopyLogButton.Click += new System.Windows.RoutedEventHandler(this.CopyLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.ClearLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 236 "..\..\..\..\MainWindow.xaml"
            this.ClearLogButton.Click += new System.Windows.RoutedEventHandler(this.ClearLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

