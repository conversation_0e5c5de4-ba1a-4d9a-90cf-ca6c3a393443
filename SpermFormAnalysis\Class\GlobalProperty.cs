﻿using GxIAPINET;
using NPOI.HSSF.Record.Chart;
using SpermFormAnalysis.Data;
using SpermFormAnalysis.utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.Class
{
    public static class GlobalProperty
    {
        private readonly static IniFileUtils iniFileUtil = new IniFileUtils(Environment.CurrentDirectory + "\\AppConfig.ini");

        private static bool m_IsLogin;
        /// <summary>
        /// 操作员是否登录
        /// </summary>
        public static bool IsLogin
        {
            get { return GlobalProperty.m_IsLogin; }
            set { GlobalProperty.m_IsLogin = value; }
        }

        private static string m_Admin_ID;
        /// <summary>
        /// 操作员用户名
        /// </summary>
        public static string Admin_ID
        {
            get
            {
                return m_Admin_ID;
            }
            set
            {
                m_Admin_ID = value;
                iniFileUtil.WriteString("user", "account", value);
            }
        }

        private static string m_Admin_Name;
        /// <summary>
        /// 操作员名称
        /// </summary>
        public static string Admin_Name
        {
            get
            {
                return m_Admin_Name;
            }
            set
            {
                m_Admin_Name = value;
            }
        }

        private static string m_Admin_PWD;
        /// <summary>
        /// 操作员密码
        /// </summary>
        public static string Admin_PWD
        {
            get
            {
                return m_Admin_PWD;
            }
            set
            {
                m_Admin_PWD = value;
                iniFileUtil.WriteString("user", "_token", des.Encrypt(value));
            }
        }

        private static string m_Admin_Level;
        /// <summary>
        /// 操作员级别
        /// </summary>
        public static string Admin_Level
        {
            get
            {
                return m_Admin_Level;
            }
            set
            {
                m_Admin_Level = value;
            }
        }

        private static String m_AutCod;
        /// <summary>
        /// 注册码
        /// </summary>
        public static String AutCod
        {
            get { return GlobalProperty.m_AutCod; }
            set { GlobalProperty.m_AutCod = value; iniFileUtil.WriteString("auth", "_auth_code", des.Encrypt(value)); }
        }

        private static string _id;

        public static String Id
        {
            get { return GlobalProperty._id; }
            set { GlobalProperty._id = value; iniFileUtil.WriteString("auth", "_id", des.Encrypt(value)); }
        }

        private static String m_Hospital;
        /// <summary>
        /// 医院名称
        /// </summary>
        public static String Hospital
        {
            get { return GlobalProperty.m_Hospital; }
            set { GlobalProperty.m_Hospital = value; iniFileUtil.WriteString("user", "organization_name", value); }
        }
        private static String _ks;
        /// <summary>
        /// 部门名称
        /// </summary>
        public static String Ks
        {
            get { return GlobalProperty._ks; }
            set
            {
                GlobalProperty._ks = value;
                iniFileUtil.WriteString("user", "department", value);
            }
        }



        private static string _BalanceWhite;
        public static String BalanceWhite
        {
            get {  _BalanceWhite = iniFileUtil.ReadString("Camera", "BalanceWhite", "Continuous");
                return _BalanceWhite;
            }
            set
            {
                
                GlobalProperty._BalanceWhite = value;
                iniFileUtil.WriteString("Camera", "BalanceWhite", value);
            }
        }

        private static string _ExposureAuto;
        public static string ExposureAuto
        {
            get {
                _ExposureAuto=iniFileUtil.ReadString("Camera", "ExposureAuto", "Off");
                return _ExposureAuto;
            }
            set
            {
                GlobalProperty._ExposureAuto = value;
                iniFileUtil.WriteString("Camera", "ExposureAuto", value);
            }
        }

        private static string _GainAuto;
        public static string GainAuto
        {
            get {  _GainAuto = iniFileUtil.ReadString("Camera", "GainAuto", "Off");
                return _GainAuto;
            }
            set
            {
                GlobalProperty._GainAuto = value;
                iniFileUtil.WriteString("Camera", "GainAuto", value);
            }
        }

        private static string _Gain;
        public static string Gain
        {
            get
            {
                _Gain = iniFileUtil.ReadString("Camera", "Gain", "12");
                return _Gain;
            }
            set
            {
                GlobalProperty._Gain = value;
                iniFileUtil.WriteString("Camera", "Gain", value);
            }
        }

        private static string _ExposureTime;
        public static string ExposureTime
        {
            get
            {
                _ExposureTime = iniFileUtil.ReadString("Camera", "ExposureTime", "120000");
                return _ExposureTime;
            }
            set
            {
                GlobalProperty._ExposureTime = value;
                iniFileUtil.WriteString("Camera", "ExposureTime", value);
            }
        }

        private static string _AcquisitionFrameRate;
        public static string AcquisitionFrameRate
        {
            get
            {
                _AcquisitionFrameRate = iniFileUtil.ReadString("Camera", "AcquisitionFrameRate", "20");
                return _AcquisitionFrameRate;
            }
            set
            {
                GlobalProperty._AcquisitionFrameRate = value;
                iniFileUtil.WriteString("Camera", "AcquisitionFrameRate", value);
            }
        }




        private static int m_Test_Type;
        /// <summary>
        /// 测试类型
        /// </summary>
        public static int Test_Type
        {
            get { return GlobalProperty.m_Test_Type; }
            set { GlobalProperty.m_Test_Type = value; }
        }

        private static int m_Machine_Type;
        /// <summary>
        /// 机器类型
        /// </summary>
        public static int Machine_Type
        {
            get { return GlobalProperty.m_Machine_Type; }
            set { GlobalProperty.m_Machine_Type = value; }
        }
        private static String Test_Type_NoS;
        public static String Test_Type_No
        {
            get { return GlobalProperty.Test_Type_NoS; }
            set { GlobalProperty.Test_Type_NoS = value; }
        }

        private static String camType;
        public static String CamType
        {
            get { return GlobalProperty.camType; }
            set { GlobalProperty.camType = value; }
        }
        private static string strAutcod;

        public static string StrAutcod
        {
            get { return GlobalProperty.strAutcod; }
            set { GlobalProperty.strAutcod = value; }
        }
        /// <summary>
        /// 授权卡剩余次数
        /// </summary>
        private static int renumber;
        public static int Renumber
        {
            get { return GlobalProperty.renumber; }
            set { GlobalProperty.renumber = value; }
        }
        private static int maxCellNum;//最大精子数目
        public static int MaxCellNum
        {
            get { return maxCellNum; }
            set
            {
                maxCellNum = value;
                iniFileUtil.WriteInt("test", "non_auto_maxCellNum", value);
            }
        }
        private static int maxPic;//最大图片数目
        public static int MaxPic
        {
            get { return maxPic; }
            set
            {
                maxPic = value;
                iniFileUtil.WriteInt("test", "non_auto_maxPic", value);
            }
        }

        private static int _ui_style;
        public static int Ui_Style
        {
            get
            {
                return _ui_style;
            }
            set
            {
                _ui_style = value;
                iniFileUtil.WriteInt("user", "ui_style", value);
            }
        }
        //密钥
        private static String key = "slkdj#*fi0@9sd83**)9w3((*^*@#$oia4^4sed8(*@#kasdj334-2*1&9$ds0JK93";
        //初始变量
        private static String IV = "klsjd9i023u908jakvj9ij0wefnih1-0023*&%%MOJIlkjmvoji1*&^%%lkdjoi_)(&1235&";
        //加解密对象
        public static DES_ des = new DES_(key, IV);

        public static IGXDevice m_objIGXDevice = null;                            ///<设备对像

        // 自动化设备行列
        private static int _rows;
        public static int Rows
        {
            get { return _rows; }
            set { _rows = value; iniFileUtil.WriteInt("test", "auto_rows", value); }
        }

        private static int _clos;


        public static int Cols
        {
            get { return _clos; }
            set { _clos = value; iniFileUtil.WriteInt("test", "auto_cols", value); }
        }
        private static int _qtrfindex;
        public static int QtrfIndex
        {
            get { return _qtrfindex; }
            set { _qtrfindex = value; iniFileUtil.WriteInt("test", "QtrfName", value); }
        }

        private static string _Machine_Code;
        public static string Machine_Code
        {
            get
            {
                return _Machine_Code;
            }
            set
            {
                _Machine_Code = value;
                iniFileUtil.WriteString("auth", "_machine_code", des.Encrypt(value));
            }
        }
        private static bool _Remember_User;
        public static bool Remember_User { get { return _Remember_User; } internal set { _Remember_User = value; iniFileUtil.WriteInt("user", "remember_user", value ? 1 : 0); } }

        private static string _testers;
        public static string Testers
        {
            get { return _testers; }
            set
            {
                _testers = value;
                if (value.StartsWith("#")) _testers = _testers.Substring(1);
                if (value.EndsWith("#")) _testers = _testers.Substring(0, _testers.Length - 1);
                iniFileUtil.WriteString("test", "testers", _testers);
            }
        }

        private static string _reviewers;
        public static string Reviewers
        {
            get { return _reviewers; }
            set
            {
                _reviewers = value;
                if (value.StartsWith("#")) _reviewers = _reviewers.Substring(1);
                if (value.EndsWith("#")) _reviewers = _reviewers.Substring(0, _reviewers.Length - 1);
                iniFileUtil.WriteString("test", "reviewers", _reviewers);
            }
        }

        private static string _doctors;
        public static string Doctors
        {
            get { return _doctors; }
            set
            {
                _doctors = value;
                if (value.StartsWith("#")) _doctors = _doctors.Substring(1);
                if (value.EndsWith("#")) _doctors = _doctors.Substring(0, _doctors.Length - 1);
                iniFileUtil.WriteString("test", "doctors", _doctors);
            }
        }

        private static string _mode;
        public static string Mode
        {
            get { return _mode; }
            set
            {
                _mode = value;
                iniFileUtil.WriteString("test", "mode", value);
            }
        }


        private static string _hisService;

        public static string HisService
        {
            get { return _hisService; }
            set
            {
                _hisService = value;
                iniFileUtil.WriteString("test", "his_service", value);
            }
        }

        private static string _LisInterface;

        public static string LisInterface
        {
            get { return _LisInterface; }
            set
            {
                _LisInterface = value;
                iniFileUtil.WriteString("test", "lis_interface", value);
            }
        }

        private static bool _lis_push_on_calc;

        /// <summary>
        /// 计算完成后自动上传至lis 
        /// </summary>
        public static bool Lis_push_on_calc
        {
            get { return _lis_push_on_calc; }
            set
            {
                _lis_push_on_calc = value;
                iniFileUtil.WriteInt("test", "lis_push_on_calc", value ? 1 : 0);
            }
        }

        private static bool _create_report_on_calc;

        /// <summary>
        /// 计算完成后直接上生成报告
        /// </summary>
        public static bool Create_report_on_calc
        {
            get { return _create_report_on_calc; }
            set
            {
                _create_report_on_calc = value;
                iniFileUtil.WriteInt("test", "create_report_on_calc", value ? 1 : 0);
            }
        }





        internal static void InitParams()
        {
            m_Admin_ID = iniFileUtil.ReadString("user", "account", "");
            //m_Admin_PWD = GlobalProperty.des.Decrypt(iniFileUtil.ReadString("user", "_token", ""));

            m_Hospital = iniFileUtil.ReadString("user", "organization_name", "");
            _ks = iniFileUtil.ReadString("user", "department", "");
            string id = iniFileUtil.ReadString("auth", "_id", "");

            if ((_id = id == "init" ? "init" : GlobalProperty.des.Decrypt(id)) == "init") m_AutCod = "";
            else m_AutCod = GlobalProperty.des.Decrypt(iniFileUtil.ReadString("auth", "_auth_code", ""));
            _Machine_Code = GlobalProperty.des.Decrypt(iniFileUtil.ReadString("auth", "_machine_code", ""));
            _Remember_User = iniFileUtil.ReadInt("user", "remember_user", 0) == 1;
            _testers = iniFileUtil.ReadString("test", "testers", "");
            _doctors = iniFileUtil.ReadString("test", "doctors", "");
            _reviewers = iniFileUtil.ReadString("test", "reviewers", "");
            _mode = iniFileUtil.ReadString("test", "mode", "auto");
            _hisService = iniFileUtil.ReadString("test", "his_service", "");
            _LisInterface = iniFileUtil.ReadString("test", "lis_interface", "");
            _lis_push_on_calc = iniFileUtil.ReadInt("test", "lis_push_on_calc", 0) == 1;
            _create_report_on_calc = iniFileUtil.ReadInt("test", "lis_push_on_calc", 0) == 1;
            _qtrfindex = iniFileUtil.ReadInt("test", "QtrfName", 1);
            if (_mode == "auto")
            {
                _rows = iniFileUtil.ReadInt("test", "auto_rows", 5);
                _clos = iniFileUtil.ReadInt("test", "auto_cols", 5);
                
                // 初始化配置文件
                IniFileUtils myIni = new IniFileUtils(Environment.CurrentDirectory + "\\configure\\manager.config");
                myIni.WriteString("system", "root", Environment.CurrentDirectory.Replace("\\", "/"));
            }
            else
            {
                maxCellNum = iniFileUtil.ReadInt("test", "non_auto_maxCellNum", 300);
                maxPic = iniFileUtil.ReadInt("test", "non_auto_maxPic", 30);
            }

        }
    }
}
