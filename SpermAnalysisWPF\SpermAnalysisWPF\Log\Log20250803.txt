2025-08-03 09:32:44,INFO: 系统初始化完成
2025-08-03 09:32:44,INFO: 开始TensorFlow预加载...
2025-08-03 09:32:44,INFO: 开始初始化TensorFlow环境...
2025-08-03 09:32:44,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 09:32:44,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:32:44,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 09:32:44,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 09:32:44,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:32:44,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:32:44,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:32:44,INFO: 文件存在: libtensorflow.dll
2025-08-03 09:32:44,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 09:32:44,INFO: 文件存在: TFRunModel.dll
2025-08-03 09:32:44,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 09:32:44,INFO: 文件存在: TFCommon.dll
2025-08-03 09:32:44,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 09:32:44,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 09:32:44,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 09:32:44,INFO: 模型文件存在
2025-08-03 09:32:44,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 09:32:44,INFO: 配置文件存在
2025-08-03 09:32:44,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:32:44,INFO: 所有必要文件检查通过
2025-08-03 09:32:44,INFO: 已设置PATH环境变量
2025-08-03 09:32:44,INFO: TensorFlow环境变量设置完成
2025-08-03 09:32:44,INFO: 开始预加载TensorFlow模型...
2025-08-03 09:32:52,INFO: 导入图像: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:32:52,INFO: 图像加载成功: 1920x1200
2025-08-03 09:32:52,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 09:32:54,INFO: 开始分析图像: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:32:54,INFO: 开始TensorFlow模型检测...
2025-08-03 09:33:01,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 09:33:01,INFO: TensorFlow初始化成功
2025-08-03 09:33:01,INFO: 运行TensorFlow模型: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:33:01,INFO: TensorFlow预加载成功
2025-08-03 09:33:01,INFO: 模型运行完成，检测到 7 个目标
2025-08-03 09:33:02,DEBUG: 性能统计 - 图像分析: 8218.46ms
2025-08-03 09:33:02,INFO: 分析完成，检测到 6 个精子
2025-08-03 09:33:02,INFO: 开始在图像上标记分析结果...
2025-08-03 09:33:02,DEBUG: 精子1坐标计算: 原始(0.291,0.615)-(0.387,0.690), 显示(341.9,457.1)-(155.5x94.6), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制检测框: 精子1, 坐标(341.9,457.1), 尺寸(155.5x94.6)
2025-08-03 09:33:02,DEBUG: 精子1坐标计算: 原始(0.291,0.615)-(0.387,0.690), 显示(341.9,457.1)-(155.5x94.6), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='64,13 63,14 62,14 61,14 60,14 59,15 58,15 57,15 56,16 55,16 54,17 53,18 52,18 51,18 50,19 49,19 49,20 48,21 47,22 46,22 46,23 46,24 45,25 44,26 44,27 43,28 43,29 42,30 41,29 40,28 39,27 38,28 37,29 36,30 35,31 35,32 35,33 35,34 34,35 34,36 33,37 32,38 31,39 31,40 31,41 31,42 30,43 30,44 30,45 30,46 29,47 29,48 29,49 29,50 29,51 29,52 29,53 29,54 29,55 29,56 29,57 29,58 29,59 29,60 29,61 30,62 30,63 30,64 31,65 32,66 32,67 33,68 34,69 35,70 35,71 36,72 37,73 38,74 39,75 40,76 41,76 42,77 43,78 44,79 45,79 46,80 47,81 48,81 49,82 50,82 51,83 52,83 53,84 54,84 55,85 56,85 57,85 58,85 59,85 60,85 61,84 62,83 63,82 64,81 65,82 66,82 67,82 68,82 69,83 70,83 71,83 72,83 73,84 74,84 75,84 76,84 77,84 78,84 79,84 80,84 81,84 82,84 83,84 84,84 85,84 86,84 87,84 88,84 89,84 90,84 91,84 92,84 93,84 94,84 95,84 96,84 97,83 98,83 99,83 100,82 101,82 102,82 103,81 104,81 105,80 106,80 107,79 108,78 109,78 110,77 111,76 112,76 113,75 114,74 115,73 116,72 117,71 118,70 119,69 119,68 120,67 120,66 121,65 121,64 121,63 121,62 121,61 121,60 121,59 121,58 121,57 120,56 120,55 120,54 119,53 119,52 118,51 118,50 118,49 118,48 117,47 117,46 116,45 116,44 115,43 114,42 114,41 113,40 112,39 112,38 111,38 110,37 109,36 108,35 107,34 106,33 105,32 104,31 103,31 102,30 101,29 100,29 99,28 98,28 97,27 96,26 96,25 95,24 94,24 93,23 92,22 91,21 90,21 89,20 88,20 87,19 86,18 85,18 84,17 83,17 82,17 81,16 80,16 79,15 78,15 77,14 76,14 75,14 74,13 73,13 72,13 71,13 70,13 69,13 68,13 67,13 66,13 65,13 ', 解析出238个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始238个点, 转换后238个点, 显示偏移(341.9,457.1), 原始偏移(530.6,709.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='39,27 38,28 37,29 36,30 35,31 35,32 35,33 35,34 34,35 34,36 33,37 32,38 31,39 31,40 31,41 31,42 30,43 30,44 30,45 30,46 29,47 29,48 29,49 29,50 29,51 29,52 29,53 29,54 29,55 29,56 29,57 29,58 29,59 29,60 29,61 30,62 30,63 30,64 31,65 32,66 32,67 33,68 34,69 35,70 35,71 36,72 37,73 38,74 39,75 40,76 41,76 42,77 43,78 44,79 45,79 46,80 47,81 48,81 49,82 50,82 51,83 52,83 53,84 54,84 55,85 56,85 57,85 58,85 59,85 60,85 61,84 62,83 63,82 62,81 63,80 64,79 63,78 62,77 61,76 60,75 59,74 58,73 57,72 57,71 56,70 55,69 55,68 55,67 54,66 54,65 54,64 53,63 53,62 53,61 53,60 52,59 52,58 52,57 51,56 51,55 51,54 50,53 49,52 49,51 48,50 47,49 47,48 46,47 46,46 45,45 45,44 45,43 45,42 45,41 45,40 45,39 46,38 46,37 46,36 45,35 44,34 43,33 42,32 42,31 42,30 41,29 40,28 ', 解析出127个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始127个点, 转换后127个点, 显示偏移(341.9,457.1), 原始偏移(530.6,709.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='64,13 63,14 62,14 61,14 60,14 59,15 58,15 57,15 56,16 55,16 54,17 53,18 52,18 51,18 50,19 49,19 49,20 48,21 47,22 46,22 46,23 46,24 45,25 44,26 44,27 43,28 43,29 43,30 43,31 43,32 44,33 45,34 46,35 47,36 47,37 47,38 46,39 46,40 46,41 46,42 46,43 46,44 46,45 47,46 47,47 48,48 48,49 49,50 50,51 50,52 51,53 52,54 52,55 52,56 53,57 53,58 53,59 54,60 54,61 54,62 54,63 55,64 55,65 55,66 56,67 56,68 56,69 57,70 58,71 58,72 59,73 60,74 61,75 62,76 63,77 64,78 65,79 64,80 63,81 64,81 65,82 66,82 67,82 68,82 69,83 70,83 71,83 72,83 73,84 74,84 75,84 76,84 77,84 78,84 79,84 80,84 81,84 82,84 83,84 84,84 85,84 86,84 87,84 88,84 89,84 90,84 91,84 92,84 93,84 94,84 95,84 96,84 97,83 98,83 99,83 100,82 101,82 102,82 103,81 104,81 105,80 106,80 107,79 108,78 109,78 110,77 111,76 112,76 113,75 114,74 115,73 116,72 117,71 118,70 119,69 119,68 120,67 120,66 121,65 121,64 121,63 121,62 121,61 121,60 121,59 121,58 121,57 120,56 120,55 120,54 119,53 119,52 118,51 118,50 118,49 118,48 117,47 117,46 116,45 116,44 115,43 114,42 114,41 113,40 112,39 112,38 111,38 110,37 109,36 108,35 107,34 106,33 105,32 104,31 103,31 102,30 101,29 100,29 99,28 98,28 97,27 96,26 96,25 95,24 94,24 93,23 92,22 91,21 90,21 89,20 88,20 87,19 86,18 85,18 84,17 83,17 82,17 81,16 80,16 79,15 78,15 77,14 76,14 75,14 74,13 73,13 72,13 71,13 70,13 69,13 68,13 67,13 66,13 65,13 ', 解析出214个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始214个点, 转换后214个点, 显示偏移(341.9,457.1), 原始偏移(530.6,709.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 精子1坐标计算: 原始(0.291,0.615)-(0.387,0.690), 显示(341.9,457.1)-(155.5x94.6), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制标签: 精子1, 位置(341.9,437.1)
2025-08-03 09:33:02,DEBUG: 精子2坐标计算: 原始(0.443,0.688)-(0.549,0.750), 显示(529.9,513.2)-(167.8x84.7), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制检测框: 精子2, 坐标(529.9,513.2), 尺寸(167.8x84.7)
2025-08-03 09:33:02,DEBUG: 精子2坐标计算: 原始(0.443,0.688)-(0.549,0.750), 显示(529.9,513.2)-(167.8x84.7), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='175,30 174,31 173,31 172,31 171,31 170,31 169,31 168,31 167,31 166,31 165,32 164,32 163,32 162,33 161,34 160,35 159,35 158,35 157,35 156,35 155,35 154,35 153,35 152,35 151,35 150,35 149,35 148,35 147,35 146,35 145,35 144,35 143,35 142,35 141,35 140,35 139,35 138,35 137,35 136,35 135,35 134,35 133,35 132,36 131,36 130,37 129,37 128,38 127,38 126,39 125,40 124,41 123,42 122,43 121,44 121,45 120,46 119,47 118,48 117,49 117,50 116,51 115,52 115,53 115,54 114,55 114,56 114,57 114,58 114,59 114,60 114,61 114,62 114,63 114,64 114,65 114,66 114,67 114,68 115,69 115,70 116,71 116,72 117,73 118,73 119,74 120,75 121,75 122,75 123,76 124,76 125,77 126,77 127,77 128,78 129,78 130,78 131,78 132,78 133,79 134,79 135,79 136,79 137,79 138,79 139,80 140,80 141,80 142,80 143,80 144,80 145,80 146,80 147,80 148,80 149,80 150,80 151,80 152,80 153,80 154,80 155,80 156,80 157,80 158,80 159,80 160,80 161,80 162,80 163,79 164,79 165,79 166,79 167,80 168,81 169,81 170,81 171,81 172,81 173,81 174,81 175,81 176,81 177,81 178,80 179,80 180,80 181,80 182,79 183,79 184,79 185,79 186,78 187,78 188,78 189,77 190,77 191,77 192,76 193,76 194,75 195,75 196,74 197,74 198,73 199,73 200,72 201,72 202,71 203,70 204,69 205,68 206,67 207,66 208,65 208,64 209,63 210,62 210,61 211,60 212,59 213,58 214,57 214,56 214,55 214,54 214,53 214,52 214,51 214,50 214,49 214,48 214,47 214,46 214,45 214,44 214,43 214,42 214,41 213,40 212,39 211,38 211,37 210,37 209,36 208,35 207,34 206,33 205,33 204,32 203,32 202,32 201,31 200,31 199,30 198,30 197,30 196,30 195,30 194,30 193,30 192,30 191,30 190,30 189,30 188,30 187,30 186,30 185,30 184,30 183,30 182,30 181,30 180,30 179,30 178,30 177,30 176,30 ', 解析出238个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始238个点, 转换后238个点, 显示偏移(529.9,513.2), 原始偏移(822.4,796.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='175,30 174,31 173,31 172,31 171,31 170,31 169,31 168,31 167,31 166,31 165,32 164,32 163,32 162,33 161,34 161,35 161,36 160,37 159,38 158,39 157,40 158,41 159,42 160,43 161,44 162,45 162,46 162,47 162,48 162,49 162,50 162,51 161,52 161,53 161,54 161,55 161,56 161,57 161,58 161,59 161,60 161,61 162,62 162,63 162,64 163,65 163,66 163,67 163,68 163,69 163,70 162,71 161,72 162,73 163,74 164,75 165,76 166,77 167,78 168,79 167,80 168,81 169,81 170,81 171,81 172,81 173,81 174,81 175,81 176,81 177,81 178,80 179,80 180,80 181,80 182,79 183,79 184,79 185,79 186,78 187,78 188,78 189,77 190,77 191,77 192,76 193,76 194,75 195,75 196,74 197,74 198,73 199,73 200,72 201,72 202,71 203,70 204,69 205,68 206,67 207,66 208,65 208,64 209,63 210,62 210,61 211,60 212,59 213,58 214,57 214,56 214,55 214,54 214,53 214,52 214,51 214,50 214,49 214,48 214,47 214,46 214,45 214,44 214,43 214,42 214,41 213,40 212,39 211,38 211,37 210,37 209,36 208,35 207,34 206,33 205,33 204,32 203,32 202,32 201,31 200,31 199,30 198,30 197,30 196,30 195,30 194,30 193,30 192,30 191,30 190,30 189,30 188,30 187,30 186,30 185,30 184,30 183,30 182,30 181,30 180,30 179,30 178,30 177,30 176,30 ', 解析出165个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始165个点, 转换后165个点, 显示偏移(529.9,513.2), 原始偏移(822.4,796.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='133,35 132,36 131,36 130,37 129,37 128,38 127,38 126,39 125,40 124,41 123,42 122,43 121,44 121,45 120,46 119,47 118,48 117,49 117,50 116,51 115,52 115,53 115,54 114,55 114,56 114,57 114,58 114,59 114,60 114,61 114,62 114,63 114,64 114,65 114,66 114,67 114,68 115,69 115,70 116,71 116,72 117,73 118,73 119,74 120,75 121,75 122,75 123,76 124,76 125,77 126,77 127,77 128,78 129,78 130,78 131,78 132,78 133,79 134,79 135,79 136,79 137,79 138,79 139,80 140,80 141,80 142,80 143,80 144,80 145,80 146,80 147,80 148,80 149,80 150,80 151,80 152,80 153,80 154,80 155,80 156,80 157,80 158,80 159,80 160,80 161,80 162,80 163,79 164,79 165,79 166,79 167,79 166,78 165,77 164,76 163,75 162,74 161,73 160,72 161,71 162,70 162,69 162,68 162,67 162,66 162,65 161,64 161,63 161,62 160,61 160,60 160,59 160,58 160,57 160,56 160,55 160,54 160,53 160,52 161,51 161,50 161,49 161,48 161,47 161,46 161,45 160,44 159,43 158,42 157,41 156,40 157,39 158,38 159,37 160,36 160,35 159,35 158,35 157,35 156,35 155,35 154,35 153,35 152,35 151,35 150,35 149,35 148,35 147,35 146,35 145,35 144,35 143,35 142,35 141,35 140,35 139,35 138,35 137,35 136,35 135,35 134,35 ', 解析出162个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始162个点, 转换后162个点, 显示偏移(529.9,513.2), 原始偏移(822.4,796.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 精子2坐标计算: 原始(0.443,0.688)-(0.549,0.750), 显示(529.9,513.2)-(167.8x84.7), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制标签: 精子2, 位置(529.9,493.2)
2025-08-03 09:33:02,DEBUG: 精子3坐标计算: 原始(0.423,0.599)-(0.479,0.641), 显示(505.4,444.9)-(105.7x68.9), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制检测框: 精子3, 坐标(505.4,444.9), 尺寸(105.7x68.9)
2025-08-03 09:33:02,DEBUG: 精子3坐标计算: 原始(0.423,0.599)-(0.479,0.641), 显示(505.4,444.9)-(105.7x68.9), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='72,25 71,26 70,27 69,27 68,27 67,28 67,29 66,30 66,31 65,32 65,33 65,34 65,35 65,36 65,37 65,38 65,39 65,40 65,41 65,42 65,43 65,44 65,45 65,46 66,47 66,48 66,49 67,50 68,51 69,52 70,52 71,53 72,53 73,53 74,53 75,53 76,53 77,53 78,53 79,53 80,53 81,53 82,53 83,53 84,53 85,53 86,52 87,52 88,52 89,51 90,51 91,51 92,51 93,51 94,51 95,51 96,51 97,51 98,51 99,51 100,51 101,52 102,52 103,52 104,52 105,52 106,52 107,52 108,52 109,52 110,52 111,52 112,52 113,51 114,51 115,50 116,49 117,49 118,48 118,47 119,46 119,45 120,44 120,43 120,42 120,41 120,40 120,39 120,38 120,37 120,36 119,35 119,34 118,33 118,32 118,31 117,31 116,30 115,29 114,29 113,28 112,28 111,28 110,28 109,28 108,28 107,28 106,28 105,28 104,28 103,28 102,28 101,28 100,28 99,28 98,29 97,29 96,29 95,29 94,29 93,29 92,29 91,29 90,28 89,28 88,28 87,27 86,27 85,27 84,26 83,25 82,25 81,25 80,25 79,25 78,25 77,25 76,25 75,25 74,25 73,25 ', 解析出141个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始141个点, 转换后141个点, 显示偏移(505.4,444.9), 原始偏移(784.4,690.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='72,25 71,26 70,27 69,27 68,27 67,28 67,29 66,30 66,31 65,32 65,33 65,34 65,35 65,36 65,37 65,38 65,39 65,40 65,41 65,42 65,43 65,44 65,45 65,46 66,47 66,48 66,49 67,50 68,51 69,52 70,52 71,53 72,53 73,53 74,53 75,53 76,53 77,53 78,53 79,53 80,53 81,53 82,53 83,53 84,53 85,53 86,52 87,52 88,52 89,51 90,51 91,51 92,51 93,51 94,51 95,51 96,51 97,51 98,51 99,51 100,51 101,52 102,52 103,52 104,52 105,52 106,52 107,52 108,52 109,52 110,52 111,52 112,52 113,51 114,51 115,50 116,49 117,49 118,48 118,47 119,46 119,45 120,44 120,43 120,42 120,41 120,40 120,39 120,38 120,37 120,36 119,35 119,34 118,33 118,32 118,31 117,31 116,30 115,29 114,29 113,28 112,28 111,28 110,28 109,28 108,28 107,28 106,28 105,28 104,28 103,28 102,28 101,28 100,28 99,28 98,29 97,29 96,29 95,29 94,29 93,29 92,29 91,29 90,28 89,28 88,28 87,27 86,27 85,27 84,26 83,25 82,25 81,25 80,25 79,25 78,25 77,25 76,25 75,25 74,25 73,25 ', 解析出141个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始141个点, 转换后141个点, 显示偏移(505.4,444.9), 原始偏移(784.4,690.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 精子3坐标计算: 原始(0.423,0.599)-(0.479,0.641), 显示(505.4,444.9)-(105.7x68.9), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制标签: 精子3, 位置(505.4,424.9)
2025-08-03 09:33:02,DEBUG: 精子4坐标计算: 原始(0.547,0.156)-(0.604,0.356), 显示(658.5,102.1)-(107.1x191.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制检测框: 精子4, 坐标(658.5,102.1), 尺寸(107.1x191.2)
2025-08-03 09:33:02,DEBUG: 精子4坐标计算: 原始(0.547,0.156)-(0.604,0.356), 显示(658.5,102.1)-(107.1x191.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='78,47 77,48 76,48 75,48 74,48 73,49 72,49 71,49 70,50 69,51 68,51 67,52 66,53 65,54 64,55 63,55 62,56 61,57 61,58 60,59 59,60 59,61 58,62 57,63 56,64 56,65 55,66 55,67 55,68 54,69 53,70 53,71 53,72 53,73 52,74 52,75 52,76 51,77 51,78 51,79 51,80 51,81 51,82 51,83 51,84 50,85 50,86 50,87 50,88 50,89 50,90 50,91 50,92 50,93 50,94 51,95 51,96 51,97 51,98 51,99 51,100 51,101 52,102 52,103 52,104 52,105 53,106 53,107 53,108 53,109 54,110 54,111 55,112 55,113 56,114 56,115 56,116 56,117 56,118 56,119 57,120 57,121 57,122 58,123 58,124 58,125 58,126 58,127 58,128 58,129 57,130 57,131 57,132 56,133 56,134 55,135 55,136 54,137 53,138 53,139 52,140 52,141 52,142 52,143 51,144 51,145 50,146 50,147 50,148 50,149 49,150 49,151 49,152 49,153 49,154 49,155 49,156 48,157 48,158 48,159 48,160 48,161 48,162 48,163 48,164 48,165 49,166 49,167 49,168 49,169 49,170 49,171 49,172 49,173 49,174 50,175 50,176 50,177 51,178 52,179 53,179 54,180 54,181 55,181 56,181 57,181 58,181 59,181 60,181 61,181 62,182 63,182 64,182 65,182 66,182 67,182 68,182 69,182 70,182 71,182 72,181 73,181 74,181 75,181 76,180 77,180 78,180 78,179 79,178 80,178 81,177 82,176 82,175 83,174 84,173 85,172 85,171 86,170 87,170 88,169 88,168 89,167 89,166 90,165 90,164 90,163 91,162 91,161 91,160 91,159 91,158 92,157 92,156 92,155 92,154 92,153 92,152 92,151 92,150 92,149 92,148 92,147 92,146 92,145 92,144 91,143 91,142 91,141 90,140 90,139 90,138 90,137 90,136 89,135 88,134 88,133 88,132 88,131 89,130 89,129 90,128 90,127 90,126 91,125 91,124 92,123 92,122 93,121 94,120 95,119 96,118 97,117 98,117 99,116 100,115 101,114 102,114 103,113 104,112 105,111 106,110 107,109 107,108 108,107 109,106 109,105 110,104 110,103 111,102 111,101 112,100 112,99 112,98 112,97 113,96 113,95 113,94 113,93 114,92 114,91 114,90 114,89 114,88 114,87 114,86 114,85 114,84 114,83 114,82 114,81 114,80 114,79 114,78 114,77 114,76 114,75 114,74 114,73 114,72 114,71 114,70 114,69 114,68 114,67 113,66 113,65 112,64 112,63 112,62 112,61 112,60 111,59 111,58 110,57 110,56 109,56 108,56 107,55 106,54 106,53 105,52 104,52 103,51 102,51 101,51 100,50 99,49 98,48 97,48 96,47 95,47 94,47 93,47 92,47 91,47 90,47 89,47 88,47 87,47 86,47 85,47 84,47 83,47 82,47 81,47 80,47 79,47 ', 解析出327个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始327个点, 转换后327个点, 显示偏移(658.5,102.1), 原始偏移(1022.1,158.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='78,47 77,48 76,48 75,48 74,48 73,49 72,49 71,49 70,50 69,51 68,51 67,52 66,53 65,54 64,55 63,55 62,56 61,57 61,58 60,59 59,60 59,61 58,62 57,63 56,64 56,65 55,66 55,67 55,68 54,69 53,70 53,71 53,72 53,73 52,74 52,75 52,76 51,77 51,78 51,79 51,80 51,81 51,82 51,83 51,84 50,85 50,86 50,87 50,88 50,89 50,90 50,91 50,92 50,93 50,94 51,95 51,96 51,97 51,98 51,99 51,100 51,101 52,102 52,103 52,104 52,105 53,106 53,107 53,108 53,109 54,110 54,111 55,112 55,113 56,114 56,115 56,116 56,117 56,118 56,119 57,120 57,121 57,122 58,123 58,124 58,125 58,126 58,127 58,128 58,129 57,130 57,131 57,132 56,133 56,134 55,135 55,136 54,137 53,138 53,139 52,140 52,141 52,142 52,143 51,144 51,145 50,146 50,147 50,148 50,149 49,150 49,151 49,152 49,153 49,154 49,155 49,156 48,157 48,158 48,159 48,160 48,161 48,162 48,163 48,164 48,165 49,166 49,167 49,168 49,169 49,170 49,171 49,172 49,173 49,174 50,175 50,176 50,177 51,178 52,179 53,179 54,180 54,181 55,181 56,181 57,181 58,181 59,181 60,181 61,181 62,182 63,182 64,182 65,182 66,182 67,182 68,182 69,182 70,182 71,182 72,181 73,181 74,181 75,181 76,180 77,180 78,180 78,179 79,178 80,178 81,177 82,176 82,175 83,174 84,173 85,172 85,171 86,170 87,170 88,169 88,168 89,167 89,166 90,165 90,164 90,163 91,162 91,161 91,160 91,159 91,158 92,157 92,156 92,155 92,154 92,153 92,152 92,151 92,150 92,149 92,148 92,147 92,146 92,145 92,144 91,143 91,142 91,141 90,140 90,139 90,138 90,137 90,136 89,135 88,134 88,133 88,132 88,131 89,130 89,129 90,128 90,127 90,126 91,125 91,124 92,123 92,122 93,121 94,120 95,119 96,118 97,117 98,117 99,116 100,115 101,114 102,114 103,113 104,112 105,111 106,110 107,109 107,108 108,107 109,106 109,105 110,104 110,103 111,102 111,101 112,100 112,99 112,98 112,97 113,96 113,95 113,94 113,93 114,92 114,91 114,90 114,89 114,88 114,87 114,86 114,85 114,84 114,83 114,82 114,81 114,80 114,79 114,78 114,77 114,76 114,75 114,74 114,73 114,72 114,71 114,70 114,69 114,68 114,67 113,66 113,65 112,64 112,63 112,62 112,61 112,60 111,59 111,58 110,57 110,56 109,56 108,56 107,55 106,54 106,53 105,52 104,52 103,51 102,51 101,51 100,50 99,49 98,48 97,48 96,47 95,47 94,47 93,47 92,47 91,47 90,47 89,47 88,47 87,47 86,47 85,47 84,47 83,47 82,47 81,47 80,47 79,47 ', 解析出327个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始327个点, 转换后327个点, 显示偏移(658.5,102.1), 原始偏移(1022.1,158.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 精子4坐标计算: 原始(0.547,0.156)-(0.604,0.356), 显示(658.5,102.1)-(107.1x191.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制标签: 精子4, 位置(658.5,82.1)
2025-08-03 09:33:02,DEBUG: 精子5坐标计算: 原始(0.721,0.551)-(0.799,0.657), 显示(873.2,406.8)-(133.8x118.8), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制检测框: 精子5, 坐标(873.2,406.8), 尺寸(133.8x118.8)
2025-08-03 09:33:02,DEBUG: 精子5坐标计算: 原始(0.721,0.551)-(0.799,0.657), 显示(873.2,406.8)-(133.8x118.8), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='89,62 88,63 87,63 86,63 85,63 84,63 83,64 82,65 81,65 80,66 79,66 78,66 77,67 76,67 75,67 74,68 73,68 72,69 71,69 70,69 69,70 68,70 67,70 66,70 65,70 64,70 63,71 62,71 61,71 60,72 59,72 58,72 57,73 56,73 55,74 54,75 53,75 52,76 52,77 51,78 50,79 49,80 48,81 47,82 47,83 47,84 46,85 45,86 44,87 44,88 44,89 44,90 43,91 43,92 43,93 42,94 42,95 42,96 41,97 41,98 41,99 41,100 41,101 41,102 41,103 40,104 39,105 38,106 37,107 36,108 35,109 35,110 35,111 35,112 36,113 36,114 36,115 37,116 37,117 38,118 39,119 40,120 41,121 42,122 42,123 43,124 44,124 45,125 46,126 47,126 48,127 49,127 50,128 51,128 52,128 53,127 54,126 55,126 56,126 57,127 58,127 59,127 60,127 61,127 62,127 63,127 64,127 65,127 66,127 67,127 68,127 69,127 70,127 71,127 72,127 73,127 74,126 75,126 76,126 77,126 78,126 79,125 80,124 81,124 82,124 83,123 84,122 85,122 86,121 87,120 88,120 89,119 90,118 91,117 92,117 93,116 94,115 95,114 96,113 97,113 98,112 99,111 100,110 100,109 101,108 102,107 102,106 103,105 104,104 104,103 104,102 105,101 106,100 106,99 107,98 107,97 107,96 107,95 108,94 108,93 108,92 109,91 109,90 109,89 110,88 110,87 110,86 110,85 110,84 111,83 111,82 112,81 112,80 112,79 112,78 112,77 113,76 113,75 113,74 113,73 113,72 113,71 113,70 113,69 113,68 113,67 113,66 112,65 112,64 111,64 110,63 109,62 108,62 107,62 106,62 105,62 104,62 103,62 102,62 101,62 100,62 99,62 98,62 97,62 96,62 95,62 94,62 93,62 92,62 91,62 90,62 ', 解析出211个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始211个点, 转换后211个点, 显示偏移(873.2,406.8), 原始偏移(1355.3,631.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='40,104 39,105 38,106 37,107 36,108 35,109 35,110 35,111 35,112 36,113 36,114 36,115 37,116 37,117 38,118 39,119 40,120 41,121 42,122 42,123 43,124 44,124 45,125 46,126 47,126 48,127 49,127 50,128 51,128 52,128 53,127 53,126 53,125 54,124 55,123 56,122 57,121 56,120 55,119 54,118 53,117 52,116 51,115 51,114 50,113 50,112 49,111 48,110 47,109 46,108 45,107 44,106 43,105 42,104 41,105 ', 解析出55个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始55个点, 转换后55个点, 显示偏移(873.2,406.8), 原始偏移(1355.3,631.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='89,62 88,63 87,63 86,63 85,63 84,63 83,64 82,65 81,65 80,66 79,66 78,66 77,67 76,67 75,67 74,68 73,68 72,69 71,69 70,69 69,70 68,70 67,70 66,70 65,70 64,70 63,71 62,71 61,71 60,72 59,72 58,72 57,73 56,73 55,74 54,75 53,75 52,76 52,77 51,78 50,79 49,80 48,81 47,82 47,83 47,84 46,85 45,86 44,87 44,88 44,89 44,90 43,91 43,92 43,93 42,94 42,95 42,96 41,97 41,98 41,99 41,100 41,101 41,102 41,103 41,104 42,103 43,104 44,105 45,106 46,107 47,108 48,109 49,110 50,111 51,112 51,113 52,114 52,115 53,116 54,117 55,118 56,119 57,120 58,121 57,122 56,123 55,124 54,125 54,126 55,126 56,126 57,127 58,127 59,127 60,127 61,127 62,127 63,127 64,127 65,127 66,127 67,127 68,127 69,127 70,127 71,127 72,127 73,127 74,126 75,126 76,126 77,126 78,126 79,125 80,124 81,124 82,124 83,123 84,122 85,122 86,121 87,120 88,120 89,119 90,118 91,117 92,117 93,116 94,115 95,114 96,113 97,113 98,112 99,111 100,110 100,109 101,108 102,107 102,106 103,105 104,104 104,103 104,102 105,101 106,100 106,99 107,98 107,97 107,96 107,95 108,94 108,93 108,92 109,91 109,90 109,89 110,88 110,87 110,86 110,85 110,84 111,83 111,82 112,81 112,80 112,79 112,78 112,77 113,76 113,75 113,74 113,73 113,72 113,71 113,70 113,69 113,68 113,67 113,66 112,65 112,64 111,64 110,63 109,62 108,62 107,62 106,62 105,62 104,62 103,62 102,62 101,62 100,62 99,62 98,62 97,62 96,62 95,62 94,62 93,62 92,62 91,62 90,62 ', 解析出204个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始204个点, 转换后204个点, 显示偏移(873.2,406.8), 原始偏移(1355.3,631.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 精子5坐标计算: 原始(0.721,0.551)-(0.799,0.657), 显示(873.2,406.8)-(133.8x118.8), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制标签: 精子5, 位置(873.2,386.8)
2025-08-03 09:33:02,DEBUG: 精子7坐标计算: 原始(0.445,0.423)-(0.503,0.470), 显示(532.3,308.3)-(108.4x73.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制检测框: 精子7, 坐标(532.3,308.3), 尺寸(108.4x73.2)
2025-08-03 09:33:02,DEBUG: 精子7坐标计算: 原始(0.445,0.423)-(0.503,0.470), 显示(532.3,308.3)-(108.4x73.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='11,15 10,16 9,17 8,18 7,19 6,20 5,21 4,22 3,23 4,24 5,25 6,26 7,27 8,28 9,29 10,30 11,30 12,30 13,30 14,30 15,31 16,31 17,31 18,32 19,32 20,33 21,34 22,35 23,36 23,37 23,38 24,39 24,40 24,41 24,42 25,43 25,44 25,45 26,46 27,47 28,48 29,49 30,50 31,50 32,51 33,51 34,52 35,52 36,52 37,53 38,53 39,53 40,54 41,54 42,55 43,56 44,57 45,58 46,58 47,59 48,60 48,61 48,62 49,63 50,64 50,65 51,66 52,67 52,68 53,68 54,68 55,69 56,70 57,70 58,70 59,70 60,70 61,70 62,70 63,70 64,70 65,70 66,70 67,70 68,70 69,70 70,70 71,70 72,69 73,69 74,68 75,67 76,67 77,66 78,65 79,65 80,64 80,63 81,62 82,61 83,60 83,59 83,58 84,57 84,56 84,55 84,54 84,53 84,52 84,51 84,50 84,49 84,48 84,47 84,46 84,45 84,44 84,43 83,42 83,41 83,40 82,40 81,39 80,38 79,37 78,37 77,37 76,36 75,36 74,36 73,36 72,36 71,36 70,36 69,36 68,36 67,36 66,36 65,36 64,36 63,36 62,37 61,37 60,37 59,37 58,36 57,36 56,35 55,34 54,33 54,32 54,31 54,30 53,29 53,28 53,27 53,26 53,25 52,24 52,23 51,22 51,21 51,20 50,19 49,18 48,17 47,17 46,17 45,17 44,17 43,17 42,17 41,17 40,18 39,18 38,18 37,18 36,18 35,18 34,18 33,17 32,17 31,17 30,17 29,17 28,17 27,17 26,17 25,17 24,17 23,17 22,17 21,16 20,16 19,16 18,15 17,15 16,15 15,15 14,15 13,15 12,15 ', 解析出202个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始202个点, 转换后202个点, 显示偏移(532.3,308.3), 原始偏移(826.2,478.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='11,15 10,16 9,17 8,18 7,19 6,20 5,21 4,22 3,23 4,24 5,25 6,26 7,27 8,28 9,29 10,30 11,30 12,30 13,30 14,30 15,31 16,31 17,31 18,32 19,32 20,33 21,34 22,35 23,36 23,37 23,38 24,39 24,40 24,41 24,42 25,43 25,44 25,45 26,46 27,47 28,48 29,49 30,50 31,50 32,51 33,51 34,52 35,52 36,52 37,53 38,53 39,53 40,54 41,54 42,55 43,56 44,57 45,58 45,57 45,56 46,55 47,54 48,53 49,52 50,51 51,50 52,49 52,48 52,47 52,46 52,45 53,44 54,43 55,42 56,41 57,40 58,39 59,38 60,37 59,37 58,36 57,36 56,35 55,34 54,33 54,32 54,31 54,30 53,29 53,28 53,27 53,26 53,25 52,24 52,23 51,22 51,21 51,20 50,19 49,18 48,17 47,17 46,17 45,17 44,17 43,17 42,17 41,17 40,18 39,18 38,18 37,18 36,18 35,18 34,18 33,17 32,17 31,17 30,17 29,17 28,17 27,17 26,17 25,17 24,17 23,17 22,17 21,16 20,16 19,16 18,15 17,15 16,15 15,15 14,15 13,15 12,15 ', 解析出137个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始137个点, 转换后137个点, 显示偏移(532.3,308.3), 原始偏移(826.2,478.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 解析轮廓数据: 原始='63,36 62,37 61,37 60,38 59,39 58,40 57,41 56,42 55,43 54,44 53,45 53,46 53,47 53,48 53,49 52,50 51,51 50,52 49,53 48,54 47,55 46,56 46,57 46,58 47,59 48,60 48,61 48,62 49,63 50,64 50,65 51,66 52,67 52,68 53,68 54,68 55,69 56,70 57,70 58,70 59,70 60,70 61,70 62,70 63,70 64,70 65,70 66,70 67,70 68,70 69,70 70,70 71,70 72,69 73,69 74,68 75,67 76,67 77,66 78,65 79,65 80,64 80,63 81,62 82,61 83,60 83,59 83,58 84,57 84,56 84,55 84,54 84,53 84,52 84,51 84,50 84,49 84,48 84,47 84,46 84,45 84,44 84,43 83,42 83,41 83,40 82,40 81,39 80,38 79,37 78,37 77,37 76,36 75,36 74,36 73,36 72,36 71,36 70,36 69,36 68,36 67,36 66,36 65,36 64,36 ', 解析出105个点
2025-08-03 09:33:02,DEBUG: 绘制轮廓(带偏移): 原始105个点, 转换后105个点, 显示偏移(532.3,308.3), 原始偏移(826.2,478.4), 显示尺寸(1237.1x773.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 精子7坐标计算: 原始(0.445,0.423)-(0.503,0.470), 显示(532.3,308.3)-(108.4x73.2), 缩放:0.644
2025-08-03 09:33:02,DEBUG: 绘制标签: 精子7, 位置(532.3,288.3)
2025-08-03 09:33:02,INFO: 图像标记完成
2025-08-03 09:33:02,INFO: 保存分析结果到数据库...
2025-08-03 09:33:02,INFO: 分析完成 - 图像:2022_12_23_14_19_41_238.jpg, 总数:6, 正常:0, 异常:6
2025-08-03 09:33:38,INFO: 用户请求检查TensorFlow状态
2025-08-03 09:33:39,INFO: 用户请求检查TensorFlow状态
2025-08-03 09:33:40,INFO: 用户请求检查TensorFlow状态
2025-08-03 09:33:41,INFO: 用户请求检查TensorFlow状态
2025-08-03 09:34:18,INFO: 系统初始化完成
2025-08-03 09:34:18,INFO: 开始TensorFlow预加载...
2025-08-03 09:34:18,INFO: 开始初始化TensorFlow环境...
2025-08-03 09:34:18,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 09:34:18,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:34:18,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 09:34:18,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 09:34:18,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:34:18,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:34:18,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:34:18,INFO: 文件存在: libtensorflow.dll
2025-08-03 09:34:18,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 09:34:18,INFO: 文件存在: TFRunModel.dll
2025-08-03 09:34:18,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 09:34:18,INFO: 文件存在: TFCommon.dll
2025-08-03 09:34:18,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 09:34:18,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 09:34:18,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 09:34:18,INFO: 模型文件存在
2025-08-03 09:34:18,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 09:34:18,INFO: 配置文件存在
2025-08-03 09:34:18,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:34:18,INFO: 所有必要文件检查通过
2025-08-03 09:34:18,INFO: 已设置PATH环境变量
2025-08-03 09:34:18,INFO: TensorFlow环境变量设置完成
2025-08-03 09:34:18,INFO: 开始预加载TensorFlow模型...
2025-08-03 09:39:23,INFO: 系统初始化完成
2025-08-03 09:39:23,INFO: 开始TensorFlow预加载...
2025-08-03 09:39:23,INFO: 开始初始化TensorFlow环境...
2025-08-03 09:39:23,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 09:39:23,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:39:23,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 09:39:23,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 09:39:23,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:39:23,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:39:23,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:39:23,INFO: 文件存在: libtensorflow.dll
2025-08-03 09:39:23,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 09:39:23,INFO: 文件存在: TFRunModel.dll
2025-08-03 09:39:23,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 09:39:23,INFO: 文件存在: TFCommon.dll
2025-08-03 09:39:23,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 09:39:23,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 09:39:23,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 09:39:23,INFO: 模型文件存在
2025-08-03 09:39:23,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 09:39:23,INFO: 配置文件存在
2025-08-03 09:39:23,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:39:23,INFO: 所有必要文件检查通过
2025-08-03 09:39:23,INFO: 已设置PATH环境变量
2025-08-03 09:39:23,INFO: TensorFlow环境变量设置完成
2025-08-03 09:39:23,INFO: 开始预加载TensorFlow模型...
2025-08-03 09:39:33,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 09:39:33,INFO: TensorFlow初始化成功
2025-08-03 09:39:33,INFO: TensorFlow预加载成功
2025-08-03 09:39:50,INFO: 用户清空了日志显示
2025-08-03 09:39:53,INFO: 导入图像: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:39:53,INFO: 图像加载成功: 1920x1200
2025-08-03 09:39:53,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 09:39:59,INFO: 用户清空了日志显示
2025-08-03 09:40:01,INFO: 开始分析图像: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:40:01,INFO: 开始TensorFlow模型检测...
2025-08-03 09:40:01,INFO: 运行TensorFlow模型: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:40:02,INFO: 模型运行完成，检测到 7 个目标
2025-08-03 09:40:02,DEBUG: 性能统计 - 图像分析: 1207.07ms
2025-08-03 09:40:02,INFO: 分析完成，检测到 6 个精子
2025-08-03 09:40:02,INFO: 开始在图像上标记分析结果...
2025-08-03 09:40:02,DEBUG: 坐标计算示例: 原始(0.291,0.615), 显示(341.9,457.1), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 检测框示例: 精子1, 坐标(341.9,457.1), 尺寸(155.5x94.6)
2025-08-03 09:40:02,DEBUG: 坐标计算示例: 原始(0.291,0.615), 显示(341.9,457.1), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 238个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 238点 -> 偏移(530.6,709.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 127个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 127点 -> 偏移(530.6,709.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 214个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 214点 -> 偏移(530.6,709.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 坐标计算示例: 原始(0.291,0.615), 显示(341.9,457.1), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 238个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 238点 -> 偏移(822.4,796.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 165个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 165点 -> 偏移(822.4,796.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 162个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 162点 -> 偏移(822.4,796.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 141个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 141点 -> 偏移(784.4,690.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 141个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 141点 -> 偏移(784.4,690.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 327个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 327点 -> 偏移(1022.1,158.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 327个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 327点 -> 偏移(1022.1,158.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 211个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 211点 -> 偏移(1355.3,631.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 55个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 55点 -> 偏移(1355.3,631.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 204个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 204点 -> 偏移(1355.3,631.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 202个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 202点 -> 偏移(826.2,478.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 137个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 137点 -> 偏移(826.2,478.4), 缩放:0.644
2025-08-03 09:40:02,DEBUG: 解析轮廓: 105个点
2025-08-03 09:40:02,DEBUG: 轮廓转换: 105点 -> 偏移(826.2,478.4), 缩放:0.644
2025-08-03 09:40:02,INFO: 图像标记完成
2025-08-03 09:40:02,INFO: 保存分析结果到数据库...
2025-08-03 09:40:02,INFO: 分析完成 - 图像:2022_12_23_14_19_41_238.jpg, 总数:6, 正常:0, 异常:6
2025-08-03 09:40:12,ERROR: 复制日志失败: OpenClipboard 失败 (0x800401D0 (CLIPBRD_E_CANT_OPEN))
2025-08-03 09:51:02,INFO: 系统初始化完成
2025-08-03 09:51:02,INFO: 开始TensorFlow预加载...
2025-08-03 09:51:02,INFO: 开始初始化TensorFlow环境...
2025-08-03 09:51:02,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 09:51:02,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:51:02,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 09:51:02,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 09:51:02,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:51:02,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:51:02,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 09:51:02,INFO: 文件存在: libtensorflow.dll
2025-08-03 09:51:02,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 09:51:02,INFO: 文件存在: TFRunModel.dll
2025-08-03 09:51:02,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 09:51:02,INFO: 文件存在: TFCommon.dll
2025-08-03 09:51:02,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 09:51:02,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 09:51:02,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 09:51:02,INFO: 模型文件存在
2025-08-03 09:51:02,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 09:51:02,INFO: 配置文件存在
2025-08-03 09:51:02,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 09:51:02,INFO: 所有必要文件检查通过
2025-08-03 09:51:02,INFO: 已设置PATH环境变量
2025-08-03 09:51:02,INFO: TensorFlow环境变量设置完成
2025-08-03 09:51:02,INFO: 开始预加载TensorFlow模型...
2025-08-03 09:51:09,INFO: 导入图像: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:51:09,INFO: 图像加载成功: 1920x1200
2025-08-03 09:51:09,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 09:51:09,INFO: 开始分析图像: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:51:09,INFO: 开始TensorFlow模型检测...
2025-08-03 09:51:12,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 09:51:12,INFO: TensorFlow初始化成功
2025-08-03 09:51:12,INFO: 运行TensorFlow模型: 2022_12_23_14_19_41_238.jpg
2025-08-03 09:51:12,INFO: TensorFlow预加载成功
2025-08-03 09:51:13,INFO: 模型运行完成，检测到 7 个目标
2025-08-03 09:51:13,DEBUG: 性能统计 - 图像分析: 3796.64ms
2025-08-03 09:51:13,INFO: 分析完成，检测到 6 个精子
2025-08-03 09:51:13,INFO: 开始在图像上标记分析结果...
2025-08-03 09:51:13,DEBUG: getBox计算: 原始(0.291,0.615), 原始框(539.2,718.0), 尺寸(224.3x129.8)
2025-08-03 09:51:13,DEBUG: 显示坐标: (347.4,462.6), 尺寸(144.5x83.6), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 检测框示例: 精子1, 坐标(347.4,462.6), 尺寸(144.5x83.6)
2025-08-03 09:51:13,DEBUG: 解析轮廓: 238个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 238点 -> 偏移(539.2,718.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 127个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 127点 -> 偏移(539.2,718.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 214个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 214点 -> 偏移(539.2,718.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: getBox计算: 原始(0.291,0.615), 原始框(539.2,718.0), 尺寸(224.3x129.8)
2025-08-03 09:51:13,DEBUG: 显示坐标: (347.4,462.6), 尺寸(144.5x83.6), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 238个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 238点 -> 偏移(831.0,805.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 165个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 165点 -> 偏移(831.0,805.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 162个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 162点 -> 偏移(831.0,805.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 141个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 141点 -> 偏移(793.0,699.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 141个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 141点 -> 偏移(793.0,699.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 327个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 327点 -> 偏移(1030.6,167.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 327个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 327点 -> 偏移(1030.6,167.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 211个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 211点 -> 偏移(1363.8,640.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 55个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 55点 -> 偏移(1363.8,640.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 204个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 204点 -> 偏移(1363.8,640.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 202个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 202点 -> 偏移(834.8,487.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 137个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 137点 -> 偏移(834.8,487.0), 缩放:0.644
2025-08-03 09:51:13,DEBUG: 解析轮廓: 105个点
2025-08-03 09:51:13,DEBUG: 轮廓转换: 105点 -> 偏移(834.8,487.0), 缩放:0.644
2025-08-03 09:51:13,INFO: 图像标记完成
2025-08-03 09:51:13,INFO: 保存分析结果到数据库...
2025-08-03 09:51:13,INFO: 分析完成 - 图像:2022_12_23_14_19_41_238.jpg, 总数:6, 正常:0, 异常:6
2025-08-03 09:53:22,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 09:53:22,INFO: 图像加载成功: 1920x1200
2025-08-03 09:53:22,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 09:53:25,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 09:53:25,INFO: 开始TensorFlow模型检测...
2025-08-03 09:53:25,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 09:53:25,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 09:53:26,DEBUG: 性能统计 - 图像分析: 1072.97ms
2025-08-03 09:53:26,INFO: 分析完成，检测到 11 个精子
2025-08-03 09:53:26,INFO: 开始在图像上标记分析结果...
2025-08-03 09:53:26,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 09:53:26,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 09:53:26,DEBUG: 解析轮廓: 215个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 161个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 144个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 09:53:26,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 221个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 66个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 198个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 213个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 138个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 164个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 251个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 191个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 154个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 180个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 164个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 124个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 205个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 178个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 141个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 200个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 137个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 146个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 242个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 136个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 188个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 228个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 179个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 159个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 254个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 159个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 189个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 284个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 09:53:26,DEBUG: 解析轮廓: 284个点
2025-08-03 09:53:26,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 09:53:26,INFO: 图像标记完成
2025-08-03 09:53:26,INFO: 保存分析结果到数据库...
2025-08-03 09:53:26,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:01:22,INFO: 系统初始化完成
2025-08-03 10:01:22,INFO: 开始TensorFlow预加载...
2025-08-03 10:01:22,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:01:22,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:01:22,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:01:22,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:01:22,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:01:22,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:01:22,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:01:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:01:22,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:01:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:01:22,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:01:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:01:22,INFO: 文件存在: TFCommon.dll
2025-08-03 10:01:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:01:22,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:01:22,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:01:22,INFO: 模型文件存在
2025-08-03 10:01:22,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:01:22,INFO: 配置文件存在
2025-08-03 10:01:22,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:01:22,INFO: 所有必要文件检查通过
2025-08-03 10:01:22,INFO: 已设置PATH环境变量
2025-08-03 10:01:22,INFO: TensorFlow环境变量设置完成
2025-08-03 10:01:22,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:01:32,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:01:32,INFO: TensorFlow初始化成功
2025-08-03 10:01:32,INFO: TensorFlow预加载成功
2025-08-03 10:02:17,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:02:17,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:02:17,INFO: 图像加载成功: 1920x1200
2025-08-03 10:02:17,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:02:18,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:02:18,INFO: 开始TensorFlow模型检测...
2025-08-03 10:02:18,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:02:19,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:02:20,DEBUG: 性能统计 - 图像分析: 1323.32ms
2025-08-03 10:02:20,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:02:20,INFO: 开始在图像上标记分析结果...
2025-08-03 10:02:20,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:02:20,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:02:20,DEBUG: 解析轮廓: 215个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 161个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 144个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:02:20,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 221个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 66个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 198个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 213个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 138个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 164个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 251个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 191个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 154个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 180个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 164个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 124个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 205个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 178个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 141个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 200个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 137个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 146个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 242个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 136个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 188个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 228个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 179个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 159个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 254个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 159个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 189个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 284个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:02:20,DEBUG: 解析轮廓: 284个点
2025-08-03 10:02:20,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:02:20,INFO: 图像标记完成
2025-08-03 10:02:20,INFO: 保存分析结果到数据库...
2025-08-03 10:02:20,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:02:33,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:02:33,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:02:33,INFO: 图像加载成功: 1920x1200
2025-08-03 10:02:33,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:02:34,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:02:34,INFO: 开始TensorFlow模型检测...
2025-08-03 10:02:34,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:02:35,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:02:35,DEBUG: 性能统计 - 图像分析: 1024.35ms
2025-08-03 10:02:35,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:02:35,INFO: 开始在图像上标记分析结果...
2025-08-03 10:02:35,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:02:35,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:02:35,DEBUG: 解析轮廓: 215个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 161个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 144个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:02:35,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 221个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 66个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 198个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 213个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 138个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 164个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 251个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 191个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 154个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 180个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 164个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 124个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 205个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 178个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 141个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 200个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 137个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 146个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 242个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 136个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 188个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 228个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 179个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 159个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 254个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 159个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 189个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 284个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:02:35,DEBUG: 解析轮廓: 284个点
2025-08-03 10:02:35,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:02:35,INFO: 图像标记完成
2025-08-03 10:02:35,INFO: 保存分析结果到数据库...
2025-08-03 10:02:35,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:07:04,INFO: 系统初始化完成
2025-08-03 10:07:04,INFO: 开始TensorFlow预加载...
2025-08-03 10:07:04,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:07:04,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:07:04,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:07:04,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:07:04,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:07:04,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:07:04,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:07:04,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:07:04,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:07:04,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:07:04,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:07:04,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:07:04,INFO: 文件存在: TFCommon.dll
2025-08-03 10:07:04,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:07:04,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:07:04,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:07:04,INFO: 模型文件存在
2025-08-03 10:07:04,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:07:04,INFO: 配置文件存在
2025-08-03 10:07:04,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:07:04,INFO: 所有必要文件检查通过
2025-08-03 10:07:04,INFO: 已设置PATH环境变量
2025-08-03 10:07:04,INFO: TensorFlow环境变量设置完成
2025-08-03 10:07:04,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:07:11,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:07:11,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:07:12,INFO: 图像加载成功: 1920x1200
2025-08-03 10:07:12,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:07:13,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:07:13,INFO: 开始TensorFlow模型检测...
2025-08-03 10:07:13,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:07:13,INFO: TensorFlow初始化成功
2025-08-03 10:07:13,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:07:13,INFO: TensorFlow预加载成功
2025-08-03 10:07:14,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:07:14,DEBUG: 性能统计 - 图像分析: 1812.06ms
2025-08-03 10:07:14,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:07:14,INFO: 开始在图像上标记分析结果...
2025-08-03 10:07:14,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:07:14,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:07:14,DEBUG: 解析轮廓: 215个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 161个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 144个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:07:14,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 221个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 66个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 198个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 213个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 138个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 164个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 251个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 191个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 154个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 180个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 164个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 124个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 205个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 178个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 141个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 200个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 137个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 146个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 242个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 136个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 188个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 228个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 179个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 159个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 254个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 159个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 189个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 284个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:07:14,DEBUG: 解析轮廓: 284个点
2025-08-03 10:07:14,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:07:14,INFO: 图像标记完成
2025-08-03 10:07:14,INFO: 保存分析结果到数据库...
2025-08-03 10:07:14,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:11:45,INFO: 系统初始化完成
2025-08-03 10:11:45,INFO: 开始TensorFlow预加载...
2025-08-03 10:11:45,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:11:45,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:11:45,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:11:45,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:11:45,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:11:45,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:11:45,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:11:45,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:11:45,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:11:45,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:11:45,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:11:45,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:11:45,INFO: 文件存在: TFCommon.dll
2025-08-03 10:11:45,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:11:45,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:11:45,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:11:45,INFO: 模型文件存在
2025-08-03 10:11:45,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:11:45,INFO: 配置文件存在
2025-08-03 10:11:45,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:11:45,INFO: 所有必要文件检查通过
2025-08-03 10:11:45,INFO: 已设置PATH环境变量
2025-08-03 10:11:45,INFO: TensorFlow环境变量设置完成
2025-08-03 10:11:45,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:11:51,INFO: 用户清空了日志显示
2025-08-03 10:11:56,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:11:56,INFO: TensorFlow初始化成功
2025-08-03 10:12:01,INFO: TensorFlow预加载成功
2025-08-03 10:12:03,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:12:03,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:12:03,INFO: 图像加载成功: 1920x1200
2025-08-03 10:12:03,INFO: 图片显示设置: 原始(1920x1200) -> 显示(302.0x188.8), 缩放比例: 0.157
2025-08-03 10:12:05,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:12:05,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:12:07,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:12:07,INFO: 开始TensorFlow模型检测...
2025-08-03 10:12:07,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:12:08,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:12:08,DEBUG: 性能统计 - 图像分析: 1316.14ms
2025-08-03 10:12:08,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:12:08,INFO: 开始在图像上标记分析结果...
2025-08-03 10:12:08,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:12:08,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:12:08,DEBUG: 解析轮廓: 215个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 161个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 144个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:12:08,DEBUG:   vop_middlePiece: ''
2025-08-03 10:12:08,DEBUG:   vop_tail: ''
2025-08-03 10:12:08,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:12:08,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:12:08,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 221个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 66个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 198个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 213个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 138个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 164个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 251个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 191个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 154个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 180个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 164个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 124个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 205个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 178个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 141个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 200个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 137个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 146个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 242个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 136个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 188个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 228个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 179个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 159个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 254个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 159个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 189个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 284个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:12:08,DEBUG: 解析轮廓: 284个点
2025-08-03 10:12:08,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:12:08,INFO: 图像标记完成
2025-08-03 10:12:08,INFO: 保存分析结果到数据库...
2025-08-03 10:12:08,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:12:26,INFO: 用户清空了日志显示
2025-08-03 10:13:42,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:13:42,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:13:42,INFO: 图像加载成功: 1920x1200
2025-08-03 10:13:42,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:13:44,INFO: 用户清空了日志显示
2025-08-03 10:13:46,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:13:46,INFO: 开始TensorFlow模型检测...
2025-08-03 10:13:46,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:13:46,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:13:47,DEBUG: 性能统计 - 图像分析: 1079.98ms
2025-08-03 10:13:47,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:13:47,INFO: 开始在图像上标记分析结果...
2025-08-03 10:13:47,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:13:47,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:13:47,DEBUG: 解析轮廓: 215个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 161个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 144个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:13:47,DEBUG:   vop_middlePiece: ''
2025-08-03 10:13:47,DEBUG:   vop_tail: ''
2025-08-03 10:13:47,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:13:47,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:13:47,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 221个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 66个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 198个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 213个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 138个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 164个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 251个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 191个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 154个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 180个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 164个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 124个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 205个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 178个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 141个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 200个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 137个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 146个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 242个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 136个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 188个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 228个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 179个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 159个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 254个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 159个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 189个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 284个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:13:47,DEBUG: 解析轮廓: 284个点
2025-08-03 10:13:47,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:13:47,INFO: 图像标记完成
2025-08-03 10:13:47,INFO: 保存分析结果到数据库...
2025-08-03 10:13:47,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:15:42,INFO: 系统初始化完成
2025-08-03 10:15:42,INFO: 开始TensorFlow预加载...
2025-08-03 10:15:42,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:15:42,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:15:42,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:15:42,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:15:42,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:15:42,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:15:42,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:15:42,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:15:42,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:15:42,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:15:42,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:15:42,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:15:42,INFO: 文件存在: TFCommon.dll
2025-08-03 10:15:42,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:15:42,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:15:42,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:15:42,INFO: 模型文件存在
2025-08-03 10:15:42,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:15:42,INFO: 配置文件存在
2025-08-03 10:15:42,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:15:42,INFO: 所有必要文件检查通过
2025-08-03 10:15:42,INFO: 已设置PATH环境变量
2025-08-03 10:15:42,INFO: TensorFlow环境变量设置完成
2025-08-03 10:15:42,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:15:51,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:15:51,INFO: TensorFlow初始化成功
2025-08-03 10:15:51,INFO: TensorFlow预加载成功
2025-08-03 10:16:50,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:16:50,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:16:50,INFO: 图像加载成功: 1920x1200
2025-08-03 10:16:50,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:16:51,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:16:51,INFO: 开始TensorFlow模型检测...
2025-08-03 10:16:51,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:16:52,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:16:52,DEBUG: 生成尾巴数据: 精子1
2025-08-03 10:16:52,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:16:52,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:16:52,DEBUG: 性能统计 - 图像分析: 1297.92ms
2025-08-03 10:16:52,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:16:52,INFO: 开始在图像上标记分析结果...
2025-08-03 10:16:52,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:16:52,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:16:52,DEBUG: 解析轮廓: 215个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 161个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 144个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:16:52,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:16:52,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:16:52,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:16:52,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 221个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 66个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 198个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 213个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 138个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 164个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 251个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 191个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 154个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 180个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 164个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 124个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 205个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 178个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 141个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 200个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 137个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 146个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 242个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 136个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 188个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 228个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 179个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 159个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 254个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 159个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 189个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 解析轮廓: 284个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 解析轮廓: 284个点
2025-08-03 10:16:52,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:16:52,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:16:52,INFO: 图像标记完成
2025-08-03 10:16:52,INFO: 保存分析结果到数据库...
2025-08-03 10:16:52,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:17:22,INFO: 系统初始化完成
2025-08-03 10:17:22,INFO: 开始TensorFlow预加载...
2025-08-03 10:17:22,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:17:22,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:17:22,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:17:22,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:17:22,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:17:22,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:17:22,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:17:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:17:22,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:17:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:17:22,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:17:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:17:22,INFO: 文件存在: TFCommon.dll
2025-08-03 10:17:22,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:17:22,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:17:22,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:17:22,INFO: 模型文件存在
2025-08-03 10:17:22,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:17:22,INFO: 配置文件存在
2025-08-03 10:17:22,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:17:22,INFO: 所有必要文件检查通过
2025-08-03 10:17:22,INFO: 已设置PATH环境变量
2025-08-03 10:17:22,INFO: TensorFlow环境变量设置完成
2025-08-03 10:17:23,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:17:27,INFO: 用户清空了日志显示
2025-08-03 10:17:30,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:17:30,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:17:30,INFO: 图像加载成功: 1920x1200
2025-08-03 10:17:30,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:17:31,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:17:31,INFO: 开始TensorFlow模型检测...
2025-08-03 10:17:31,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:17:31,INFO: TensorFlow初始化成功
2025-08-03 10:17:31,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:17:31,INFO: TensorFlow预加载成功
2025-08-03 10:17:32,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:17:32,DEBUG: 生成尾巴数据: 精子1
2025-08-03 10:17:32,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:17:32,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:17:32,DEBUG: 性能统计 - 图像分析: 1113.47ms
2025-08-03 10:17:32,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:17:32,INFO: 开始在图像上标记分析结果...
2025-08-03 10:17:32,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:17:32,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:17:32,DEBUG: 解析轮廓: 215个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 161个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 144个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:17:32,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:17:32,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:17:32,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:17:32,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 221个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 66个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 198个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 213个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 138个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 164个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 251个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 191个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 154个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 180个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 164个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 124个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 205个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 178个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 141个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 200个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 137个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 146个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 242个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 136个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 188个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 228个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 179个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 159个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 254个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 159个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 189个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 解析轮廓: 284个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 解析轮廓: 284个点
2025-08-03 10:17:32,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:32,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:32,INFO: 图像标记完成
2025-08-03 10:17:32,INFO: 保存分析结果到数据库...
2025-08-03 10:17:32,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:17:38,INFO: 用户清空了日志显示
2025-08-03 10:17:39,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:17:39,INFO: 开始TensorFlow模型检测...
2025-08-03 10:17:39,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:17:40,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:17:40,DEBUG: 生成尾巴数据: 精子1
2025-08-03 10:17:40,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:17:40,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:17:40,DEBUG: 性能统计 - 图像分析: 986.24ms
2025-08-03 10:17:40,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:17:40,INFO: 开始在图像上标记分析结果...
2025-08-03 10:17:40,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:17:40,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:17:40,DEBUG: 解析轮廓: 215个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 161个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 144个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:17:40,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:17:40,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:17:40,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:17:40,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 221个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 66个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 198个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 213个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 138个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 164个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 251个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 191个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 154个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 180个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 164个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 124个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 205个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 178个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 141个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 200个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 137个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 146个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 242个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 136个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 188个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 228个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 179个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 159个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 254个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 159个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 189个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 解析轮廓: 284个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 解析轮廓: 284个点
2025-08-03 10:17:40,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:17:40,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:17:40,INFO: 图像标记完成
2025-08-03 10:17:40,INFO: 保存分析结果到数据库...
2025-08-03 10:17:40,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:22:29,INFO: 系统初始化完成
2025-08-03 10:22:29,INFO: 开始TensorFlow预加载...
2025-08-03 10:22:29,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:22:29,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:22:29,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:22:29,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:22:29,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:22:29,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:22:29,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:22:29,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:22:29,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:22:29,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:22:29,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:22:29,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:22:29,INFO: 文件存在: TFCommon.dll
2025-08-03 10:22:29,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:22:29,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:22:29,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:22:29,INFO: 模型文件存在
2025-08-03 10:22:29,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:22:29,INFO: 配置文件存在
2025-08-03 10:22:29,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:22:29,INFO: 所有必要文件检查通过
2025-08-03 10:22:29,INFO: 已设置PATH环境变量
2025-08-03 10:22:29,INFO: TensorFlow环境变量设置完成
2025-08-03 10:22:29,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:22:41,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:22:41,INFO: TensorFlow初始化成功
2025-08-03 10:22:41,INFO: TensorFlow预加载成功
2025-08-03 10:22:49,INFO: 用户清空了日志显示
2025-08-03 10:22:52,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:22:52,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:22:52,INFO: 图像加载成功: 1920x1200
2025-08-03 10:22:52,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:22:54,INFO: 用户清空了日志显示
2025-08-03 10:22:55,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:22:55,INFO: 开始TensorFlow模型检测...
2025-08-03 10:22:55,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:22:56,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:22:56,DEBUG: 开始处理尾巴数据，精子数量: 11
2025-08-03 10:22:56,DEBUG: 生成尾巴数据: 精子1
2025-08-03 10:22:56,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:22:56,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:22:56,DEBUG: 尾巴数据处理完成
2025-08-03 10:22:56,DEBUG: 性能统计 - 图像分析: 1301.11ms
2025-08-03 10:22:56,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:22:56,INFO: 开始在图像上标记分析结果...
2025-08-03 10:22:56,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:22:56,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:22:56,DEBUG: 解析轮廓: 215个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 161个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 144个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:22:56,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5' (长度:79)
2025-08-03 10:22:56,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5' (长度:151)
2025-08-03 10:22:56,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:22:56,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 221个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 66个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 198个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 213个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 138个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 164个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 251个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 191个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 154个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 180个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 164个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 124个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 205个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 178个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 141个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 200个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 137个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 146个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 242个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 136个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 188个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 228个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 179个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 159个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 254个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 159个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 189个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 解析轮廓: 284个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 解析轮廓: 284个点
2025-08-03 10:22:56,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:22:56,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:22:56,INFO: 图像标记完成
2025-08-03 10:22:56,INFO: 保存分析结果到数据库...
2025-08-03 10:22:56,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:25:50,INFO: 系统初始化完成
2025-08-03 10:25:50,INFO: 开始TensorFlow预加载...
2025-08-03 10:25:50,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:25:50,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:25:50,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:25:50,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:25:50,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:25:50,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:25:50,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:25:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:25:50,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:25:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:25:50,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:25:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:25:50,INFO: 文件存在: TFCommon.dll
2025-08-03 10:25:50,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:25:50,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:25:50,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:25:50,INFO: 模型文件存在
2025-08-03 10:25:50,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:25:50,INFO: 配置文件存在
2025-08-03 10:25:50,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:25:50,INFO: 所有必要文件检查通过
2025-08-03 10:25:50,INFO: 已设置PATH环境变量
2025-08-03 10:25:50,INFO: TensorFlow环境变量设置完成
2025-08-03 10:25:50,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:26:02,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:26:02,INFO: TensorFlow初始化成功
2025-08-03 10:26:02,INFO: TensorFlow预加载成功
2025-08-03 10:26:15,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:26:15,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:26:15,INFO: 图像加载成功: 1920x1200
2025-08-03 10:26:15,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:26:16,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:26:16,INFO: 开始TensorFlow模型检测...
2025-08-03 10:26:16,DEBUG: SpermAnalysisService.AnalyzeImage 被调用: D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\bin\x64\Debug\images\20240411\20240411-1\2024_04_11_14_45_14_087.jpg
2025-08-03 10:26:16,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:26:16,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:26:17,DEBUG: 开始处理尾巴数据，精子数量: 11
2025-08-03 10:26:17,DEBUG: 生成尾巴数据: 精子1
2025-08-03 10:26:17,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:26:17,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:26:17,DEBUG: 尾巴数据处理完成
2025-08-03 10:26:17,DEBUG: 性能统计 - 图像分析: 1306.35ms
2025-08-03 10:26:17,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:26:17,INFO: 开始在图像上标记分析结果...
2025-08-03 10:26:17,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:26:17,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:26:17,DEBUG: 解析轮廓: 215个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 161个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 144个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:26:17,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5' (长度:79)
2025-08-03 10:26:17,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5' (长度:151)
2025-08-03 10:26:17,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:26:17,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 221个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 66个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 198个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 213个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 138个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 164个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 251个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 191个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 154个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 180个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 164个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 124个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 205个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 178个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 141个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 200个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 137个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 146个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 242个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 136个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 188个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 228个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 179个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 159个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 254个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 159个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 189个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 解析轮廓: 284个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 解析轮廓: 284个点
2025-08-03 10:26:17,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:26:17,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:26:17,INFO: 图像标记完成
2025-08-03 10:26:17,INFO: 保存分析结果到数据库...
2025-08-03 10:26:17,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
2025-08-03 10:29:32,INFO: 系统初始化完成
2025-08-03 10:29:32,INFO: 开始TensorFlow预加载...
2025-08-03 10:29:32,INFO: 开始初始化TensorFlow环境...
2025-08-03 10:29:32,INFO: 当前工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF
2025-08-03 10:29:32,INFO: 应用程序基目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:29:32,INFO: 程序集目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64
2025-08-03 10:29:32,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\libtensorflow.dll
2025-08-03 10:29:32,INFO: 测试目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\, 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:29:32,INFO: 找到正确的工作目录: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:29:32,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\libtensorflow.dll
2025-08-03 10:29:32,INFO: 文件存在: libtensorflow.dll
2025-08-03 10:29:32,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFRunModel.dll
2025-08-03 10:29:32,INFO: 文件存在: TFRunModel.dll
2025-08-03 10:29:32,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TFCommon.dll
2025-08-03 10:29:32,INFO: 文件存在: TFCommon.dll
2025-08-03 10:29:32,INFO: 检查文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\TensorFlowSharp.dll
2025-08-03 10:29:32,INFO: 文件存在: TensorFlowSharp.dll
2025-08-03 10:29:32,INFO: 检查模型文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\tfmodel\frozen_inference_graph_mrf.pb
2025-08-03 10:29:32,INFO: 模型文件存在
2025-08-03 10:29:32,INFO: 检查配置文件: D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\configure\manager.config
2025-08-03 10:29:32,INFO: 配置文件存在
2025-08-03 10:29:32,INFO: 设置工作目录从 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF 到 D:\code\SpermFormAnalysis-界面优化版本0802\SpermAnalysisWPF\SpermAnalysisWPF\bin\Debug\net8.0-windows\win-x64\
2025-08-03 10:29:32,INFO: 所有必要文件检查通过
2025-08-03 10:29:32,INFO: 已设置PATH环境变量
2025-08-03 10:29:32,INFO: TensorFlow环境变量设置完成
2025-08-03 10:29:32,INFO: 开始预加载TensorFlow模型...
2025-08-03 10:29:40,INFO: 模型预加载成功，检测到 5 个目标
2025-08-03 10:29:40,INFO: TensorFlow初始化成功
2025-08-03 10:29:40,INFO: TensorFlow预加载成功
2025-08-03 10:29:41,INFO: 导入图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:29:41,DEBUG: 已清除所有绘制痕迹
2025-08-03 10:29:41,INFO: 图像加载成功: 1920x1200
2025-08-03 10:29:41,INFO: 图片显示设置: 原始(1920x1200) -> 显示(1237.1x773.2), 缩放比例: 0.644
2025-08-03 10:29:43,INFO: 开始分析图像: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:29:43,INFO: 开始TensorFlow模型检测...
2025-08-03 10:29:43,DEBUG: SpermAnalysisService.AnalyzeImage 被调用: D:\code\SpermFormAnalysis-界面优化版本0802\SpermFormAnalysis\bin\x64\Debug\images\20240411\20240411-1\2024_04_11_14_45_14_087.jpg
2025-08-03 10:29:43,INFO: 运行TensorFlow模型: 2024_04_11_14_45_14_087.jpg
2025-08-03 10:29:43,INFO: 模型运行完成，检测到 11 个目标
2025-08-03 10:29:44,DEBUG: 开始处理尾巴数据，精子数量: 11
2025-08-03 10:29:44,DEBUG: 生成尾巴数据: 精子1
2025-08-03 10:29:44,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5'
2025-08-03 10:29:44,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5'
2025-08-03 10:29:44,DEBUG: 尾巴数据处理完成
2025-08-03 10:29:44,DEBUG: 性能统计 - 图像分析: 1195.79ms
2025-08-03 10:29:44,INFO: 分析完成，检测到 11 个精子
2025-08-03 10:29:44,INFO: 开始在图像上标记分析结果...
2025-08-03 10:29:44,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:29:44,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 检测框示例: 精子1, 坐标(132.0,315.7), 尺寸(115.1x76.8)
2025-08-03 10:29:44,DEBUG: 解析轮廓: 215个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 215点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 161个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 161点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 144个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 144点 -> 偏移(204.9,490.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 我的项目尾巴数据: 精子1
2025-08-03 10:29:44,DEBUG:   vop_middlePiece: '-10.8,-7.5;-1.8,10.5;6.2,8.5;-5.8,23.5;-15.8,22.5;-18.8,19.5;-7.8,39.5;7.2,38.5' (长度:79)
2025-08-03 10:29:44,DEBUG:   vop_tail: '-6.8,40.5;3.2,48.5;6.2,56.5;6.2,64.5;13.2,72.5;-12.8,80.5;-10.8,88.5;-3.8,96.5;8.2,104.5;-9.8,112.5;8.2,120.5;-5.8,128.5;9.2,136.5;11.2,144.5;1.2,152.5' (长度:151)
2025-08-03 10:29:44,DEBUG:   偏移量: (204.9,490.0)
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: getBox计算: 原始(0.117,0.425), 原始框(204.9,490.0), 尺寸(178.6x119.2)
2025-08-03 10:29:44,DEBUG: 显示坐标: (132.0,315.7), 尺寸(115.1x76.8), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 221个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 221点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 66个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 66点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 198个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 198点 -> 偏移(1401.7,1012.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 213个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 213点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 138个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 138点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 164个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 164点 -> 偏移(773.7,710.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 251个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 251点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 191个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 191点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 154个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 154点 -> 偏移(1321.5,743.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 180个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 180点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 164个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 164点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 124个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 124点 -> 偏移(274.8,390.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 205个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 205点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 178个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 178点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 141个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 141点 -> 偏移(1244.6,177.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 200个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 200点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 137个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 137点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 146个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 146点 -> 偏移(1674.7,772.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 242个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 242点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 136个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 136点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 188个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 188点 -> 偏移(1743.5,9.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 228个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 228点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 179个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 179点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 159个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 159点 -> 偏移(1147.9,0.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 254个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 254点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 159个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 159点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 189个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 189点 -> 偏移(289.2,326.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 解析轮廓: 284个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 解析轮廓: 284个点
2025-08-03 10:29:44,DEBUG: 轮廓转换: 284点 -> 偏移(1258.2,750.0), 缩放:0.644
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,DEBUG: 尾巴解析结果: 0个点
2025-08-03 10:29:44,DEBUG: 尾巴点数不足(0<2)，跳过绘制
2025-08-03 10:29:44,INFO: 图像标记完成
2025-08-03 10:29:44,INFO: 保存分析结果到数据库...
2025-08-03 10:29:44,INFO: 分析完成 - 图像:2024_04_11_14_45_14_087.jpg, 总数:11, 正常:0, 异常:11
