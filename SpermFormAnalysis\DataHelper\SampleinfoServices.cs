﻿using MySql.Data.MySqlClient;
using SpermFormAnalysis.Model;
using SpermFormAnalysis.utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Configuration;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.SQLite;

namespace SpermFormAnalysis.DataHelper
{
    public static class SampleinfoServices
    {
        private static string connectionString = @"Data Source=" + Application.StartupPath + @"\database\morsperm.db;Initial Catalog=sqlite;Integrated Security=True; =10";

        public static Sampleinfo GetObject(int id)
        {
            Sampleinfo sampleinfo = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                conn.BusyTimeout = 3;
                SQLiteCommand cmd = new SQLiteCommand("select * from sampleinfo where id=@id", conn);
                cmd.Parameters.AddWithValue("@id", id);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        sampleinfo = new Sampleinfo();
                        bool flag2 = !Convert.IsDBNull(dr["sendtime"]);
                        if (flag2)
                        {
                            sampleinfo.sendtime = dr["sendtime"].ToString();
                        }
                        bool flag3 = !Convert.IsDBNull(dr["sampleId"]);
                        if (flag3)
                        {
                            sampleinfo.sampleId = Convert.ToString(dr["sampleId"]);
                        }
                        string a = "";
                        bool flag4 = !Convert.IsDBNull(dr["checkcode"]);
                        if (flag4)
                        {
                            a = Convert.ToString(dr["checkcode"]);
                        }
                        string b = Crypto.DES.MD5(sampleinfo.sampleId + sampleinfo.sendtime.ToString());
                        bool flag5 = Basic.useCheckcode && a != b;
                        if (flag5)
                        {
                            //result = null;
                        }
                        else
                        {
                            sampleinfo.Id = Convert.ToInt32(dr["id"]);
                            sampleinfo.patientId = Convert.ToString(dr["patientId"]);
                            sampleinfo.patientName = Convert.ToString(dr["patientName"]);
                            sampleinfo.patientage = Convert.ToInt32(dr["patientage"]);
                            sampleinfo.inspectItem = Convert.ToString(dr["inspectItem"]);
                            string inspectItem = sampleinfo.inspectItem;
                            if (!(inspectItem == "MRF"))
                            {
                                if (!(inspectItem == "DFI"))
                                {
                                    if (inspectItem == "MAT")
                                    {
                                        sampleinfo.inspectItemCHN = "核成熟度";
                                    }
                                }
                                else
                                {
                                    sampleinfo.inspectItemCHN = "DFI";
                                }
                            }
                            else
                            {
                                sampleinfo.inspectItemCHN = "形态";
                            }
                            sampleinfo.getSpermWay = Convert.ToString(dr["getSpermWay"]);
                            sampleinfo.abstinenceDays = Convert.ToInt32(dr["abstinenceDays"]);
                            sampleinfo.dilutionRatio = Convert.ToDouble(dr["dilutionRatio"]);
                            sampleinfo.sampleSource = Convert.ToString(dr["sampleSource"]);
                            sampleinfo.department = Convert.ToString(dr["department"]);
                            sampleinfo.inspectionDoctor = Convert.ToString(dr["inspectionDoctor"]);
                            bool flag6 = !Convert.IsDBNull(dr["userid"]);
                            if (flag6)
                            {
                                sampleinfo.userid = Convert.ToInt32(dr["userid"]);
                            }
                            sampleinfo.sendDoctor = Convert.ToString(dr["sendDoctor"]);
                            bool flagscaned = !Convert.IsDBNull(dr["scanned"]);
                            if (flagscaned)
                            {
                                sampleinfo.scanned = Convert.ToInt32(dr["scanned"]);
                            }
                            bool flag7 = !Convert.IsDBNull(dr["scantime"]);
                            if (flag7)
                            {
                                sampleinfo.scantime = dr["scantime"].ToString();
                            }
                            sampleinfo.scanmode = Convert.ToString(dr["scanmode"]);
                            bool flagpro = !Convert.IsDBNull(dr["processed"]);
                            if (flagpro)
                            {
                                sampleinfo.processed = Convert.ToInt32(dr["processed"]);
                            }
                            bool flag8 = !Convert.IsDBNull(dr["processed_time"]);
                            if (flag8)
                            {
                                sampleinfo.processed_time = dr["processed_time"].ToString();
                            }
                            bool flagrev = !Convert.IsDBNull(dr["reviewed"]);
                            if (flagrev)
                            {
                                sampleinfo.reviewed = Convert.ToInt32(dr["reviewed"]);
                            }
                            sampleinfo.reviewer = Convert.ToString(dr["reviewer"]);
                            bool flag9 = !Convert.IsDBNull(dr["review_time"]);
                            if (flag9)
                            {
                                sampleinfo.review_time = dr["review_time"].ToString();
                            }
                            sampleinfo.processed_np = dr["processed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["processed_np"]);
                            bool flag10 = !Convert.IsDBNull(dr["processed_np_time"]);
                            if (flag10)
                            {
                                sampleinfo.processed_np_time = dr["processed_np_time"].ToString();
                            }
                            sampleinfo.reviewed_np = dr["reviewed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reviewed_np"]);
                            sampleinfo.reviewer_np = Convert.ToString(dr["reviewer_np"]);
                            bool flag11 = !Convert.IsDBNull(dr["review_np_time"]);
                            if (flag11)
                            {
                                sampleinfo.review_np_time = dr["review_np_time"].ToString();
                            }
                            sampleinfo.reportprinted = dr["reportprinted"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reportprinted"]);
                            bool flag12 = !Convert.IsDBNull(dr["reporttime"]);
                            if (flag12)
                            {
                                sampleinfo.reporttime = dr["reporttime"].ToString();
                            }
                            sampleinfo.reportcontent = Convert.ToString(dr["reportcontent"]);
                            sampleinfo.diagnosis = Convert.ToString(dr["diagnosis"]);
                            sampleinfo.purpose = Convert.ToString(dr["purpose"]);
                            bool flag13 = !Convert.IsDBNull(dr["field_num"]);
                            if (flag13)
                            {
                                sampleinfo.field_num = Convert.ToInt32(dr["field_num"]);
                            }
                            bool flag14 = !Convert.IsDBNull(dr["sperm_num"]);
                            if (flag14)
                            {
                                sampleinfo.sperm_num = Convert.ToInt32(dr["sperm_num"]);
                            }
                            bool flag15 = !Convert.IsDBNull(dr["normal_num"]);
                            if (flag15)
                            {
                                sampleinfo.normal_num = Convert.ToInt32(dr["normal_num"]);
                            }
                            bool flag16 = !Convert.IsDBNull(dr["abnormal_num"]);
                            if (flag16)
                            {
                                sampleinfo.abnormal_num = Convert.ToInt32(dr["abnormal_num"]);
                            }
                            bool flag17 = !Convert.IsDBNull(dr["subclinical_num"]);
                            if (flag17)
                            {
                                sampleinfo.subclinical_num = Convert.ToInt32(dr["subclinical_num"]);
                            }
                            bool flag18 = !Convert.IsDBNull(dr["qualification_rate"]);
                            if (flag18)
                            {
                                sampleinfo.qualification_rate = Convert.ToDouble(dr["qualification_rate"]);
                                sampleinfo.qualification_rate= 1.0 * (double)sampleinfo.normal_num/(double)sampleinfo.sperm_num;
                                sampleinfo.abnormal_rate = 1.0 * (double)sampleinfo.abnormal_num / (double)sampleinfo.sperm_num;
                                sampleinfo.subclinical_rate = 1.0 * (double)sampleinfo.subclinical_num / (double)sampleinfo.sperm_num;
                                sampleinfo.totalPercent = 1.0;
                            }
                            bool flag19 = !Convert.IsDBNull(dr["normalhead_num"]);
                            if (flag19)
                            {
                                sampleinfo.normalhead_num = Convert.ToInt32(dr["normalhead_num"]);
                            }
                            bool flag20 = !Convert.IsDBNull(dr["microhead_num"]);
                            if (flag20)
                            {
                                sampleinfo.microhead_num = Convert.ToInt32(dr["microhead_num"]);
                            }
                            bool flag21 = !Convert.IsDBNull(dr["macrohead_num"]);
                            if (flag21)
                            {
                                sampleinfo.macrohead_num = Convert.ToInt32(dr["macrohead_num"]);
                            }
                            bool flag22 = !Convert.IsDBNull(dr["normalheight_num"]);
                            if (flag22)
                            {
                                sampleinfo.normalheight_num = Convert.ToInt32(dr["normalheight_num"]);
                            }
                            bool flag23 = !Convert.IsDBNull(dr["tallhead_num"]);
                            if (flag23)
                            {
                                sampleinfo.tallhead_num = Convert.ToInt32(dr["tallhead_num"]);
                            }
                            bool flag24 = !Convert.IsDBNull(dr["shorthead_num"]);
                            if (flag24)
                            {
                                sampleinfo.shorthead_num = Convert.ToInt32(dr["shorthead_num"]);
                            }
                            bool flag25 = !Convert.IsDBNull(dr["normalwidth_num"]);
                            if (flag25)
                            {
                                sampleinfo.normalwidth_num = Convert.ToInt32(dr["normalwidth_num"]);
                            }
                            bool flag26 = !Convert.IsDBNull(dr["fathead_num"]);
                            if (flag26)
                            {
                                sampleinfo.fathead_num = Convert.ToInt32(dr["fathead_num"]);
                            }
                            bool flag27 = !Convert.IsDBNull(dr["thinhead_num"]);
                            if (flag27)
                            {
                                sampleinfo.thinhead_num = Convert.ToInt32(dr["thinhead_num"]);
                            }
                            bool flag28 = !Convert.IsDBNull(dr["normalratio_num"]);
                            if (flag28)
                            {
                                sampleinfo.normalratio_num = Convert.ToInt32(dr["normalratio_num"]);
                            }
                            bool flag29 = !Convert.IsDBNull(dr["sharphead_num"]);
                            if (flag29)
                            {
                                sampleinfo.sharphead_num = Convert.ToInt32(dr["sharphead_num"]);
                            }
                            bool flag30 = !Convert.IsDBNull(dr["roundhead_num"]);
                            if (flag30)
                            {
                                sampleinfo.roundhead_num = Convert.ToInt32(dr["roundhead_num"]);
                            }
                            bool flag31 = !Convert.IsDBNull(dr["normalkernel_num"]);
                            if (flag31)
                            {
                                sampleinfo.normalkernel_num = Convert.ToInt32(dr["normalkernel_num"]);
                                sampleinfo.abnormalkernel_num = sampleinfo.sperm_num - sampleinfo.normalkernel_num;
                            }
                            bool flag32 = !Convert.IsDBNull(dr["normalacrosome_num"]);
                            if (flag32)
                            {
                                sampleinfo.normalacrosome_num = Convert.ToInt32(dr["normalacrosome_num"]);
                                sampleinfo.abnormalacrosome_num = sampleinfo.sperm_num - sampleinfo.normalacrosome_num;
                            }
                            bool flag33 = !Convert.IsDBNull(dr["bigacrosome_num"]);
                            if (flag33)
                            {
                                sampleinfo.bigacrosome_num = Convert.ToInt32(dr["bigacrosome_num"]);
                            }
                            bool flag34 = !Convert.IsDBNull(dr["smallacrosome_num"]);
                            if (flag34)
                            {
                                sampleinfo.smallacrosome_num = Convert.ToInt32(dr["smallacrosome_num"]);
                            }
                            bool flag35 = !Convert.IsDBNull(dr["noacrosome_num"]);
                            if (flag35)
                            {
                                sampleinfo.noacrosome_num = Convert.ToInt32(dr["noacrosome_num"]);
                            }
                            bool flag36 = !Convert.IsDBNull(dr["normalvacuoles_num"]);
                            if (flag36)
                            {
                                sampleinfo.normalvacuoles_num = Convert.ToInt32(dr["normalvacuoles_num"]);
                            }
                            bool flag37 = !Convert.IsDBNull(dr["abnormalvacuoles_num"]);
                            if (flag37)
                            {
                                sampleinfo.abnormalvacuoles_num = Convert.ToInt32(dr["abnormalvacuoles_num"]);
                            }
                            bool flag38 = !Convert.IsDBNull(dr["_0vacuoles_num"]);
                            if (flag38)
                            {
                                sampleinfo._0vacuoles_num = Convert.ToInt32(dr["_0vacuoles_num"]);
                            }
                            bool flag39 = !Convert.IsDBNull(dr["_12vacuoles_num"]);
                            if (flag39)
                            {
                                sampleinfo._12vacuoles_num = Convert.ToInt32(dr["_12vacuoles_num"]);
                            }
                            bool flag40 = !Convert.IsDBNull(dr["_3plusvacuoles_num"]);
                            if (flag40)
                            {
                                sampleinfo._3plusvacuoles_num = Convert.ToInt32(dr["_3plusvacuoles_num"]);
                            }
                            bool flag41 = !Convert.IsDBNull(dr["kernelvacuoles_num"]);
                            if (flag41)
                            {
                                sampleinfo.kernelvacuoles_num = Convert.ToInt32(dr["kernelvacuoles_num"]);
                            }
                            bool flag42 = !Convert.IsDBNull(dr["bigvacuoles_num"]);
                            if (flag42)
                            {
                                sampleinfo.bigvacuoles_num = Convert.ToInt32(dr["bigvacuoles_num"]);
                            }
                            bool flag43 = !Convert.IsDBNull(dr["normalmiddlepiece_num"]);
                            if (flag43)
                            {
                                sampleinfo.normalmiddlepiece_num = Convert.ToInt32(dr["normalmiddlepiece_num"]);
                            }
                            bool flag44 = !Convert.IsDBNull(dr["abnormalmiddlepiece_num"]);
                            if (flag44)
                            {
                                sampleinfo.abnormalmiddlepiece_num = Convert.ToInt32(dr["abnormalmiddlepiece_num"]);
                            }
                            bool flag45 = !Convert.IsDBNull(dr["normalmiddlepieceangle_num"]);
                            if (flag45)
                            {
                                sampleinfo.normalmiddlepieceangle_num = Convert.ToInt32(dr["normalmiddlepieceangle_num"]);
                            }
                            bool flag46 = !Convert.IsDBNull(dr["abnormalmiddlepieceangle_num"]);
                            if (flag46)
                            {
                                sampleinfo.abnormalmiddlepieceangle_num = Convert.ToInt32(dr["abnormalmiddlepieceangle_num"]);
                            }
                            bool flag47 = !Convert.IsDBNull(dr["normalmiddlepiecewidth_num"]);
                            if (flag47)
                            {
                                sampleinfo.normalmiddlepiecewidth_num = Convert.ToInt32(dr["normalmiddlepiecewidth_num"]);
                            }
                            bool flag48 = !Convert.IsDBNull(dr["abnormalmiddlepiecewidth_num"]);
                            if (flag48)
                            {
                                sampleinfo.abnormalmiddlepiecewidth_num = Convert.ToInt32(dr["abnormalmiddlepiecewidth_num"]);
                            }
                            bool flag49 = !Convert.IsDBNull(dr["normalshape_num"]);
                            if (flag49)
                            {
                                sampleinfo.normalshape_num = Convert.ToInt32(dr["normalshape_num"]);
                            }
                            bool flag50 = !Convert.IsDBNull(dr["abnormalshape_num"]);
                            if (flag50)
                            {
                                sampleinfo.abnormalshape_num = Convert.ToInt32(dr["abnormalshape_num"]);
                            }
                            bool flag51 = !Convert.IsDBNull(dr["irregularshape_num"]);
                            if (flag51)
                            {
                                sampleinfo.irregularshape_num = Convert.ToInt32(dr["irregularshape_num"]);
                            }
                            bool flag52 = !Convert.IsDBNull(dr["pyriformconeshape_num"]);
                            if (flag52)
                            {
                                sampleinfo.pyriformconeshape_num = Convert.ToInt32(dr["pyriformconeshape_num"]);
                            }
                            bool flag53 = !Convert.IsDBNull(dr["asymmetryshape_num"]);
                            if (flag53)
                            {
                                sampleinfo.asymmetryshape_num = Convert.ToInt32(dr["asymmetryshape_num"]);
                            }
                            bool flag54 = !Convert.IsDBNull(dr["normalTail_num"]);
                            if (flag54)
                            {
                                sampleinfo.normalTail_num = Convert.ToInt32(dr["normalTail_num"]);
                            }
                            bool flag55 = !Convert.IsDBNull(dr["abnormalTail_num"]);
                            if (flag55)
                            {
                                sampleinfo.abnormalTail_num = Convert.ToInt32(dr["abnormalTail_num"]);
                            }
                            bool flag56 = !Convert.IsDBNull(dr["ERC_num"]);
                            if (flag56)
                            {
                                sampleinfo.ERC_num = Convert.ToInt32(dr["ERC_num"]);
                            }
                            bool flag57 = !Convert.IsDBNull(dr["notail_num"]);
                            if (flag57)
                            {
                                sampleinfo.notail_num = Convert.ToInt32(dr["notail_num"]);
                            }
                            bool flag58 = !Convert.IsDBNull(dr["shorttail_num"]);
                            if (flag58)
                            {
                                sampleinfo.shorttail_num = Convert.ToInt32(dr["shorttail_num"]);
                            }
                            bool flag59 = !Convert.IsDBNull(dr["curvetail_num"]);
                            if (flag59)
                            {
                                sampleinfo.curvetail_num = Convert.ToInt32(dr["curvetail_num"]);
                            }
                            bool flag60 = !Convert.IsDBNull(dr["bendtail_num"]);
                            if (flag60)
                            {
                                sampleinfo.bendtail_num = Convert.ToInt32(dr["bendtail_num"]);
                            }
                            bool flag61 = !Convert.IsDBNull(dr["lheadlacro_num"]);
                            if (flag61)
                            {
                                sampleinfo.lheadlacro_num = Convert.ToInt32(dr["lheadlacro_num"]);
                            }
                            bool flag62 = !Convert.IsDBNull(dr["rheadnacro_num"]);
                            if (flag62)
                            {
                                sampleinfo.rheadnacro_num = Convert.ToInt32(dr["rheadnacro_num"]);
                            }
                            bool flag63 = !Convert.IsDBNull(dr["rheadlacro_num"]);
                            if (flag63)
                            {
                                sampleinfo.rheadlacro_num = Convert.ToInt32(dr["rheadlacro_num"]);
                            }
                            bool flag64 = !Convert.IsDBNull(dr["pheadbacro_num"]);
                            if (flag64)
                            {
                                sampleinfo.pheadbacro_num = Convert.ToInt32(dr["pheadbacro_num"]);
                            }
                            bool flag65 = !Convert.IsDBNull(dr["theadlacro_num"]);
                            if (flag65)
                            {
                                sampleinfo.theadlacro_num = Convert.ToInt32(dr["theadlacro_num"]);
                            }
                            bool flag66 = sampleinfo.sperm_num > 0;
                            if (flag66)
                            {
                                sampleinfo.tallhead_rate = 1.0 * (double)sampleinfo.tallhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.shorthead_rate = 1.0 * (double)sampleinfo.shorthead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalheight_rate = 1.0 * (double)sampleinfo.normalheight_num / (double)sampleinfo.sperm_num;
                                sampleinfo.fathead_rate = 1.0 * (double)sampleinfo.fathead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.thinhead_rate = 1.0 * (double)sampleinfo.thinhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalwidth_rate = 1.0 * (double)sampleinfo.normalwidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.roundhead_rate = 1.0 * (double)sampleinfo.roundhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalratio_rate = 1.0 * (double)sampleinfo.normalratio_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalvacuoles_rate = 1.0 * (double)sampleinfo.normalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._0vacuoles_rate = 1.0 * (double)sampleinfo._0vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._12vacuoles_rate = 1.0 * (double)sampleinfo._12vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._3plusvacuoles_rate = 1.0 * (double)sampleinfo._3plusvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.kernelvacuoles_rate = 1.0 * (double)sampleinfo.kernelvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigvacuoles_rate = 1.0 * (double)sampleinfo.bigvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiece_rate = 1.0 * (double)sampleinfo.normalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiece_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.normalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.abnormalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.normalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalshape_rate = 1.0 * (double)sampleinfo.abnormalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.asymmetryshape_rate = 1.0 * (double)sampleinfo.asymmetryshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalshape_rate = 1.0 * (double)sampleinfo.normalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalhead_rate = 1.0 * (double)sampleinfo.normalhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.microhead_rate = 1.0 * (double)sampleinfo.microhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.macrohead_rate = 1.0 * (double)sampleinfo.macrohead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.sharphead_rate = 1.0 * (double)sampleinfo.sharphead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pyriformconeshape_rate = 1.0 * (double)sampleinfo.pyriformconeshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.irregularshape_rate = 1.0 * (double)sampleinfo.irregularshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.noacrosome_rate = 1.0 * (double)sampleinfo.noacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigacrosome_rate = 1.0 * (double)sampleinfo.bigacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.smallacrosome_rate = 1.0 * (double)sampleinfo.smallacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalacrosome_rate = 1.0 * (double)sampleinfo.normalacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalvacuoles_rate = 1.0 * (double)sampleinfo.abnormalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.lheadlacro_rate = 1.0 * (double)sampleinfo.lheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadlacro_rate = 1.0 * (double)sampleinfo.rheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadnacro_rate = 1.0 * (double)sampleinfo.rheadnacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.theadlacro_rate = 1.0 * (double)sampleinfo.theadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pheadbacro_rate = 1.0 * (double)sampleinfo.pheadbacro_num / (double)sampleinfo.sperm_num;
                            }
                        }
                    }
                }
                conn.Close();
            }
            return sampleinfo;
        }

        public static Sampleinfo GetLastObject()
        {
            Sampleinfo sampleinfo = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from sampleinfo order by id desc limit 1", conn);
                conn.Open();
                using ( SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        sampleinfo= new Sampleinfo();
                        bool flag2 = !Convert.IsDBNull(dr["sendtime"]);
                        if (flag2)
                        {
                            sampleinfo.sendtime = dr["sendtime"].ToString();
                        }
                        bool flag3 = !Convert.IsDBNull(dr["sampleId"]);
                        if (flag3)
                        {
                            sampleinfo.sampleId = Convert.ToString(dr["sampleId"]);
                        }
                        string a = "";
                        bool flag4 = !Convert.IsDBNull(dr["checkcode"]);
                        if (flag4)
                        {
                            a = Convert.ToString(dr["checkcode"]);
                        }
                        string b = Crypto.DES.MD5(sampleinfo.sampleId + sampleinfo.sendtime.ToString());
                        bool flag5 = Basic.useCheckcode && a != b;
                        if (flag5)
                        {
                            //result = null;
                        }
                        else
                        {
                            sampleinfo.Id = Convert.ToInt32(dr["id"]);
                            sampleinfo.patientId = Convert.ToString(dr["patientId"]);
                            sampleinfo.patientName = Convert.ToString(dr["patientName"]);
                            sampleinfo.patientage = Convert.ToInt32(dr["patientage"]);
                            sampleinfo.inspectItem = Convert.ToString(dr["inspectItem"]);
                            string inspectItem = sampleinfo.inspectItem;
                            if (!(inspectItem == "MRF"))
                            {
                                if (!(inspectItem == "DFI"))
                                {
                                    if (inspectItem == "MAT")
                                    {
                                        sampleinfo.inspectItemCHN = "核成熟度";
                                    }
                                }
                                else
                                {
                                    sampleinfo.inspectItemCHN = "DFI";
                                }
                            }
                            else
                            {
                                sampleinfo.inspectItemCHN = "形态";
                            }
                            sampleinfo.getSpermWay = Convert.ToString(dr["getSpermWay"]);
                            sampleinfo.abstinenceDays = Convert.ToInt32(dr["abstinenceDays"]);
                            sampleinfo.dilutionRatio = Convert.ToDouble(dr["dilutionRatio"]);
                            sampleinfo.sampleSource = Convert.ToString(dr["sampleSource"]);
                            sampleinfo.department = Convert.ToString(dr["department"]);
                            sampleinfo.inspectionDoctor = Convert.ToString(dr["inspectionDoctor"]);
                            bool flag6 = !Convert.IsDBNull(dr["userid"]);
                            if (flag6)
                            {
                                sampleinfo.userid = Convert.ToInt32(dr["userid"]);
                            }
                            sampleinfo.sendDoctor = Convert.ToString(dr["sendDoctor"]);
                            bool flagscaned = !Convert.IsDBNull(dr["scanned"]);
                            if (flagscaned)
                            {
                                sampleinfo.scanned = Convert.ToInt32(dr["scanned"]);
                            }
                            
                            bool flag7 = !Convert.IsDBNull(dr["scantime"]);
                            if (flag7)
                            {
                                sampleinfo.scantime = dr["scantime"].ToString();
                            }
                            sampleinfo.scanmode = Convert.ToString(dr["scanmode"]);
                            bool flagpro = !Convert.IsDBNull(dr["processed"]);
                            if (flagpro)
                            {
                                sampleinfo.processed = Convert.ToInt32(dr["processed"]);
                            }
                            
                            bool flag8 = !Convert.IsDBNull(dr["processed_time"]);
                            if (flag8)
                            {
                                sampleinfo.processed_time = dr["processed_time"].ToString();
                            }
                            bool flagrev = !Convert.IsDBNull(dr["reviewed"]);
                            if (flagrev)
                            {
                                sampleinfo.reviewed = Convert.ToInt32(dr["reviewed"]);
                            }
                            
                            sampleinfo.reviewer = Convert.ToString(dr["reviewer"]);
                            bool flag9 = !Convert.IsDBNull(dr["review_time"]);
                            if (flag9)
                            {
                                sampleinfo.review_time = dr["review_time"].ToString();
                            }

                            sampleinfo.processed_np = dr["processed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["processed_np"]);

                            bool flag10 = !Convert.IsDBNull(dr["processed_np_time"]);
                            if (flag10)
                            {
                                sampleinfo.processed_np_time = dr["processed_np_time"].ToString();
                            }
                            sampleinfo.reviewed_np = dr["reviewed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reviewed_np"]);
                            sampleinfo.reviewer_np = Convert.ToString(dr["reviewer_np"]);
                            bool flag11 = !Convert.IsDBNull(dr["review_np_time"]);
                            if (flag11)
                            {
                                sampleinfo.review_np_time = dr["review_np_time"].ToString();
                            }
                            sampleinfo.reportprinted = dr["reportprinted"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reportprinted"]);
                            bool flag12 = !Convert.IsDBNull(dr["reporttime"]);
                            if (flag12)
                            {
                                sampleinfo.reporttime = dr["reporttime"].ToString();
                            }
                            sampleinfo.reportcontent = Convert.ToString(dr["reportcontent"]);
                            sampleinfo.diagnosis = Convert.ToString(dr["diagnosis"]);
                            sampleinfo.purpose = Convert.ToString(dr["purpose"]);
                            bool flag13 = !Convert.IsDBNull(dr["field_num"]);
                            if (flag13)
                            {
                                sampleinfo.field_num = Convert.ToInt32(dr["field_num"]);
                            }
                            bool flag14 = !Convert.IsDBNull(dr["sperm_num"]);
                            if (flag14)
                            {
                                sampleinfo.sperm_num = Convert.ToInt32(dr["sperm_num"]);
                            }
                            bool flag15 = !Convert.IsDBNull(dr["normal_num"]);
                            if (flag15)
                            {
                                sampleinfo.normal_num = Convert.ToInt32(dr["normal_num"]);
                            }
                            bool flag16 = !Convert.IsDBNull(dr["abnormal_num"]);
                            if (flag16)
                            {
                                sampleinfo.abnormal_num = Convert.ToInt32(dr["abnormal_num"]);
                            }
                            bool flag17 = !Convert.IsDBNull(dr["subclinical_num"]);
                            if (flag17)
                            {
                                sampleinfo.subclinical_num = Convert.ToInt32(dr["subclinical_num"]);
                            }
                            bool flag18 = !Convert.IsDBNull(dr["qualification_rate"]);
                            if (flag18)
                            {
                                sampleinfo.qualification_rate = Convert.ToDouble(dr["qualification_rate"]);
                                sampleinfo.abnormal_rate = 1.0 * (double)sampleinfo.abnormal_num / (double)sampleinfo.sperm_num;
                                sampleinfo.subclinical_rate = 1.0 * (double)sampleinfo.subclinical_num / (double)sampleinfo.sperm_num;
                                sampleinfo.totalPercent = 1.0;
                            }
                            bool flag19 = !Convert.IsDBNull(dr["normalhead_num"]);
                            if (flag19)
                            {
                                sampleinfo.normalhead_num = Convert.ToInt32(dr["normalhead_num"]);
                            }
                            bool flag20 = !Convert.IsDBNull(dr["microhead_num"]);
                            if (flag20)
                            {
                                sampleinfo.microhead_num = Convert.ToInt32(dr["microhead_num"]);
                            }
                            bool flag21 = !Convert.IsDBNull(dr["macrohead_num"]);
                            if (flag21)
                            {
                                sampleinfo.macrohead_num = Convert.ToInt32(dr["macrohead_num"]);
                            }
                            bool flag22 = !Convert.IsDBNull(dr["normalheight_num"]);
                            if (flag22)
                            {
                                sampleinfo.normalheight_num = Convert.ToInt32(dr["normalheight_num"]);
                            }
                            bool flag23 = !Convert.IsDBNull(dr["tallhead_num"]);
                            if (flag23)
                            {
                                sampleinfo.tallhead_num = Convert.ToInt32(dr["tallhead_num"]);
                            }
                            bool flag24 = !Convert.IsDBNull(dr["shorthead_num"]);
                            if (flag24)
                            {
                                sampleinfo.shorthead_num = Convert.ToInt32(dr["shorthead_num"]);
                            }
                            bool flag25 = !Convert.IsDBNull(dr["normalwidth_num"]);
                            if (flag25)
                            {
                                sampleinfo.normalwidth_num = Convert.ToInt32(dr["normalwidth_num"]);
                            }
                            bool flag26 = !Convert.IsDBNull(dr["fathead_num"]);
                            if (flag26)
                            {
                                sampleinfo.fathead_num = Convert.ToInt32(dr["fathead_num"]);
                            }
                            bool flag27 = !Convert.IsDBNull(dr["thinhead_num"]);
                            if (flag27)
                            {
                                sampleinfo.thinhead_num = Convert.ToInt32(dr["thinhead_num"]);
                            }
                            bool flag28 = !Convert.IsDBNull(dr["normalratio_num"]);
                            if (flag28)
                            {
                                sampleinfo.normalratio_num = Convert.ToInt32(dr["normalratio_num"]);
                            }
                            bool flag29 = !Convert.IsDBNull(dr["sharphead_num"]);
                            if (flag29)
                            {
                                sampleinfo.sharphead_num = Convert.ToInt32(dr["sharphead_num"]);
                            }
                            bool flag30 = !Convert.IsDBNull(dr["roundhead_num"]);
                            if (flag30)
                            {
                                sampleinfo.roundhead_num = Convert.ToInt32(dr["roundhead_num"]);
                            }
                            bool flag31 = !Convert.IsDBNull(dr["normalkernel_num"]);
                            if (flag31)
                            {
                                sampleinfo.normalkernel_num = Convert.ToInt32(dr["normalkernel_num"]);
                                sampleinfo.abnormalkernel_num = sampleinfo.sperm_num - sampleinfo.normalkernel_num;
                            }
                            bool flag32 = !Convert.IsDBNull(dr["normalacrosome_num"]);
                            if (flag32)
                            {
                                sampleinfo.normalacrosome_num = Convert.ToInt32(dr["normalacrosome_num"]);
                                sampleinfo.abnormalacrosome_num = sampleinfo.sperm_num - sampleinfo.normalacrosome_num;
                            }
                            bool flag33 = !Convert.IsDBNull(dr["bigacrosome_num"]);
                            if (flag33)
                            {
                                sampleinfo.bigacrosome_num = Convert.ToInt32(dr["bigacrosome_num"]);
                            }
                            bool flag34 = !Convert.IsDBNull(dr["smallacrosome_num"]);
                            if (flag34)
                            {
                                sampleinfo.smallacrosome_num = Convert.ToInt32(dr["smallacrosome_num"]);
                            }
                            bool flag35 = !Convert.IsDBNull(dr["noacrosome_num"]);
                            if (flag35)
                            {
                                sampleinfo.noacrosome_num = Convert.ToInt32(dr["noacrosome_num"]);
                            }
                            bool flag36 = !Convert.IsDBNull(dr["normalvacuoles_num"]);
                            if (flag36)
                            {
                                sampleinfo.normalvacuoles_num = Convert.ToInt32(dr["normalvacuoles_num"]);
                            }
                            bool flag37 = !Convert.IsDBNull(dr["abnormalvacuoles_num"]);
                            if (flag37)
                            {
                                sampleinfo.abnormalvacuoles_num = Convert.ToInt32(dr["abnormalvacuoles_num"]);
                            }
                            bool flag38 = !Convert.IsDBNull(dr["_0vacuoles_num"]);
                            if (flag38)
                            {
                                sampleinfo._0vacuoles_num = Convert.ToInt32(dr["_0vacuoles_num"]);
                            }
                            bool flag39 = !Convert.IsDBNull(dr["_12vacuoles_num"]);
                            if (flag39)
                            {
                                sampleinfo._12vacuoles_num = Convert.ToInt32(dr["_12vacuoles_num"]);
                            }
                            bool flag40 = !Convert.IsDBNull(dr["_3plusvacuoles_num"]);
                            if (flag40)
                            {
                                sampleinfo._3plusvacuoles_num = Convert.ToInt32(dr["_3plusvacuoles_num"]);
                            }
                            bool flag41 = !Convert.IsDBNull(dr["kernelvacuoles_num"]);
                            if (flag41)
                            {
                                sampleinfo.kernelvacuoles_num = Convert.ToInt32(dr["kernelvacuoles_num"]);
                            }
                            bool flag42 = !Convert.IsDBNull(dr["bigvacuoles_num"]);
                            if (flag42)
                            {
                                sampleinfo.bigvacuoles_num = Convert.ToInt32(dr["bigvacuoles_num"]);
                            }
                            bool flag43 = !Convert.IsDBNull(dr["normalmiddlepiece_num"]);
                            if (flag43)
                            {
                                sampleinfo.normalmiddlepiece_num = Convert.ToInt32(dr["normalmiddlepiece_num"]);
                            }
                            bool flag44 = !Convert.IsDBNull(dr["abnormalmiddlepiece_num"]);
                            if (flag44)
                            {
                                sampleinfo.abnormalmiddlepiece_num = Convert.ToInt32(dr["abnormalmiddlepiece_num"]);
                            }
                            bool flag45 = !Convert.IsDBNull(dr["normalmiddlepieceangle_num"]);
                            if (flag45)
                            {
                                sampleinfo.normalmiddlepieceangle_num = Convert.ToInt32(dr["normalmiddlepieceangle_num"]);
                            }
                            bool flag46 = !Convert.IsDBNull(dr["abnormalmiddlepieceangle_num"]);
                            if (flag46)
                            {
                                sampleinfo.abnormalmiddlepieceangle_num = Convert.ToInt32(dr["abnormalmiddlepieceangle_num"]);
                            }
                            bool flag47 = !Convert.IsDBNull(dr["normalmiddlepiecewidth_num"]);
                            if (flag47)
                            {
                                sampleinfo.normalmiddlepiecewidth_num = Convert.ToInt32(dr["normalmiddlepiecewidth_num"]);
                            }
                            bool flag48 = !Convert.IsDBNull(dr["abnormalmiddlepiecewidth_num"]);
                            if (flag48)
                            {
                                sampleinfo.abnormalmiddlepiecewidth_num = Convert.ToInt32(dr["abnormalmiddlepiecewidth_num"]);
                            }
                            bool flag49 = !Convert.IsDBNull(dr["normalshape_num"]);
                            if (flag49)
                            {
                                sampleinfo.normalshape_num = Convert.ToInt32(dr["normalshape_num"]);
                            }
                            bool flag50 = !Convert.IsDBNull(dr["abnormalshape_num"]);
                            if (flag50)
                            {
                                sampleinfo.abnormalshape_num = Convert.ToInt32(dr["abnormalshape_num"]);
                            }
                            bool flag51 = !Convert.IsDBNull(dr["irregularshape_num"]);
                            if (flag51)
                            {
                                sampleinfo.irregularshape_num = Convert.ToInt32(dr["irregularshape_num"]);
                            }
                            bool flag52 = !Convert.IsDBNull(dr["pyriformconeshape_num"]);
                            if (flag52)
                            {
                                sampleinfo.pyriformconeshape_num = Convert.ToInt32(dr["pyriformconeshape_num"]);
                            }
                            bool flag53 = !Convert.IsDBNull(dr["asymmetryshape_num"]);
                            if (flag53)
                            {
                                sampleinfo.asymmetryshape_num = Convert.ToInt32(dr["asymmetryshape_num"]);
                            }
                            bool flag54 = !Convert.IsDBNull(dr["normalTail_num"]);
                            if (flag54)
                            {
                                sampleinfo.normalTail_num = Convert.ToInt32(dr["normalTail_num"]);
                            }
                            bool flag55 = !Convert.IsDBNull(dr["abnormalTail_num"]);
                            if (flag55)
                            {
                                sampleinfo.abnormalTail_num = Convert.ToInt32(dr["abnormalTail_num"]);
                            }
                            bool flag56 = !Convert.IsDBNull(dr["ERC_num"]);
                            if (flag56)
                            {
                                sampleinfo.ERC_num = Convert.ToInt32(dr["ERC_num"]);
                            }
                            bool flag57 = !Convert.IsDBNull(dr["notail_num"]);
                            if (flag57)
                            {
                                sampleinfo.notail_num = Convert.ToInt32(dr["notail_num"]);
                            }
                            bool flag58 = !Convert.IsDBNull(dr["shorttail_num"]);
                            if (flag58)
                            {
                                sampleinfo.shorttail_num = Convert.ToInt32(dr["shorttail_num"]);
                            }
                            bool flag59 = !Convert.IsDBNull(dr["curvetail_num"]);
                            if (flag59)
                            {
                                sampleinfo.curvetail_num = Convert.ToInt32(dr["curvetail_num"]);
                            }
                            bool flag60 = !Convert.IsDBNull(dr["bendtail_num"]);
                            if (flag60)
                            {
                                sampleinfo.bendtail_num = Convert.ToInt32(dr["bendtail_num"]);
                            }
                            bool flag61 = !Convert.IsDBNull(dr["lheadlacro_num"]);
                            if (flag61)
                            {
                                sampleinfo.lheadlacro_num = Convert.ToInt32(dr["lheadlacro_num"]);
                            }
                            bool flag62 = !Convert.IsDBNull(dr["rheadnacro_num"]);
                            if (flag62)
                            {
                                sampleinfo.rheadnacro_num = Convert.ToInt32(dr["rheadnacro_num"]);
                            }
                            bool flag63 = !Convert.IsDBNull(dr["rheadlacro_num"]);
                            if (flag63)
                            {
                                sampleinfo.rheadlacro_num = Convert.ToInt32(dr["rheadlacro_num"]);
                            }
                            bool flag64 = !Convert.IsDBNull(dr["pheadbacro_num"]);
                            if (flag64)
                            {
                                sampleinfo.pheadbacro_num = Convert.ToInt32(dr["pheadbacro_num"]);
                            }
                            bool flag65 = !Convert.IsDBNull(dr["theadlacro_num"]);
                            if (flag65)
                            {
                                sampleinfo.theadlacro_num = Convert.ToInt32(dr["theadlacro_num"]);
                            }
                            bool flag66 = sampleinfo.sperm_num > 0;
                            if (flag66)
                            {
                                sampleinfo.tallhead_rate = 1.0 * (double)sampleinfo.tallhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.shorthead_rate = 1.0 * (double)sampleinfo.shorthead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalheight_rate = 1.0 * (double)sampleinfo.normalheight_num / (double)sampleinfo.sperm_num;
                                sampleinfo.fathead_rate = 1.0 * (double)sampleinfo.fathead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.thinhead_rate = 1.0 * (double)sampleinfo.thinhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalwidth_rate = 1.0 * (double)sampleinfo.normalwidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.roundhead_rate = 1.0 * (double)sampleinfo.roundhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalratio_rate = 1.0 * (double)sampleinfo.normalratio_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalvacuoles_rate = 1.0 * (double)sampleinfo.normalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._0vacuoles_rate = 1.0 * (double)sampleinfo._0vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._12vacuoles_rate = 1.0 * (double)sampleinfo._12vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._3plusvacuoles_rate = 1.0 * (double)sampleinfo._3plusvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.kernelvacuoles_rate = 1.0 * (double)sampleinfo.kernelvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigvacuoles_rate = 1.0 * (double)sampleinfo.bigvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiece_rate = 1.0 * (double)sampleinfo.normalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiece_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.normalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.abnormalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.normalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalshape_rate = 1.0 * (double)sampleinfo.abnormalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.asymmetryshape_rate = 1.0 * (double)sampleinfo.asymmetryshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalshape_rate = 1.0 * (double)sampleinfo.normalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalhead_rate = 1.0 * (double)sampleinfo.normalhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.microhead_rate = 1.0 * (double)sampleinfo.microhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.macrohead_rate = 1.0 * (double)sampleinfo.macrohead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.sharphead_rate = 1.0 * (double)sampleinfo.sharphead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pyriformconeshape_rate = 1.0 * (double)sampleinfo.pyriformconeshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.irregularshape_rate = 1.0 * (double)sampleinfo.irregularshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.noacrosome_rate = 1.0 * (double)sampleinfo.noacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigacrosome_rate = 1.0 * (double)sampleinfo.bigacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.smallacrosome_rate = 1.0 * (double)sampleinfo.smallacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalacrosome_rate = 1.0 * (double)sampleinfo.normalacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalvacuoles_rate = 1.0 * (double)sampleinfo.abnormalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.lheadlacro_rate = 1.0 * (double)sampleinfo.lheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadlacro_rate = 1.0 * (double)sampleinfo.rheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadnacro_rate = 1.0 * (double)sampleinfo.rheadnacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.theadlacro_rate = 1.0 * (double)sampleinfo.theadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pheadbacro_rate = 1.0 * (double)sampleinfo.pheadbacro_num / (double)sampleinfo.sperm_num;
                            }
                        }
                    }
                }
                conn.Close();
            }
            return sampleinfo;
        }

        public static Sampleinfo GetLastObjectByPro0()
        {
            Sampleinfo sampleinfo = null;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select * from sampleinfo where processed=0 order by id asc limit 1", conn);
                conn.Open();
                using (SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    if (dr.Read())
                    {
                        sampleinfo = new Sampleinfo();
                        bool flag2 = !Convert.IsDBNull(dr["sendtime"]);
                        if (flag2)
                        {
                            sampleinfo.sendtime = dr["sendtime"].ToString();
                        }
                        bool flag3 = !Convert.IsDBNull(dr["sampleId"]);
                        if (flag3)
                        {
                            sampleinfo.sampleId = Convert.ToString(dr["sampleId"]);
                        }
                        string a = "";
                        bool flag4 = !Convert.IsDBNull(dr["checkcode"]);
                        if (flag4)
                        {
                            a = Convert.ToString(dr["checkcode"]);
                        }
                        string b = Crypto.DES.MD5(sampleinfo.sampleId + sampleinfo.sendtime.ToString());
                        bool flag5 = Basic.useCheckcode && a != b;
                        if (flag5)
                        {
                            //result = null;
                        }
                        else
                        {
                            sampleinfo.Id = Convert.ToInt32(dr["id"]);
                            sampleinfo.patientId = Convert.ToString(dr["patientId"]);
                            sampleinfo.patientName = Convert.ToString(dr["patientName"]);
                            sampleinfo.patientage = Convert.ToInt32(dr["patientage"]);
                            sampleinfo.inspectItem = Convert.ToString(dr["inspectItem"]);
                            string inspectItem = sampleinfo.inspectItem;
                            if (!(inspectItem == "MRF"))
                            {
                                if (!(inspectItem == "DFI"))
                                {
                                    if (inspectItem == "MAT")
                                    {
                                        sampleinfo.inspectItemCHN = "核成熟度";
                                    }
                                }
                                else
                                {
                                    sampleinfo.inspectItemCHN = "DFI";
                                }
                            }
                            else
                            {
                                sampleinfo.inspectItemCHN = "形态";
                            }
                            sampleinfo.getSpermWay = Convert.ToString(dr["getSpermWay"]);
                            sampleinfo.abstinenceDays = Convert.ToInt32(dr["abstinenceDays"]);
                            sampleinfo.dilutionRatio = Convert.ToDouble(dr["dilutionRatio"]);
                            sampleinfo.sampleSource = Convert.ToString(dr["sampleSource"]);
                            sampleinfo.department = Convert.ToString(dr["department"]);
                            sampleinfo.inspectionDoctor = Convert.ToString(dr["inspectionDoctor"]);
                            bool flag6 = !Convert.IsDBNull(dr["userid"]);
                            if (flag6)
                            {
                                sampleinfo.userid = Convert.ToInt32(dr["userid"]);
                            }
                            sampleinfo.sendDoctor = Convert.ToString(dr["sendDoctor"]);
                            bool flagscaned = !Convert.IsDBNull(dr["scanned"]);
                            if (flagscaned)
                            {
                                sampleinfo.scanned = Convert.ToInt32(dr["scanned"]);
                            }

                            bool flag7 = !Convert.IsDBNull(dr["scantime"]);
                            if (flag7)
                            {
                                sampleinfo.scantime = dr["scantime"].ToString();
                            }
                            sampleinfo.scanmode = Convert.ToString(dr["scanmode"]);
                            bool flagpro = !Convert.IsDBNull(dr["processed"]);
                            if (flagpro)
                            {
                                sampleinfo.processed = Convert.ToInt32(dr["processed"]);
                            }

                            bool flag8 = !Convert.IsDBNull(dr["processed_time"]);
                            if (flag8)
                            {
                                sampleinfo.processed_time = dr["processed_time"].ToString();
                            }
                            bool flagrev = !Convert.IsDBNull(dr["reviewed"]);
                            if (flagrev)
                            {
                                sampleinfo.reviewed = Convert.ToInt32(dr["reviewed"]);
                            }

                            sampleinfo.reviewer = Convert.ToString(dr["reviewer"]);
                            bool flag9 = !Convert.IsDBNull(dr["review_time"]);
                            if (flag9)
                            {
                                sampleinfo.review_time = dr["review_time"].ToString();
                            }

                            sampleinfo.processed_np = dr["processed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["processed_np"]);

                            bool flag10 = !Convert.IsDBNull(dr["processed_np_time"]);
                            if (flag10)
                            {
                                sampleinfo.processed_np_time = dr["processed_np_time"].ToString();
                            }
                            sampleinfo.reviewed_np = dr["reviewed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reviewed_np"]);
                            sampleinfo.reviewer_np = Convert.ToString(dr["reviewer_np"]);
                            bool flag11 = !Convert.IsDBNull(dr["review_np_time"]);
                            if (flag11)
                            {
                                sampleinfo.review_np_time = dr["review_np_time"].ToString();
                            }
                            sampleinfo.reportprinted = dr["reportprinted"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reportprinted"]);
                            bool flag12 = !Convert.IsDBNull(dr["reporttime"]);
                            if (flag12)
                            {
                                sampleinfo.reporttime = dr["reporttime"].ToString();
                            }
                            sampleinfo.reportcontent = Convert.ToString(dr["reportcontent"]);
                            sampleinfo.diagnosis = Convert.ToString(dr["diagnosis"]);
                            sampleinfo.purpose = Convert.ToString(dr["purpose"]);
                            bool flag13 = !Convert.IsDBNull(dr["field_num"]);
                            if (flag13)
                            {
                                sampleinfo.field_num = Convert.ToInt32(dr["field_num"]);
                            }
                            bool flag14 = !Convert.IsDBNull(dr["sperm_num"]);
                            if (flag14)
                            {
                                sampleinfo.sperm_num = Convert.ToInt32(dr["sperm_num"]);
                            }
                            bool flag15 = !Convert.IsDBNull(dr["normal_num"]);
                            if (flag15)
                            {
                                sampleinfo.normal_num = Convert.ToInt32(dr["normal_num"]);
                            }
                            bool flag16 = !Convert.IsDBNull(dr["abnormal_num"]);
                            if (flag16)
                            {
                                sampleinfo.abnormal_num = Convert.ToInt32(dr["abnormal_num"]);
                            }
                            bool flag17 = !Convert.IsDBNull(dr["subclinical_num"]);
                            if (flag17)
                            {
                                sampleinfo.subclinical_num = Convert.ToInt32(dr["subclinical_num"]);
                            }
                            bool flag18 = !Convert.IsDBNull(dr["qualification_rate"]);
                            if (flag18)
                            {
                                sampleinfo.qualification_rate = Convert.ToDouble(dr["qualification_rate"]);
                                sampleinfo.abnormal_rate = 1.0 * (double)sampleinfo.abnormal_num / (double)sampleinfo.sperm_num;
                                sampleinfo.subclinical_rate = 1.0 * (double)sampleinfo.subclinical_num / (double)sampleinfo.sperm_num;
                                sampleinfo.totalPercent = 1.0;
                            }
                            bool flag19 = !Convert.IsDBNull(dr["normalhead_num"]);
                            if (flag19)
                            {
                                sampleinfo.normalhead_num = Convert.ToInt32(dr["normalhead_num"]);
                            }
                            bool flag20 = !Convert.IsDBNull(dr["microhead_num"]);
                            if (flag20)
                            {
                                sampleinfo.microhead_num = Convert.ToInt32(dr["microhead_num"]);
                            }
                            bool flag21 = !Convert.IsDBNull(dr["macrohead_num"]);
                            if (flag21)
                            {
                                sampleinfo.macrohead_num = Convert.ToInt32(dr["macrohead_num"]);
                            }
                            bool flag22 = !Convert.IsDBNull(dr["normalheight_num"]);
                            if (flag22)
                            {
                                sampleinfo.normalheight_num = Convert.ToInt32(dr["normalheight_num"]);
                            }
                            bool flag23 = !Convert.IsDBNull(dr["tallhead_num"]);
                            if (flag23)
                            {
                                sampleinfo.tallhead_num = Convert.ToInt32(dr["tallhead_num"]);
                            }
                            bool flag24 = !Convert.IsDBNull(dr["shorthead_num"]);
                            if (flag24)
                            {
                                sampleinfo.shorthead_num = Convert.ToInt32(dr["shorthead_num"]);
                            }
                            bool flag25 = !Convert.IsDBNull(dr["normalwidth_num"]);
                            if (flag25)
                            {
                                sampleinfo.normalwidth_num = Convert.ToInt32(dr["normalwidth_num"]);
                            }
                            bool flag26 = !Convert.IsDBNull(dr["fathead_num"]);
                            if (flag26)
                            {
                                sampleinfo.fathead_num = Convert.ToInt32(dr["fathead_num"]);
                            }
                            bool flag27 = !Convert.IsDBNull(dr["thinhead_num"]);
                            if (flag27)
                            {
                                sampleinfo.thinhead_num = Convert.ToInt32(dr["thinhead_num"]);
                            }
                            bool flag28 = !Convert.IsDBNull(dr["normalratio_num"]);
                            if (flag28)
                            {
                                sampleinfo.normalratio_num = Convert.ToInt32(dr["normalratio_num"]);
                            }
                            bool flag29 = !Convert.IsDBNull(dr["sharphead_num"]);
                            if (flag29)
                            {
                                sampleinfo.sharphead_num = Convert.ToInt32(dr["sharphead_num"]);
                            }
                            bool flag30 = !Convert.IsDBNull(dr["roundhead_num"]);
                            if (flag30)
                            {
                                sampleinfo.roundhead_num = Convert.ToInt32(dr["roundhead_num"]);
                            }
                            bool flag31 = !Convert.IsDBNull(dr["normalkernel_num"]);
                            if (flag31)
                            {
                                sampleinfo.normalkernel_num = Convert.ToInt32(dr["normalkernel_num"]);
                                sampleinfo.abnormalkernel_num = sampleinfo.sperm_num - sampleinfo.normalkernel_num;
                            }
                            bool flag32 = !Convert.IsDBNull(dr["normalacrosome_num"]);
                            if (flag32)
                            {
                                sampleinfo.normalacrosome_num = Convert.ToInt32(dr["normalacrosome_num"]);
                                sampleinfo.abnormalacrosome_num = sampleinfo.sperm_num - sampleinfo.normalacrosome_num;
                            }
                            bool flag33 = !Convert.IsDBNull(dr["bigacrosome_num"]);
                            if (flag33)
                            {
                                sampleinfo.bigacrosome_num = Convert.ToInt32(dr["bigacrosome_num"]);
                            }
                            bool flag34 = !Convert.IsDBNull(dr["smallacrosome_num"]);
                            if (flag34)
                            {
                                sampleinfo.smallacrosome_num = Convert.ToInt32(dr["smallacrosome_num"]);
                            }
                            bool flag35 = !Convert.IsDBNull(dr["noacrosome_num"]);
                            if (flag35)
                            {
                                sampleinfo.noacrosome_num = Convert.ToInt32(dr["noacrosome_num"]);
                            }
                            bool flag36 = !Convert.IsDBNull(dr["normalvacuoles_num"]);
                            if (flag36)
                            {
                                sampleinfo.normalvacuoles_num = Convert.ToInt32(dr["normalvacuoles_num"]);
                            }
                            bool flag37 = !Convert.IsDBNull(dr["abnormalvacuoles_num"]);
                            if (flag37)
                            {
                                sampleinfo.abnormalvacuoles_num = Convert.ToInt32(dr["abnormalvacuoles_num"]);
                            }
                            bool flag38 = !Convert.IsDBNull(dr["_0vacuoles_num"]);
                            if (flag38)
                            {
                                sampleinfo._0vacuoles_num = Convert.ToInt32(dr["_0vacuoles_num"]);
                            }
                            bool flag39 = !Convert.IsDBNull(dr["_12vacuoles_num"]);
                            if (flag39)
                            {
                                sampleinfo._12vacuoles_num = Convert.ToInt32(dr["_12vacuoles_num"]);
                            }
                            bool flag40 = !Convert.IsDBNull(dr["_3plusvacuoles_num"]);
                            if (flag40)
                            {
                                sampleinfo._3plusvacuoles_num = Convert.ToInt32(dr["_3plusvacuoles_num"]);
                            }
                            bool flag41 = !Convert.IsDBNull(dr["kernelvacuoles_num"]);
                            if (flag41)
                            {
                                sampleinfo.kernelvacuoles_num = Convert.ToInt32(dr["kernelvacuoles_num"]);
                            }
                            bool flag42 = !Convert.IsDBNull(dr["bigvacuoles_num"]);
                            if (flag42)
                            {
                                sampleinfo.bigvacuoles_num = Convert.ToInt32(dr["bigvacuoles_num"]);
                            }
                            bool flag43 = !Convert.IsDBNull(dr["normalmiddlepiece_num"]);
                            if (flag43)
                            {
                                sampleinfo.normalmiddlepiece_num = Convert.ToInt32(dr["normalmiddlepiece_num"]);
                            }
                            bool flag44 = !Convert.IsDBNull(dr["abnormalmiddlepiece_num"]);
                            if (flag44)
                            {
                                sampleinfo.abnormalmiddlepiece_num = Convert.ToInt32(dr["abnormalmiddlepiece_num"]);
                            }
                            bool flag45 = !Convert.IsDBNull(dr["normalmiddlepieceangle_num"]);
                            if (flag45)
                            {
                                sampleinfo.normalmiddlepieceangle_num = Convert.ToInt32(dr["normalmiddlepieceangle_num"]);
                            }
                            bool flag46 = !Convert.IsDBNull(dr["abnormalmiddlepieceangle_num"]);
                            if (flag46)
                            {
                                sampleinfo.abnormalmiddlepieceangle_num = Convert.ToInt32(dr["abnormalmiddlepieceangle_num"]);
                            }
                            bool flag47 = !Convert.IsDBNull(dr["normalmiddlepiecewidth_num"]);
                            if (flag47)
                            {
                                sampleinfo.normalmiddlepiecewidth_num = Convert.ToInt32(dr["normalmiddlepiecewidth_num"]);
                            }
                            bool flag48 = !Convert.IsDBNull(dr["abnormalmiddlepiecewidth_num"]);
                            if (flag48)
                            {
                                sampleinfo.abnormalmiddlepiecewidth_num = Convert.ToInt32(dr["abnormalmiddlepiecewidth_num"]);
                            }
                            bool flag49 = !Convert.IsDBNull(dr["normalshape_num"]);
                            if (flag49)
                            {
                                sampleinfo.normalshape_num = Convert.ToInt32(dr["normalshape_num"]);
                            }
                            bool flag50 = !Convert.IsDBNull(dr["abnormalshape_num"]);
                            if (flag50)
                            {
                                sampleinfo.abnormalshape_num = Convert.ToInt32(dr["abnormalshape_num"]);
                            }
                            bool flag51 = !Convert.IsDBNull(dr["irregularshape_num"]);
                            if (flag51)
                            {
                                sampleinfo.irregularshape_num = Convert.ToInt32(dr["irregularshape_num"]);
                            }
                            bool flag52 = !Convert.IsDBNull(dr["pyriformconeshape_num"]);
                            if (flag52)
                            {
                                sampleinfo.pyriformconeshape_num = Convert.ToInt32(dr["pyriformconeshape_num"]);
                            }
                            bool flag53 = !Convert.IsDBNull(dr["asymmetryshape_num"]);
                            if (flag53)
                            {
                                sampleinfo.asymmetryshape_num = Convert.ToInt32(dr["asymmetryshape_num"]);
                            }
                            bool flag54 = !Convert.IsDBNull(dr["normalTail_num"]);
                            if (flag54)
                            {
                                sampleinfo.normalTail_num = Convert.ToInt32(dr["normalTail_num"]);
                            }
                            bool flag55 = !Convert.IsDBNull(dr["abnormalTail_num"]);
                            if (flag55)
                            {
                                sampleinfo.abnormalTail_num = Convert.ToInt32(dr["abnormalTail_num"]);
                            }
                            bool flag56 = !Convert.IsDBNull(dr["ERC_num"]);
                            if (flag56)
                            {
                                sampleinfo.ERC_num = Convert.ToInt32(dr["ERC_num"]);
                            }
                            bool flag57 = !Convert.IsDBNull(dr["notail_num"]);
                            if (flag57)
                            {
                                sampleinfo.notail_num = Convert.ToInt32(dr["notail_num"]);
                            }
                            bool flag58 = !Convert.IsDBNull(dr["shorttail_num"]);
                            if (flag58)
                            {
                                sampleinfo.shorttail_num = Convert.ToInt32(dr["shorttail_num"]);
                            }
                            bool flag59 = !Convert.IsDBNull(dr["curvetail_num"]);
                            if (flag59)
                            {
                                sampleinfo.curvetail_num = Convert.ToInt32(dr["curvetail_num"]);
                            }
                            bool flag60 = !Convert.IsDBNull(dr["bendtail_num"]);
                            if (flag60)
                            {
                                sampleinfo.bendtail_num = Convert.ToInt32(dr["bendtail_num"]);
                            }
                            bool flag61 = !Convert.IsDBNull(dr["lheadlacro_num"]);
                            if (flag61)
                            {
                                sampleinfo.lheadlacro_num = Convert.ToInt32(dr["lheadlacro_num"]);
                            }
                            bool flag62 = !Convert.IsDBNull(dr["rheadnacro_num"]);
                            if (flag62)
                            {
                                sampleinfo.rheadnacro_num = Convert.ToInt32(dr["rheadnacro_num"]);
                            }
                            bool flag63 = !Convert.IsDBNull(dr["rheadlacro_num"]);
                            if (flag63)
                            {
                                sampleinfo.rheadlacro_num = Convert.ToInt32(dr["rheadlacro_num"]);
                            }
                            bool flag64 = !Convert.IsDBNull(dr["pheadbacro_num"]);
                            if (flag64)
                            {
                                sampleinfo.pheadbacro_num = Convert.ToInt32(dr["pheadbacro_num"]);
                            }
                            bool flag65 = !Convert.IsDBNull(dr["theadlacro_num"]);
                            if (flag65)
                            {
                                sampleinfo.theadlacro_num = Convert.ToInt32(dr["theadlacro_num"]);
                            }
                            bool flag66 = sampleinfo.sperm_num > 0;
                            if (flag66)
                            {
                                sampleinfo.tallhead_rate = 1.0 * (double)sampleinfo.tallhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.shorthead_rate = 1.0 * (double)sampleinfo.shorthead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalheight_rate = 1.0 * (double)sampleinfo.normalheight_num / (double)sampleinfo.sperm_num;
                                sampleinfo.fathead_rate = 1.0 * (double)sampleinfo.fathead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.thinhead_rate = 1.0 * (double)sampleinfo.thinhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalwidth_rate = 1.0 * (double)sampleinfo.normalwidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.roundhead_rate = 1.0 * (double)sampleinfo.roundhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalratio_rate = 1.0 * (double)sampleinfo.normalratio_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalvacuoles_rate = 1.0 * (double)sampleinfo.normalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._0vacuoles_rate = 1.0 * (double)sampleinfo._0vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._12vacuoles_rate = 1.0 * (double)sampleinfo._12vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._3plusvacuoles_rate = 1.0 * (double)sampleinfo._3plusvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.kernelvacuoles_rate = 1.0 * (double)sampleinfo.kernelvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigvacuoles_rate = 1.0 * (double)sampleinfo.bigvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiece_rate = 1.0 * (double)sampleinfo.normalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiece_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.normalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.abnormalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.normalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalshape_rate = 1.0 * (double)sampleinfo.abnormalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.asymmetryshape_rate = 1.0 * (double)sampleinfo.asymmetryshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalshape_rate = 1.0 * (double)sampleinfo.normalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalhead_rate = 1.0 * (double)sampleinfo.normalhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.microhead_rate = 1.0 * (double)sampleinfo.microhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.macrohead_rate = 1.0 * (double)sampleinfo.macrohead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.sharphead_rate = 1.0 * (double)sampleinfo.sharphead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pyriformconeshape_rate = 1.0 * (double)sampleinfo.pyriformconeshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.irregularshape_rate = 1.0 * (double)sampleinfo.irregularshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.noacrosome_rate = 1.0 * (double)sampleinfo.noacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigacrosome_rate = 1.0 * (double)sampleinfo.bigacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.smallacrosome_rate = 1.0 * (double)sampleinfo.smallacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalacrosome_rate = 1.0 * (double)sampleinfo.normalacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalvacuoles_rate = 1.0 * (double)sampleinfo.abnormalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.lheadlacro_rate = 1.0 * (double)sampleinfo.lheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadlacro_rate = 1.0 * (double)sampleinfo.rheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadnacro_rate = 1.0 * (double)sampleinfo.rheadnacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.theadlacro_rate = 1.0 * (double)sampleinfo.theadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pheadbacro_rate = 1.0 * (double)sampleinfo.pheadbacro_num / (double)sampleinfo.sperm_num;
                            }
                        }
                    }
                }
                conn.Close();
            }
            return sampleinfo;
        }

        public static ObservableCollection<Sampleinfo> getSampleInfo(string sampleId)
        {

            string cmdText = "select * from sampleinfo where sampleId=@sampleId";
            return GetModelObjects(cmdText);
        }

        public static bool IsExist(string sampleId)
        {
            bool isexist = false;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select count(id) from Sampleinfo where sampleId=@sampleId", conn);
                cmd.Parameters.AddWithValue("@sampleId", sampleId);
                conn.Open();
                var obj = cmd.ExecuteScalar();
                if (Convert.ToInt32(obj) > 0)
                {
                    isexist = true;
                }
                conn.Close();
            }
            return isexist;
        }

        public static bool ExistSample(int id)
        {
            string cmdText = "select id from sampleinfo where id=@id";
            bool result;
            using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
            {
                using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                {
                    mySqlCommand.Parameters.Add(new SQLiteParameter
                    {
                        ParameterName = "@id",
                        Value = id,
                        DbType = DbType.Int32
                    });
                    mySqlConnection.Open();
                    object obj = mySqlCommand.ExecuteScalar();
                    bool flag = obj == null;
                    if (flag)
                    {
                        result = false;
                    }
                    else
                    {
                        result = true;
                    }
                }
            }
            return result;
        }


        public static bool ExistSampleIdForEdit(string sampleid, int id)
        {
            string cmdText = "select id from sampleinfo where sampleid=@sampleid and id!=@id";
            bool result;
            using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
            {
                using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                {
                    mySqlCommand.Parameters.Add(new SQLiteParameter
                    {
                        ParameterName = "@id",
                        Value = id,
                        DbType = DbType.Int32
                    });
                    mySqlCommand.Parameters.Add(new SQLiteParameter
                    {
                        ParameterName = "@sampleid",
                        Value = sampleid,
                        DbType = DbType.String
                    });
                    mySqlConnection.Open();
                    object obj = mySqlCommand.ExecuteScalar();
                    bool flag = obj == null;
                    if (flag)
                    {
                        result = false;
                    }
                    else
                    {
                        result = true;
                    }
                }
            }
            return result;
        }

        public static bool CreateObject(Sampleinfo obj)
        {
            int num = 0;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                string cmdText = "insert into sampleinfo (patientId,sampleId,patientName,patientage,inspectItem,getSpermWay,abstinenceDays,dilutionRatio,sampleSource,inspectionDoctor,reviewed,reviewer,sendDoctor,sendtime,scantime,purpose,userid,department,checkcode)values(@patientId,@sampleId,@patientName,@patientage,@inspectItem,@getSpermWay,@abstinenceDays,@dilutionRatio,@sampleSource,@inspectionDoctor,@reviewed,@reviewer,@sendDoctor,@sendtime,@scantime,@purpose,@userid,@department,@checkcode)";
                var date = DateTime.Now;

                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                cmd.Parameters.Add(new SQLiteParameter("@patientId", obj.patientId));
                cmd.Parameters.Add(new SQLiteParameter("@sampleId", obj.sampleId.Trim()));
                cmd.Parameters.Add(new SQLiteParameter("@patientName", obj.patientName));
                cmd.Parameters.Add(new SQLiteParameter("@patientage", obj.patientage));
                cmd.Parameters.Add(new SQLiteParameter("@inspectItem", obj.inspectItem));
                cmd.Parameters.Add(new SQLiteParameter("@getSpermWay", obj.getSpermWay));
                cmd.Parameters.Add(new SQLiteParameter("@abstinenceDays", obj.abstinenceDays));
                cmd.Parameters.Add(new SQLiteParameter("@dilutionRatio", obj.dilutionRatio));
                cmd.Parameters.Add(new SQLiteParameter("@sampleSource", obj.sampleSource));
                cmd.Parameters.Add(new SQLiteParameter("@inspectionDoctor", obj.inspectionDoctor));
                cmd.Parameters.Add(new SQLiteParameter("@reviewed", obj.reviewed));
                cmd.Parameters.Add(new SQLiteParameter("@reviewer", obj.reviewer));
                cmd.Parameters.Add(new SQLiteParameter("@sendDoctor", obj.sendDoctor));
                cmd.Parameters.Add(new SQLiteParameter("@sendtime", date));
                cmd.Parameters.Add(new SQLiteParameter("@scantime", obj.scantime));
                cmd.Parameters.Add(new SQLiteParameter("@purpose", obj.purpose));
                cmd.Parameters.Add(new SQLiteParameter("@userid", obj.userid));
                cmd.Parameters.Add(new SQLiteParameter("@department", obj.department));
                cmd.Parameters.Add(new SQLiteParameter("@checkcode", Crypto.DES.MD5(obj.sampleId + date.ToString("yyyy-MM-dd hh:mm:ss"))));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }
        /// <summary>
        /// 创建预加载样本信息
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static bool CreateTestObject()
        {
            int num = 0;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                string cmdText = "insert into sampleinfo (patientId,sampleId,patientName,patientage,inspectItem,getSpermWay,abstinenceDays,dilutionRatio,sampleSource,inspectionDoctor,reviewed,reviewer,sendDoctor,sendtime,scantime,purpose,userid,department,checkcode,processed)values(@patientId,@sampleId,@patientName,@patientage,@inspectItem,@getSpermWay,@abstinenceDays,@dilutionRatio,@sampleSource,@inspectionDoctor,@reviewed,@reviewer,@sendDoctor,@sendtime,@scantime,@purpose,@userid,@department,@checkcode,0)";
                var date = DateTime.Now;

                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                var patientId = Guid.NewGuid(); 
                cmd.Parameters.Add(new SQLiteParameter("@patientId", patientId.ToString()));
                var sampleId = DateTime.Now.ToString("yyyyMMdd")+"_"+ Guid.NewGuid();
                cmd.Parameters.Add(new SQLiteParameter("@sampleId", sampleId));
                cmd.Parameters.Add(new SQLiteParameter("@patientName", ""));
                cmd.Parameters.Add(new SQLiteParameter("@patientage", 30));
                cmd.Parameters.Add(new SQLiteParameter("@inspectItem", "MRF"));
                cmd.Parameters.Add(new SQLiteParameter("@getSpermWay", "手淫"));
                cmd.Parameters.Add(new SQLiteParameter("@abstinenceDays", 30));
                cmd.Parameters.Add(new SQLiteParameter("@dilutionRatio", 1));
                cmd.Parameters.Add(new SQLiteParameter("@sampleSource", "冻存"));
                cmd.Parameters.Add(new SQLiteParameter("@inspectionDoctor", 12));
                cmd.Parameters.Add(new SQLiteParameter("@reviewed", 1));
                cmd.Parameters.Add(new SQLiteParameter("@reviewer", 212));
                cmd.Parameters.Add(new SQLiteParameter("@sendDoctor", ""));
                cmd.Parameters.Add(new SQLiteParameter("@sendtime", date));
                cmd.Parameters.Add(new SQLiteParameter("@scantime", date));
                cmd.Parameters.Add(new SQLiteParameter("@purpose", ""));
                cmd.Parameters.Add(new SQLiteParameter("@userid", 0));
                cmd.Parameters.Add(new SQLiteParameter("@department", "男科"));
                cmd.Parameters.Add(new SQLiteParameter("@checkcode", Crypto.DES.MD5(sampleId + date.ToString("yyyy-MM-dd hh:mm:ss"))));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }

        public static bool CreateObjectBase(Sampleinfo obj)
        {
            int num = 0;
            string cmdText = "insert into sampleinfo (patientId,sampleId,patientName,patientage,inspectItem,getSpermWay,abstinenceDays,dilutionRatio,sampleSource,inspectionDoctor,sendDoctor,sendtime,purpose,userid,department)values(@patientId,@sampleId,@patientName,@patientage,@inspectItem,@getSpermWay,@abstinenceDays,@dilutionRatio,@sampleSource,@inspectionDoctor,@sendDoctor,@sendtime,@purpose,@userid,@department)";
            using (SqlConnection conn = new SqlConnection(connectionString))
            {
                SqlCommand cmd = new SqlCommand(cmdText, conn);
                cmd.Parameters.Add(new SQLiteParameter("@patientId", obj.patientId));
                cmd.Parameters.Add(new SQLiteParameter("@sampleId", obj.sampleId.Trim()));
                cmd.Parameters.Add(new SQLiteParameter("@patientName", obj.patientName));
                cmd.Parameters.Add(new SQLiteParameter("@patientage", obj.patientage));
                cmd.Parameters.Add(new SQLiteParameter("@inspectItem", obj.inspectItem));
                cmd.Parameters.Add(new SQLiteParameter("@getSpermWay", obj.getSpermWay));
                cmd.Parameters.Add(new SQLiteParameter("@abstinenceDays", obj.abstinenceDays));
                cmd.Parameters.Add(new SQLiteParameter("@dilutionRatio", obj.dilutionRatio));
                cmd.Parameters.Add(new SQLiteParameter("@sampleSource", obj.sampleSource));
                cmd.Parameters.Add(new SQLiteParameter("@inspectionDoctor", obj.inspectionDoctor));
                cmd.Parameters.Add(new SQLiteParameter("@sendDoctor", obj.sendDoctor));
                cmd.Parameters.Add(new SQLiteParameter("@sendtime", obj.sendtime));
                cmd.Parameters.Add(new SQLiteParameter("@purpose", obj.purpose));
                cmd.Parameters.Add(new SQLiteParameter("@userid", obj.userid));
                cmd.Parameters.Add(new SQLiteParameter("@department", obj.department));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }

        public static bool UpdateObject(Sampleinfo obj)
        {
            int num = 0;
            string cmdText = "update sampleinfo set patientId=@patientId,sampleId=@sampleId,patientName=@patientName,patientage=@patientage,department=@department,inspectItem=@inspectItem,getSpermWay=@getSpermWay,abstinenceDays=@abstinenceDays,dilutionRatio=@dilutionRatio,sampleSource=@sampleSource,inspectionDoctor=@inspectionDoctor,sendDoctor=@sendDoctor,reviewed=@reviewed,reviewer=@reviewer,sendtime=@sendtime,purpose=@purpose,field_num=@field_num,department=@department where id=@id";
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                cmd.Parameters.Add(new SQLiteParameter("@patientId", obj.patientId));
                cmd.Parameters.Add(new SQLiteParameter("@sampleId", obj.sampleId));
                cmd.Parameters.Add(new SQLiteParameter("@patientName", obj.patientName));
                cmd.Parameters.Add(new SQLiteParameter("@patientage", obj.patientage));
                cmd.Parameters.Add(new SQLiteParameter("@inspectItem", obj.inspectItem));
                cmd.Parameters.Add(new SQLiteParameter("@getSpermWay", obj.getSpermWay));
                cmd.Parameters.Add(new SQLiteParameter("@abstinenceDays", obj.abstinenceDays));
                cmd.Parameters.Add(new SQLiteParameter("@dilutionRatio", obj.dilutionRatio));
                cmd.Parameters.Add(new SQLiteParameter("@sampleSource", obj.sampleSource));
                cmd.Parameters.Add(new SQLiteParameter("@inspectionDoctor", obj.inspectionDoctor));
                cmd.Parameters.Add(new SQLiteParameter("@sendDoctor", obj.sendDoctor));
                cmd.Parameters.Add(new SQLiteParameter("@reviewed", obj.reviewed));
                cmd.Parameters.Add(new SQLiteParameter("@reviewer", obj.reviewer));
                cmd.Parameters.Add(new SQLiteParameter("@sendtime", obj.sendtime));
                cmd.Parameters.Add(new SQLiteParameter("@purpose", obj.purpose));
                cmd.Parameters.Add(new SQLiteParameter("@field_num", obj.field_num));
                cmd.Parameters.Add(new SQLiteParameter("@department", obj.department));
                cmd.Parameters.Add(new SQLiteParameter("@id", obj.Id));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }

        public static bool DeleteObject(int id)
        {
            var num = 0;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("delete from Sampleinfo where id=@id", conn);
                cmd.Parameters.Add(new SQLiteParameter("@id", id));
                conn.Open();
                num += cmd.ExecuteNonQuery();
                conn.Close();
            }
            bool flag = num == 0;
            return !flag;
        }


        public static ObservableCollection<Sampleinfo> GetSampleInfo()
        {
            string cmdText = "select * from sampleinfo order by sendtime desc";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sampleinfo> GetSampleInfo(string sampleId)
        {
            string cmdText = "select * from sampleinfo where sampleId='" + sampleId+ "'";
            return GetModelObjects(cmdText);
        }

        public static ObservableCollection<Sampleinfo> GetSampleInfoByPage(string wheresql, int pageNo,int pageCount)
        {
            string cmdText = "select * from sampleinfo where " + wheresql + " order by ID desc limit " + (pageNo - 1) * 20 + ","+ pageCount;
            return GetModelObjects(cmdText);
        }

        public static int GetSampleCount(string wheresql)
        {
            int count = 0;
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand("select count(Id) from sampleinfo where " + wheresql, conn);
                conn.Open();
                var num = cmd.ExecuteScalar();
                conn.Close();
                count = Convert.ToInt32(num);
            }
            return count;
        }


        private static ObservableCollection<Sampleinfo> GetModelObjects(string cmdText)
        {
            ObservableCollection<Sampleinfo> observableCollection = new ObservableCollection<Sampleinfo>();
            using (SQLiteConnection conn = new SQLiteConnection(connectionString))
            {
                SQLiteCommand cmd = new SQLiteCommand(cmdText, conn);
                conn.Open();
                using ( SQLiteDataReader dr = cmd.ExecuteReader())
                {
                    while (dr.Read())
                    {
                        Sampleinfo sampleinfo = new Sampleinfo();
                        bool flag2 = !Convert.IsDBNull(dr["sendtime"]);
                        if (flag2)
                        {
                            sampleinfo.sendtime = dr["sendtime"].ToString();
                        }
                        bool flag3 = !Convert.IsDBNull(dr["sampleId"]);
                        if (flag3)
                        {
                            sampleinfo.sampleId = Convert.ToString(dr["sampleId"]);
                        }
                        string a = "";
                        bool flag4 = !Convert.IsDBNull(dr["checkcode"]);
                        if (flag4)
                        {
                            a = Convert.ToString(dr["checkcode"]);
                        }
                        string b = Crypto.DES.MD5(sampleinfo.sampleId + sampleinfo.sendtime.ToString());
                        bool flag5 = Basic.useCheckcode && a != b;
                        if (flag5)
                        {
                            //result = null;代表着空值 无需返回这个实体
                        }
                        else
                        {
                            sampleinfo.Id = Convert.ToInt32(dr["id"]);
                            sampleinfo.patientId = Convert.ToString(dr["patientId"]);
                            sampleinfo.patientName = Convert.ToString(dr["patientName"]);
                            sampleinfo.patientage = Convert.ToInt32(dr["patientage"]);
                            sampleinfo.inspectItem = Convert.ToString(dr["inspectItem"]);
                            string inspectItem = sampleinfo.inspectItem;
                            if (!(inspectItem == "MRF"))
                            {
                                if (!(inspectItem == "DFI"))
                                {
                                    if (inspectItem == "MAT")
                                    {
                                        sampleinfo.inspectItemCHN = "核成熟度";
                                    }
                                }
                                else
                                {
                                    sampleinfo.inspectItemCHN = "DFI";
                                }
                            }
                            else
                            {
                                sampleinfo.inspectItemCHN = "形态";
                            }
                            sampleinfo.getSpermWay = Convert.ToString(dr["getSpermWay"]);
                            sampleinfo.abstinenceDays = Convert.ToInt32(dr["abstinenceDays"]);
                            sampleinfo.dilutionRatio = Convert.ToDouble(dr["dilutionRatio"]);
                            sampleinfo.sampleSource = Convert.ToString(dr["sampleSource"]);
                            sampleinfo.department = Convert.ToString(dr["department"]);
                            sampleinfo.inspectionDoctor = Convert.ToString(dr["inspectionDoctor"]);
                            bool flag6 = !Convert.IsDBNull(dr["userid"]);
                            if (flag6)
                            {
                                sampleinfo.userid = Convert.ToInt32(dr["userid"]);
                            }
                            sampleinfo.sendDoctor = Convert.ToString(dr["sendDoctor"]);
                            bool flagscaned = !Convert.IsDBNull(dr["scanned"]);
                            if (flagscaned)
                            {
                                sampleinfo.scanned = Convert.ToInt32(dr["scanned"]);
                            }
                            bool flag7 = !Convert.IsDBNull(dr["scantime"]);
                            if (flag7)
                            {
                                sampleinfo.scantime = dr["scantime"].ToString();
                            }
                            sampleinfo.scanmode = Convert.ToString(dr["scanmode"]);
                            bool flagpro = !Convert.IsDBNull(dr["processed"]);
                            if (flagpro)
                            {
                                sampleinfo.processed = Convert.ToInt32(dr["processed"]);
                            }
                            bool flag8 = !Convert.IsDBNull(dr["processed_time"]);
                            if (flag8)
                            {
                                sampleinfo.processed_time = dr["processed_time"].ToString();
                            }
                            bool flagrev = !Convert.IsDBNull(dr["reviewed"]);
                            if (flagrev)
                            {
                                sampleinfo.reviewed = Convert.ToInt32(dr["reviewed"]);
                            }
                            sampleinfo.reviewer = Convert.ToString(dr["reviewer"]);
                            bool flag9 = !Convert.IsDBNull(dr["review_time"]);
                            if (flag9)
                            {
                                sampleinfo.review_time = dr["review_time"].ToString();
                            }
                            sampleinfo.processed_np = dr["processed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["processed_np"]);
                            bool flag10 = !Convert.IsDBNull(dr["processed_np_time"]);
                            if (flag10)
                            {
                                sampleinfo.processed_np_time = dr["processed_np_time"].ToString();
                            }
                            sampleinfo.reviewed_np = dr["reviewed_np"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reviewed_np"]);
                            sampleinfo.reviewer_np = Convert.ToString(dr["reviewer_np"]);
                            bool flag11 = !Convert.IsDBNull(dr["review_np_time"]);
                            if (flag11)
                            {
                                sampleinfo.review_np_time = dr["review_np_time"].ToString();
                            }
                            sampleinfo.reportprinted = dr["reportprinted"] == DBNull.Value ? 0 : Convert.ToInt32(dr["reportprinted"]);
                            bool flag12 = !Convert.IsDBNull(dr["reporttime"]);
                            if (flag12)
                            {
                                sampleinfo.reporttime = dr["reporttime"].ToString();
                            }
                            sampleinfo.reportcontent = Convert.ToString(dr["reportcontent"]);
                            sampleinfo.diagnosis = Convert.ToString(dr["diagnosis"]);
                            sampleinfo.purpose = Convert.ToString(dr["purpose"]);
                            bool flag13 = !Convert.IsDBNull(dr["field_num"]);
                            if (flag13)
                            {
                                sampleinfo.field_num = Convert.ToInt32(dr["field_num"]);
                            }
                            bool flag14 = !Convert.IsDBNull(dr["sperm_num"]);
                            if (flag14)
                            {
                                sampleinfo.sperm_num = Convert.ToInt32(dr["sperm_num"]);
                            }
                            bool flag15 = !Convert.IsDBNull(dr["normal_num"]);
                            if (flag15)
                            {
                                sampleinfo.normal_num = Convert.ToInt32(dr["normal_num"]);
                            }
                            bool flag16 = !Convert.IsDBNull(dr["abnormal_num"]);
                            if (flag16)
                            {
                                sampleinfo.abnormal_num = Convert.ToInt32(dr["abnormal_num"]);
                            }
                            bool flag17 = !Convert.IsDBNull(dr["subclinical_num"]);
                            if (flag17)
                            {
                                sampleinfo.subclinical_num = Convert.ToInt32(dr["subclinical_num"]);
                            }
                            bool flag18 = !Convert.IsDBNull(dr["qualification_rate"]);
                            if (flag18)
                            {
                                sampleinfo.qualification_rate = Convert.ToDouble(dr["qualification_rate"]);
                                sampleinfo.abnormal_rate = 1.0 * (double)sampleinfo.abnormal_num / (double)sampleinfo.sperm_num;
                                sampleinfo.subclinical_rate = 1.0 * (double)sampleinfo.subclinical_num / (double)sampleinfo.sperm_num;
                                sampleinfo.totalPercent = 1.0;
                            }
                            bool flag19 = !Convert.IsDBNull(dr["normalhead_num"]);
                            if (flag19)
                            {
                                sampleinfo.normalhead_num = Convert.ToInt32(dr["normalhead_num"]);
                            }
                            bool flag20 = !Convert.IsDBNull(dr["microhead_num"]);
                            if (flag20)
                            {
                                sampleinfo.microhead_num = Convert.ToInt32(dr["microhead_num"]);
                            }
                            bool flag21 = !Convert.IsDBNull(dr["macrohead_num"]);
                            if (flag21)
                            {
                                sampleinfo.macrohead_num = Convert.ToInt32(dr["macrohead_num"]);
                            }
                            bool flag22 = !Convert.IsDBNull(dr["normalheight_num"]);
                            if (flag22)
                            {
                                sampleinfo.normalheight_num = Convert.ToInt32(dr["normalheight_num"]);
                            }
                            bool flag23 = !Convert.IsDBNull(dr["tallhead_num"]);
                            if (flag23)
                            {
                                sampleinfo.tallhead_num = Convert.ToInt32(dr["tallhead_num"]);
                            }
                            bool flag24 = !Convert.IsDBNull(dr["shorthead_num"]);
                            if (flag24)
                            {
                                sampleinfo.shorthead_num = Convert.ToInt32(dr["shorthead_num"]);
                            }
                            bool flag25 = !Convert.IsDBNull(dr["normalwidth_num"]);
                            if (flag25)
                            {
                                sampleinfo.normalwidth_num = Convert.ToInt32(dr["normalwidth_num"]);
                            }
                            bool flag26 = !Convert.IsDBNull(dr["fathead_num"]);
                            if (flag26)
                            {
                                sampleinfo.fathead_num = Convert.ToInt32(dr["fathead_num"]);
                            }
                            bool flag27 = !Convert.IsDBNull(dr["thinhead_num"]);
                            if (flag27)
                            {
                                sampleinfo.thinhead_num = Convert.ToInt32(dr["thinhead_num"]);
                            }
                            bool flag28 = !Convert.IsDBNull(dr["normalratio_num"]);
                            if (flag28)
                            {
                                sampleinfo.normalratio_num = Convert.ToInt32(dr["normalratio_num"]);
                            }
                            bool flag29 = !Convert.IsDBNull(dr["sharphead_num"]);
                            if (flag29)
                            {
                                sampleinfo.sharphead_num = Convert.ToInt32(dr["sharphead_num"]);
                            }
                            bool flag30 = !Convert.IsDBNull(dr["roundhead_num"]);
                            if (flag30)
                            {
                                sampleinfo.roundhead_num = Convert.ToInt32(dr["roundhead_num"]);
                            }
                            bool flag31 = !Convert.IsDBNull(dr["normalkernel_num"]);
                            if (flag31)
                            {
                                sampleinfo.normalkernel_num = Convert.ToInt32(dr["normalkernel_num"]);
                                sampleinfo.abnormalkernel_num = sampleinfo.sperm_num - sampleinfo.normalkernel_num;
                            }
                            bool flag32 = !Convert.IsDBNull(dr["normalacrosome_num"]);
                            if (flag32)
                            {
                                sampleinfo.normalacrosome_num = Convert.ToInt32(dr["normalacrosome_num"]);
                                sampleinfo.abnormalacrosome_num = sampleinfo.sperm_num - sampleinfo.normalacrosome_num;
                            }
                            bool flag33 = !Convert.IsDBNull(dr["bigacrosome_num"]);
                            if (flag33)
                            {
                                sampleinfo.bigacrosome_num = Convert.ToInt32(dr["bigacrosome_num"]);
                            }
                            bool flag34 = !Convert.IsDBNull(dr["smallacrosome_num"]);
                            if (flag34)
                            {
                                sampleinfo.smallacrosome_num = Convert.ToInt32(dr["smallacrosome_num"]);
                            }
                            bool flag35 = !Convert.IsDBNull(dr["noacrosome_num"]);
                            if (flag35)
                            {
                                sampleinfo.noacrosome_num = Convert.ToInt32(dr["noacrosome_num"]);
                            }
                            bool flag36 = !Convert.IsDBNull(dr["normalvacuoles_num"]);
                            if (flag36)
                            {
                                sampleinfo.normalvacuoles_num = Convert.ToInt32(dr["normalvacuoles_num"]);
                            }
                            bool flag37 = !Convert.IsDBNull(dr["abnormalvacuoles_num"]);
                            if (flag37)
                            {
                                sampleinfo.abnormalvacuoles_num = Convert.ToInt32(dr["abnormalvacuoles_num"]);
                            }
                            bool flag38 = !Convert.IsDBNull(dr["_0vacuoles_num"]);
                            if (flag38)
                            {
                                sampleinfo._0vacuoles_num = Convert.ToInt32(dr["_0vacuoles_num"]);
                            }
                            bool flag39 = !Convert.IsDBNull(dr["_12vacuoles_num"]);
                            if (flag39)
                            {
                                sampleinfo._12vacuoles_num = Convert.ToInt32(dr["_12vacuoles_num"]);
                            }
                            bool flag40 = !Convert.IsDBNull(dr["_3plusvacuoles_num"]);
                            if (flag40)
                            {
                                sampleinfo._3plusvacuoles_num = Convert.ToInt32(dr["_3plusvacuoles_num"]);
                            }
                            bool flag41 = !Convert.IsDBNull(dr["kernelvacuoles_num"]);
                            if (flag41)
                            {
                                sampleinfo.kernelvacuoles_num = Convert.ToInt32(dr["kernelvacuoles_num"]);
                            }
                            bool flag42 = !Convert.IsDBNull(dr["bigvacuoles_num"]);
                            if (flag42)
                            {
                                sampleinfo.bigvacuoles_num = Convert.ToInt32(dr["bigvacuoles_num"]);
                            }
                            bool flag43 = !Convert.IsDBNull(dr["normalmiddlepiece_num"]);
                            if (flag43)
                            {
                                sampleinfo.normalmiddlepiece_num = Convert.ToInt32(dr["normalmiddlepiece_num"]);
                            }
                            bool flag44 = !Convert.IsDBNull(dr["abnormalmiddlepiece_num"]);
                            if (flag44)
                            {
                                sampleinfo.abnormalmiddlepiece_num = Convert.ToInt32(dr["abnormalmiddlepiece_num"]);
                            }
                            bool flag45 = !Convert.IsDBNull(dr["normalmiddlepieceangle_num"]);
                            if (flag45)
                            {
                                sampleinfo.normalmiddlepieceangle_num = Convert.ToInt32(dr["normalmiddlepieceangle_num"]);
                            }
                            bool flag46 = !Convert.IsDBNull(dr["abnormalmiddlepieceangle_num"]);
                            if (flag46)
                            {
                                sampleinfo.abnormalmiddlepieceangle_num = Convert.ToInt32(dr["abnormalmiddlepieceangle_num"]);
                            }
                            bool flag47 = !Convert.IsDBNull(dr["normalmiddlepiecewidth_num"]);
                            if (flag47)
                            {
                                sampleinfo.normalmiddlepiecewidth_num = Convert.ToInt32(dr["normalmiddlepiecewidth_num"]);
                            }
                            bool flag48 = !Convert.IsDBNull(dr["abnormalmiddlepiecewidth_num"]);
                            if (flag48)
                            {
                                sampleinfo.abnormalmiddlepiecewidth_num = Convert.ToInt32(dr["abnormalmiddlepiecewidth_num"]);
                            }
                            bool flag49 = !Convert.IsDBNull(dr["normalshape_num"]);
                            if (flag49)
                            {
                                sampleinfo.normalshape_num = Convert.ToInt32(dr["normalshape_num"]);
                            }
                            bool flag50 = !Convert.IsDBNull(dr["abnormalshape_num"]);
                            if (flag50)
                            {
                                sampleinfo.abnormalshape_num = Convert.ToInt32(dr["abnormalshape_num"]);
                            }
                            bool flag51 = !Convert.IsDBNull(dr["irregularshape_num"]);
                            if (flag51)
                            {
                                sampleinfo.irregularshape_num = Convert.ToInt32(dr["irregularshape_num"]);
                            }
                            bool flag52 = !Convert.IsDBNull(dr["pyriformconeshape_num"]);
                            if (flag52)
                            {
                                sampleinfo.pyriformconeshape_num = Convert.ToInt32(dr["pyriformconeshape_num"]);
                            }
                            bool flag53 = !Convert.IsDBNull(dr["asymmetryshape_num"]);
                            if (flag53)
                            {
                                sampleinfo.asymmetryshape_num = Convert.ToInt32(dr["asymmetryshape_num"]);
                            }
                            bool flag54 = !Convert.IsDBNull(dr["normalTail_num"]);
                            if (flag54)
                            {
                                sampleinfo.normalTail_num = Convert.ToInt32(dr["normalTail_num"]);
                            }
                            bool flag55 = !Convert.IsDBNull(dr["abnormalTail_num"]);
                            if (flag55)
                            {
                                sampleinfo.abnormalTail_num = Convert.ToInt32(dr["abnormalTail_num"]);
                            }
                            bool flag56 = !Convert.IsDBNull(dr["ERC_num"]);
                            if (flag56)
                            {
                                sampleinfo.ERC_num = Convert.ToInt32(dr["ERC_num"]);
                            }
                            bool flag57 = !Convert.IsDBNull(dr["notail_num"]);
                            if (flag57)
                            {
                                sampleinfo.notail_num = Convert.ToInt32(dr["notail_num"]);
                            }
                            bool flag58 = !Convert.IsDBNull(dr["shorttail_num"]);
                            if (flag58)
                            {
                                sampleinfo.shorttail_num = Convert.ToInt32(dr["shorttail_num"]);
                            }
                            bool flag59 = !Convert.IsDBNull(dr["curvetail_num"]);
                            if (flag59)
                            {
                                sampleinfo.curvetail_num = Convert.ToInt32(dr["curvetail_num"]);
                            }
                            bool flag60 = !Convert.IsDBNull(dr["bendtail_num"]);
                            if (flag60)
                            {
                                sampleinfo.bendtail_num = Convert.ToInt32(dr["bendtail_num"]);
                            }
                            bool flag61 = !Convert.IsDBNull(dr["lheadlacro_num"]);
                            if (flag61)
                            {
                                sampleinfo.lheadlacro_num = Convert.ToInt32(dr["lheadlacro_num"]);
                            }
                            bool flag62 = !Convert.IsDBNull(dr["rheadnacro_num"]);
                            if (flag62)
                            {
                                sampleinfo.rheadnacro_num = Convert.ToInt32(dr["rheadnacro_num"]);
                            }
                            bool flag63 = !Convert.IsDBNull(dr["rheadlacro_num"]);
                            if (flag63)
                            {
                                sampleinfo.rheadlacro_num = Convert.ToInt32(dr["rheadlacro_num"]);
                            }
                            bool flag64 = !Convert.IsDBNull(dr["pheadbacro_num"]);
                            if (flag64)
                            {
                                sampleinfo.pheadbacro_num = Convert.ToInt32(dr["pheadbacro_num"]);
                            }
                            bool flag65 = !Convert.IsDBNull(dr["theadlacro_num"]);
                            if (flag65)
                            {
                                sampleinfo.theadlacro_num = Convert.ToInt32(dr["theadlacro_num"]);
                            }
                            bool flag66 = sampleinfo.sperm_num > 0;
                            if (flag66)
                            {
                                sampleinfo.tallhead_rate = 1.0 * (double)sampleinfo.tallhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.shorthead_rate = 1.0 * (double)sampleinfo.shorthead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalheight_rate = 1.0 * (double)sampleinfo.normalheight_num / (double)sampleinfo.sperm_num;
                                sampleinfo.fathead_rate = 1.0 * (double)sampleinfo.fathead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.thinhead_rate = 1.0 * (double)sampleinfo.thinhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalwidth_rate = 1.0 * (double)sampleinfo.normalwidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.roundhead_rate = 1.0 * (double)sampleinfo.roundhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalratio_rate = 1.0 * (double)sampleinfo.normalratio_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalvacuoles_rate = 1.0 * (double)sampleinfo.normalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._0vacuoles_rate = 1.0 * (double)sampleinfo._0vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._12vacuoles_rate = 1.0 * (double)sampleinfo._12vacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo._3plusvacuoles_rate = 1.0 * (double)sampleinfo._3plusvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.kernelvacuoles_rate = 1.0 * (double)sampleinfo.kernelvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigvacuoles_rate = 1.0 * (double)sampleinfo.bigvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiece_rate = 1.0 * (double)sampleinfo.normalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiece_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiece_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.normalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepieceangle_rate = 1.0 * (double)sampleinfo.abnormalmiddlepieceangle_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.normalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalmiddlepiecewidth_rate = 1.0 * (double)sampleinfo.abnormalmiddlepiecewidth_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalshape_rate = 1.0 * (double)sampleinfo.abnormalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.asymmetryshape_rate = 1.0 * (double)sampleinfo.asymmetryshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalshape_rate = 1.0 * (double)sampleinfo.normalshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalhead_rate = 1.0 * (double)sampleinfo.normalhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.microhead_rate = 1.0 * (double)sampleinfo.microhead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.macrohead_rate = 1.0 * (double)sampleinfo.macrohead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.sharphead_rate = 1.0 * (double)sampleinfo.sharphead_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pyriformconeshape_rate = 1.0 * (double)sampleinfo.pyriformconeshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.irregularshape_rate = 1.0 * (double)sampleinfo.irregularshape_num / (double)sampleinfo.sperm_num;
                                sampleinfo.noacrosome_rate = 1.0 * (double)sampleinfo.noacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.bigacrosome_rate = 1.0 * (double)sampleinfo.bigacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.smallacrosome_rate = 1.0 * (double)sampleinfo.smallacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.normalacrosome_rate = 1.0 * (double)sampleinfo.normalacrosome_num / (double)sampleinfo.sperm_num;
                                sampleinfo.abnormalvacuoles_rate = 1.0 * (double)sampleinfo.abnormalvacuoles_num / (double)sampleinfo.sperm_num;
                                sampleinfo.lheadlacro_rate = 1.0 * (double)sampleinfo.lheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadlacro_rate = 1.0 * (double)sampleinfo.rheadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.rheadnacro_rate = 1.0 * (double)sampleinfo.rheadnacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.theadlacro_rate = 1.0 * (double)sampleinfo.theadlacro_num / (double)sampleinfo.sperm_num;
                                sampleinfo.pheadbacro_rate = 1.0 * (double)sampleinfo.pheadbacro_num / (double)sampleinfo.sperm_num;
                            }
                        }
                        observableCollection.Add(sampleinfo);
                    }
                    conn.Close();
                }
                conn.Close();
            }
            return observableCollection;
        }


        public static bool UpdateSampleProcessed(Sampleinfo si)
        {
            bool flag = si == null;
            bool result;
            if (flag)
            {
                result = false;
            }
            else
            {
                int num = 0;
                string cmdText = "update sampleinfo set sperm_num=(select count(id) from sperm where sampleId=@sampleId and valid=1),normal_num=(select count(id) from sperm where sampleId=@sampleId and isNormal=true and valid=1),abnormal_num=(select count(id) from sperm where sampleId=@sampleId and isNormal=false and valid=1) ,subclinical_num=(select count(id) from sperm where sampleId=@sampleId and isNormal=2 and valid=1) ,qualification_rate=(select sum(normal_num)/sum(sperm_num) from samplephoto where sampleId=@sampleId and processed=1),processed_time=@processed_time  where sampleId=@sampleId";
                try
                {
                    using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
                    {
                        using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                        {
                            mySqlCommand.Parameters.AddWithValue("@sampleId", si.sampleId);
                            mySqlCommand.Parameters.AddWithValue("@processed_time", DateTime.Now);
                            mySqlConnection.Open();
                            num += mySqlCommand.ExecuteNonQuery();
                        }
                    }
                    bool flag2 = num == 0;
                    if (flag2)
                    {
                        result = false;
                    }
                    else
                    {
                        result = true;
                    }
                }
                catch
                {
                    result = false;
                }
            }
            return result;
        }

        public static bool UpdateSampleProcessed(Sampleinfo si, int mode)
        {
            bool flag = si == null;
            bool result;
            if (flag)
            {
                result = false;
            }
            else
            {
                int num = 0;
                string str = (mode == 1) ? "processed_time" : ((mode == 2) ? "review_time" : "reporttime");
                string cmdText = "update sampleinfo set sperm_num=(select count(id) from sperm where sampleId=@sampleId and valid=1),normal_num=(select count(id) from sperm where sampleId=@sampleId and isNormal=true and valid=1),abnormal_num=(select count(id) from sperm where sampleId=@sampleId and isNormal=false and valid=1) ,subclinical_num=(select count(id) from sperm where sampleId=@sampleId and isNormal=2 and valid=1) ,qualification_rate=(select sum(normal_num)/sum(sperm_num) from samplephoto where sampleId=@sampleId and processed=1)," + str + "=@processed_time  where sampleId=@sampleId";
                bool flag2 = mode == 3;
                if (flag2)
                {
                    cmdText = "update sampleinfo set reporttime=@processed_time where sampleId=@sampleId";
                }
                try
                {
                    using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
                    {
                        using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                        {
                            mySqlCommand.Parameters.AddWithValue("@sampleId", si.sampleId);
                            mySqlCommand.Parameters.AddWithValue("@processed_time", DateTime.Now);
                            mySqlConnection.Open();
                            num += mySqlCommand.ExecuteNonQuery();
                        }
                    }
                }
                catch
                {
                    return false;
                }
                bool flag3 = si.inspectItem == "MRF";
                if (flag3)
                {
                    UpdateSampleProcessedA(si);
                }
                bool flag4 = num == 0;
                result = !flag4;
            }
            return result;
        }

        public static bool UpdateSampleProcessedA(Sampleinfo si)
        {
            int num = 0;
            string cmdText = "update sampleinfo set  normalshape_num=(select count(id) from sperm where sampleId=@sampleId and shapeType=0 and valid=1) ,abnormalshape_num=(select count(id) from sperm where sampleId=@sampleId and shapeType>0 and valid=1) ,microhead_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&4>0 and valid=1) ,macrohead_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&8>0 and valid=1) ,thinhead_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&2>0 and valid=1) ,roundhead_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&16>0 and valid=1) ,sharphead_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&1>0 and valid=1) ,irregularshape_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&64>0 and valid=1) ,pyriformconeshape_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&32>0 and valid=1) ,normalacrosome_num=(select count(id) from sperm where sampleId=@sampleId and acrosomeType&32>0 and valid=1) ,noacrosome_num=(select count(id) from sperm where sampleId=@sampleId and acrosomeType&1>0 and valid=1) ,smallacrosome_num=(select count(id) from sperm where sampleId=@sampleId and acrosomeType&2>0 and valid=1) ,bigacrosome_num=(select count(id) from sperm where sampleId=@sampleId and acrosomeType&4>0 and valid=1) ,abnormalvacuoles_num=(select count(id) from sperm where sampleId=@sampleId and acrosomeType&8>0 and valid=1) ,bigvacuoles_num=(select count(id) from sperm where sampleId=@sampleId and acrosomeType&64>0 and valid=1) ,normalkernel_num=(select count(id) from sperm where sampleId=@sampleId and (kernelType&16<=0 and kernelType&8<=0 )and valid=1) ,kernelvacuoles_num=(select count(id) from sperm where sampleId=@sampleId and (kernelType&8>0 or kernelType&16>0)and valid=1) ,lheadlacro_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&4>0 and acrosomeType&2>0 and valid=1) ,rheadnacro_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&16>0 and acrosomeType&1>0 and valid=1) ,rheadlacro_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&16>0 and acrosomeType&2>0 and valid=1) ,theadlacro_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&1>0 and acrosomeType&2>0 and valid=1) ,pheadbacro_num=(select count(id) from sperm where sampleId=@sampleId and shapeType&32>0 and acrosomeType&4>0 and valid=1) ,normalmiddlepiece_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&1<=0 and middlePieceType&2<=0 and middlePieceType&4<=0 and valid=1) ,abnormalmiddlepiece_num=(select count(id) from sperm where sampleId=@sampleId and (middlePieceType&1>0 or middlePieceType&2>0 or middlePieceType&4>0) and valid=1) ,normalmiddlepieceangle_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&1<=0 and valid=1) ,abnormalmiddlepieceangle_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&1>0 and valid=1) ,normalmiddlepiecewidth_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&2<=0 and valid=1) ,abnormalmiddlepiecewidth_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&2>0 and valid=1) ,ERC_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&4>0 and valid=1) ,notail_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&8>0 and valid=1) ,shorttail_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&16>0 and valid=1) ,curvetail_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&32>0 and valid=1) ,bendtail_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&64>0 and valid=1) ,normalTail_num=(select count(id) from sperm where sampleId=@sampleId and middlePieceType&8<=0 and middlePieceType&16<=0 and middlePieceType&32<=0 and middlePieceType&64<=0 and valid=1) ,abnormalTail_num=(select count(id) from sperm where sampleId=@sampleId and (middlePieceType&8>0 or middlePieceType&16>0 or middlePieceType&32>0  or middlePieceType&64>0) and valid=1)  where sampleId=@sampleId";
            bool result;
            try
            {
                using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
                {
                    using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                    {
                        mySqlCommand.Parameters.AddWithValue("@sampleId", si.sampleId);
                        mySqlConnection.Open();
                        num += mySqlCommand.ExecuteNonQuery();
                    }
                }
                bool flag = num == 0;
                if (flag)
                {
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            catch
            {
                result = false;
            }
            return result;
        }

        public static bool UpdateSampleStatus(Sampleinfo si, int status)
        {
            bool flag = si == null;
            bool result;
            if (flag)
            {
                result = false;
            }
            else
            {
                int num = 0;
                string cmdText = "update sampleinfo set processed=@processed where sampleId=@sampleId";
                try
                {
                    using (SQLiteConnection mySqlConnection = new SQLiteConnection(connectionString))
                    {
                        using (SQLiteCommand mySqlCommand = new SQLiteCommand(cmdText, mySqlConnection))
                        {
                            mySqlCommand.Parameters.AddWithValue("@sampleId", si.sampleId);
                            mySqlCommand.Parameters.AddWithValue("@processed", status);
                            mySqlConnection.Open();
                            num += mySqlCommand.ExecuteNonQuery();
                        }
                    }
                    bool flag2 = num == 0;
                    if (flag2)
                    {
                        result = false;
                    }
                    else
                    {
                        result = true;
                    }
                }
                catch
                {
                    result = false;
                }
            }
            return result;
        }
    }
}
