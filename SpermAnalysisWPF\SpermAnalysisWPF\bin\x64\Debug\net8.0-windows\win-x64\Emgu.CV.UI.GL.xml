<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Emgu.CV.UI.GL</name>
    </assembly>
    <members>
        <member name="F:Emgu.CV.UI.GLView.GLImageView._textureRotations">
            <summary>
            Individual texture rotations
            </summary>
        </member>
        <member name="M:Emgu.CV.UI.GLView.GLImageView.UpdateRectangleByTouch(Emgu.CV.UI.GLView.GLImageView.TouchType,System.Drawing.PointF,System.Drawing.PointF,System.Int32)">
            <summary>
            Update the rectangles location by a touch event
            </summary>
            <param name="type">The type of touch</param>
            <param name="viewportStartPoint">The start point of the touch, may be PointF.Empty if touch type is down</param>
            <param name="viewportEndPoint">The end point of the touch</param>
            <param name="rectangleIndex">The index of the rectangle when the shape is to be updated.</param>
        </member>
        <member name="P:Emgu.CV.UI.GLView.GLImageView.Rectangles">
            <summary>
            The rectangles to be drawed. (0,0) point should be the image texture origin where (1,1) should match the opposite corner of the image.
            </summary>
        </member>
        <member name="P:Emgu.CV.UI.GLView.GLImageView.Rotation">
            <summary>
            Get or set the rotation of the whole GLImageView in degree
            </summary>
        </member>
        <member name="F:Emgu.CV.UI.GLView.GLImageViewer.components">
            <summary>
            Required designer variable.
            </summary>
        </member>
        <member name="M:Emgu.CV.UI.GLView.GLImageViewer.Dispose(System.Boolean)">
            <summary>
            Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:Emgu.CV.UI.GLView.GLImageViewer.InitializeComponent">
            <summary>
            Required method for Designer support - do not modify
            the contents of this method with the code editor.
            </summary>
        </member>
    </members>
</doc>
