@echo off
title 精子形态分析系统 - 彻底禁用GPU模式
color 0C

echo ========================================
echo    精子形态分析系统 - 彻底禁用GPU模式
echo ========================================
echo.
echo 此启动器将彻底禁用GPU，强制使用CPU
echo 解决MX450显卡内存不足问题
echo.

echo [1/4] 彻底禁用CUDA和GPU...
REM 多种方式禁用GPU
set CUDA_VISIBLE_DEVICES=-1
set TF_FORCE_GPU_ALLOW_GROWTH=false
set TF_CPP_MIN_LOG_LEVEL=3
set NVIDIA_VISIBLE_DEVICES=none
set CUDA_DEVICE_ORDER=PCI_BUS_ID

REM 强制TensorFlow使用CPU
set TF_DEVICE_MIN_SYS_MEMORY_IN_MB=4096
set OMP_NUM_THREADS=4
set TF_NUM_INTEROP_THREADS=4
set TF_NUM_INTRAOP_THREADS=4

echo     ✓ CUDA_VISIBLE_DEVICES=-1
echo     ✓ NVIDIA_VISIBLE_DEVICES=none
echo     ✓ 强制CPU线程配置

echo.
echo [2/4] 备份并重命名CUDA库文件...
REM 临时重命名CUDA相关DLL，防止TensorFlow加载
if exist "cudart64_110.dll" (
    if not exist "cudart64_110.dll.backup" (
        ren "cudart64_110.dll" "cudart64_110.dll.backup"
        echo     ✓ 已备份 cudart64_110.dll
    )
)

if exist "cublas64_11.dll" (
    if not exist "cublas64_11.dll.backup" (
        ren "cublas64_11.dll" "cublas64_11.dll.backup"
        echo     ✓ 已备份 cublas64_11.dll
    )
)

if exist "curand64_10.dll" (
    if not exist "curand64_10.dll.backup" (
        ren "curand64_10.dll" "curand64_10.dll.backup"
        echo     ✓ 已备份 curand64_10.dll
    )
)

if exist "cudnn64_8.dll" (
    if not exist "cudnn64_8.dll.backup" (
        ren "cudnn64_8.dll" "cudnn64_8.dll.backup"
        echo     ✓ 已备份 cudnn64_8.dll
    )
)

echo     ✓ CUDA库文件已临时禁用

echo.
echo [3/4] 创建CPU专用配置...
echo [GPU设置] > gpu_config.ini
echo ForceUseCPU=true >> gpu_config.ini
echo GPUMemoryOptimized=false >> gpu_config.ini
echo GPUMemoryLimit=0 >> gpu_config.ini
echo EnableTailProcessing=true >> gpu_config.ini
echo AutoDetectGPU=false >> gpu_config.ini
echo     ✓ 已创建CPU专用配置文件

echo.
echo [4/4] 启动程序...
echo     正在启动精子形态分析系统（纯CPU模式）...
echo     警告：CPU模式运行速度很慢，尾巴处理可能需要1-2分钟
echo.

REM 启动程序
start "" "SpermFormAnalysis.exe"

echo     ✓ 程序已启动（纯CPU模式）
echo.
echo ==========================================
echo                重要提示
echo ==========================================
echo 1. 程序现在运行在纯CPU模式
echo 2. 尾巴信息处理需要1-2分钟，请耐心等待
echo 3. 不要同时运行多个分析任务
echo 4. 关闭程序后，CUDA库文件会自动恢复
echo.
echo 如果要恢复GPU模式，请运行"恢复GPU模式.bat"
echo.
echo 按任意键继续...
pause >nul

echo.
echo 程序运行中，请不要关闭此窗口
echo 关闭程序后按任意键恢复CUDA库文件
pause >nul

echo.
echo [恢复] 正在恢复CUDA库文件...
if exist "cudart64_110.dll.backup" (
    ren "cudart64_110.dll.backup" "cudart64_110.dll"
    echo     ✓ 已恢复 cudart64_110.dll
)

if exist "cublas64_11.dll.backup" (
    ren "cublas64_11.dll.backup" "cublas64_11.dll"
    echo     ✓ 已恢复 cublas64_11.dll
)

if exist "curand64_10.dll.backup" (
    ren "curand64_10.dll.backup" "curand64_10.dll"
    echo     ✓ 已恢复 curand64_10.dll
)

if exist "cudnn64_8.dll.backup" (
    ren "cudnn64_8.dll.backup" "cudnn64_8.dll"
    echo     ✓ 已恢复 cudnn64_8.dll
)

echo     ✓ CUDA库文件已恢复
echo.
echo 恢复完成！下次启动将使用默认GPU设置
pause
