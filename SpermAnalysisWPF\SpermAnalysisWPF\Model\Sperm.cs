using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SpermAnalysisWPF.Model
{
    [Table("Sperm")]
    public class Sperm
    {
        [Key]
        public int id { get; set; }

        public int spermindex { get; set; }

        public string sampleId { get; set; } = string.Empty;

        public string imageSource { get; set; } = string.Empty;

        public int classid { get; set; }

        public string clsname { get; set; } = string.Empty;

        public int valid { get; set; }

        public string imageName { get; set; } = string.Empty;

        public float xmin { get; set; }

        public float xmax { get; set; }

        public float ymin { get; set; }

        public float ymax { get; set; }

        public float score { get; set; }

        public double spermArea { get; set; }

        public double spermGirth { get; set; }

        public double longAxis { get; set; }

        public double shortAxis { get; set; }

        public double ellipsRatio { get; set; }

        public double acrosomeRatio { get; set; }

        public double acrosomeArea { get; set; }

        public double kernelArea { get; set; }

        public double kernelRatio { get; set; }

        public double middlePieceArea { get; set; }

        public double middlePieceRatio { get; set; }

        public double tailLength { get; set; }

        public double tailAngle { get; set; }

        public int isNormal { get; set; }

        public int shapeType { get; set; }

        public int acrosomeType { get; set; }

        public int kernelType { get; set; }

        public int middlePieceType { get; set; }

        public int tailType { get; set; }

        public string vop_sperm { get; set; } = string.Empty;

        public string vop_acrosome { get; set; } = string.Empty;

        public string vop_kernel { get; set; } = string.Empty;

        public string vop_middlePiece { get; set; } = string.Empty;

        public string vop_tail { get; set; } = string.Empty;

        public DateTime createTime { get; set; }

        public DateTime updateTime { get; set; }

        public Sperm()
        {
            createTime = DateTime.Now;
            updateTime = DateTime.Now;
        }
    }
}
