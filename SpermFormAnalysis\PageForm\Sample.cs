﻿using NPOI.SS.Formula.Functions;
using SpermFormAnalysis.Class;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.Model;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SpermFormAnalysis.PageForm
{
    public partial class Sample : UIForm
    {
        private Sampleinfo model;
        
        public Sample()
        {
            InitializeComponent();
            Init1();
            txt_sampleid0.Text = DateTime.Now.ToString("yyyyMMdd");
        }
        protected override CreateParams CreateParams
        {
            get
            {
                CreateParams cp = base.CreateParams;
                cp.ExStyle |= 0x02000000;
                return cp;
            }
        }
        public Sample(ref Sampleinfo model)
        {
            InitializeComponent();
            Init1();
            this.model = model;
            txt_sampleid0.Text = model.sampleId.Split('-')[0];
            txt_sampleid1.Text = model.sampleId.Split('-')[1];
            txt_sampleid0.Enabled = false;
            txt_sampleid1.Enabled = false;
            txt_Patient_No.Text = model.patientId;
            txt_Patient_Name.Text = model.patientName;
            txt_Patient_Age.Text = model.patientage.ToString();
            txt_abstinenceDays.Text = model.abstinenceDays.ToString();
            txt_inspectionDoctor.Text = model.inspectionDoctor;
            txt_sendDoctor.Text = model.sendDoctor;
            dateTime_Now.Value = DateTime.Now;
            dateTime_sendtime.Value = DateTime.Parse(model.sendtime) ;
            txt_department.Text = model.department;
            txt_getSpermWay.Text = model.getSpermWay;
            txt_Reviewer.Text = model.reviewer;
            if (model.sampleSource == "新鲜")
            {
                rabrabreviewed0.Checked = true;
            }
            else 
            {
                rabreviewed1.Checked = true;
            }
            txt_dilutionRatio.Text = model.dilutionRatio.ToString();
        }

        public void Init1()
        {
            string testerStr = GlobalProperty.Testers;
            this.txt_inspectionDoctor.Items.Clear();
            if (!string.IsNullOrEmpty(testerStr))
            {
                this.txt_inspectionDoctor.Items.AddRange(testerStr.Split("#"));
                this.txt_inspectionDoctor.Text = this.txt_inspectionDoctor.Items[0].ToString();
            }
            string docterStr = GlobalProperty.Doctors;
            this.txt_sendDoctor.Items.Clear();
            if (!string.IsNullOrEmpty(docterStr))
            {
                this.txt_sendDoctor.Items.AddRange(docterStr.Split("#"));
                this.txt_sendDoctor.Text = this.txt_sendDoctor.Items[0].ToString();
            }
            string txt_Reviewerstr = GlobalProperty.Reviewers;
            this.txt_Reviewer.Items.Clear();
            if (!string.IsNullOrEmpty(txt_Reviewerstr))
            {
                this.txt_Reviewer.Items.AddRange(docterStr.Split("#"));
                this.txt_Reviewer.Text = this.txt_Reviewer.Items[0].ToString();
            }
        }

        private void okBtn_Click(object sender, EventArgs e)
        {
            if (!checkInput())
            {
                return;
            }
            if (!string.IsNullOrEmpty(txt_inspectionDoctor.Text.Trim()) && !("#" + GlobalProperty.Testers + "#").Contains("#" + txt_inspectionDoctor.Text.Trim() + "#"))
            {
                GlobalProperty.Testers = GlobalProperty.Testers + "#" + txt_inspectionDoctor.Text.Trim();
            }
            if (!string.IsNullOrEmpty(txt_sendDoctor.Text.Trim()) && !("#" + GlobalProperty.Doctors + "#").Contains("#" + txt_sendDoctor.Text.Trim() + "#"))
            {
                GlobalProperty.Doctors = GlobalProperty.Doctors + "#" + txt_sendDoctor.Text.Trim();
            }
            if (!string.IsNullOrEmpty(txt_Reviewer.Text.Trim()) && !("#" + GlobalProperty.Reviewers + "#").Contains("#" + txt_Reviewer.Text.Trim() + "#"))
            {
                GlobalProperty.Reviewers = GlobalProperty.Reviewers + "#" + txt_Reviewer.Text.Trim();
            }
            this.DialogResult = DialogResult.OK;
        }

        private bool checkTextString(String temp, String strName, int length)
        {
            int ChinaNumber = Regex.Matches(temp, @"[\u4e00-\u9fa5]").Count;//获取字符长度
            if (String.IsNullOrEmpty(temp))//检测是否为空
            {
                ShowErrorTip(strName + "不能为空！");
                return false;
            }
            if (temp.Length + ChinaNumber > length)//检测长度
            {
                //MessageBox.Show(strName + "不能超过" + length.ToString() + "位！", "友情提示");
                ShowErrorTip(strName + "不能超过" + length.ToString() + "位！");
                return false;
            }
            return true;
        }

        private bool checkInput()
        {
            String sampleid0 = this.txt_sampleid0.Text.TrimEnd();
            string sampleid1 = this.txt_sampleid1.Text.TrimEnd();
            if (!checkTextString(sampleid0, "请输入样本编号与序号", 15))
            {
                return false;
            }
            if (!checkTextString(sampleid1, "请输入样本编号与序号", 15))
            {
                return false;
            }


            String Test_ID = this.txt_Patient_No.Text.TrimEnd();
            if (!checkTextString(Test_ID, "请输入门诊号", 25))
            {
                return false;
            }

            string age = this.txt_Patient_Age.Text;
            if (age != "")
            {
                int patient_Age;
                try
                {
                    patient_Age = Convert.ToInt32(age);
                    if (patient_Age < 0)
                    {
                        //MessageBox.Show("你输入的年龄不合法，请重新输入！", "友情提示");
                        ShowErrorTip("你输入的年龄不合法，请重新输入！");
                        return false;
                    }
                }
                catch
                {
                    //MessageBox.Show("你输入的年龄不合法，请重新输入！", "友情提示");
                    ShowErrorTip("你输入的年龄不合法，请重新输入！");
                    return false;
                }
            }

            string Days = txt_abstinenceDays.Text.Trim();
            if (Days != "")
            {
                int abstinenceDays = 7;
                try {
                    abstinenceDays = Convert.ToInt32(Days);
                    if (abstinenceDays < 0)
                    {
                        ShowErrorTip("你输入的禁欲天数不合法，请重新输入！");
                        return false;
                    }
                }
                catch
                {
                    //MessageBox.Show("你输入的年龄不合法，请重新输入！", "友情提示");
                    ShowErrorTip("你输入的禁欲天数不合法，请重新输入！");
                    return false;
                }
            }
            else
            {
                ShowErrorTip("请输入禁欲天数");
                return false;
            }

            string dilutionRatio = txt_dilutionRatio.Text.Trim();
            if (dilutionRatio != "")
            {
                try
                {
                    var ratio = Convert.ToInt32(dilutionRatio);
                    if (ratio < 0)
                    {
                        ShowErrorTip("你输入的稀释比例不合法，请重新输入！");
                        return false;
                    }
                }
                catch
                {
                    //MessageBox.Show("你输入的年龄不合法，请重新输入！", "友情提示");
                    ShowErrorTip("你输入的稀释比例不合法，请重新输入！");
                    return false;
                }
            }

            Sampleinfo sampleinfo = new Sampleinfo();
            sampleinfo.inspectItem = "MRF";//形态
            sampleinfo.sampleId = txt_sampleid0.Text.Trim() + "-" + txt_sampleid1.Text.Trim();
            sampleinfo.sendtime = dateTime_sendtime.Text.ToString();
            sampleinfo.inspectItemCHN = "MRF";
            sampleinfo.getSpermWay = txt_getSpermWay.Text.Trim();//取精方式
            sampleinfo.patientId = txt_Patient_No.Text.Trim();
            sampleinfo.patientName=txt_Patient_Name.Text.Trim();
            sampleinfo.patientage = Convert.ToInt32(txt_Patient_Age.Text.Trim());
            sampleinfo.abstinenceDays = Convert.ToInt32(txt_abstinenceDays.Text.Trim());
            sampleinfo.inspectionDoctor = txt_inspectionDoctor.Text.Trim();
            sampleinfo.sendDoctor = txt_sendDoctor.Text.Trim();
            sampleinfo.scantime = dateTime_Now.Value.ToString();
            sampleinfo.sendtime = dateTime_sendtime.Value.ToString();
            sampleinfo.department = txt_department.Text.Trim();
            sampleinfo.dilutionRatio = Convert.ToDouble(txt_dilutionRatio.Text.Trim());
            sampleinfo.reviewer = txt_Reviewer.Text.Trim();
            if (rabrabreviewed0.Checked)
            {
                sampleinfo.sampleSource = "新鲜";
            }
            else
            {
                sampleinfo.sampleSource = "冻存";
            }

            if (model != null)
            {
                
                model.patientId = txt_Patient_No.Text.Trim();
                model.patientName = txt_Patient_Name.Text.Trim();
                model.patientage = Convert.ToInt32(txt_Patient_Age.Text.Trim());
                model.inspectItem= "MRF";//形态
                model.getSpermWay = txt_getSpermWay.Text.Trim();
                model.abstinenceDays = Convert.ToInt32(txt_abstinenceDays.Text.Trim());
                model.dilutionRatio = Convert.ToDouble(txt_dilutionRatio.Text.Trim());
                model.inspectionDoctor = txt_inspectionDoctor.Text.Trim();
                model.sendDoctor = txt_sendDoctor.Text.Trim();
                model.sendtime = dateTime_sendtime.Text.ToString();
                model.department = txt_department.Text.Trim();
                model.reviewer = txt_Reviewer.Text.Trim();
                if (rabrabreviewed0.Checked)
                {
                    model.reviewed = 0;
                }
                else
                {
                    model.reviewed = 1;
                }

                SampleinfoServices.UpdateObject(model);
            }
            else
            {
                //判断样本id是否存在
                if (!SampleinfoServices.IsExist(sampleinfo.sampleId))
                {
                    SampleinfoServices.CreateObject(sampleinfo);
                }
                else
                {
                    ShowErrorTip("该样本实例已存在！");
                    return false;
                }
            }

            return true;
        }
        public DialogResult _ShowDialog()
        {
            return this.ShowDialog();
        }
        private void resetBtn_Click(object sender, EventArgs e)
        {
            txt_sampleid1.Text = "";
            txt_Patient_No.Text = "";
            txt_Patient_Name.Text = "";
            txt_Patient_Age.Text = "";
            txt_abstinenceDays.Text = "";
            txt_getSpermWay.Text = "";
            txt_dilutionRatio.Text = "";
        }

        private void cancelBtn_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void Sample_Load(object sender, EventArgs e)
        {
            
        }
    }
}
