﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.UnmanagedMemoryStream</name>
  </assembly>
  <members>
    <member name="T:System.IO.UnmanagedMemoryAccessor">
      <summary>Ermöglicht den wahlfreien Zugriff auf nicht verwaltete Speicherblöcke aus verwaltetem Code heraus.</summary>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryAccessor" />-<PERSON><PERSON><PERSON>. </summary>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryAccessor" />-<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>set und Kapazität angegeben sind.</summary>
      <param name="buffer"><PERSON>, der den Accessor enthalten soll.</param>
      <param name="offset">Das Byte, ab dem der Accessor beginnen soll.</param>
      <param name="capacity">Die Größe des zu belegenden Speichers in Bytes.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> plus <paramref name="capacity" /> ist größer als <paramref name="buffer" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="capacity" /> ist kleiner als 0.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="offset" /> plus <paramref name="capacity" /> würde das größere Ende des Adressbereichs umschließen.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryAccessor" />-Klasse, wobei Puffer, Offset, Kapazität und Zugriffsrecht angegeben sind.</summary>
      <param name="buffer">Der Puffer, der den Accessor enthalten soll.</param>
      <param name="offset">Das Byte, ab dem der Accessor beginnen soll.</param>
      <param name="capacity">Die Größe des zu belegenden Speichers in Bytes.</param>
      <param name="access">Der zulässige Typ des Zugriffs auf den Speicher.Die Standardeinstellung ist <see cref="F:System.IO.MemoryMappedFiles.MemoryMappedFileAccess.ReadWrite" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> plus <paramref name="capacity" /> ist größer als <paramref name="buffer" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="capacity" /> ist kleiner als 0.- oder - <paramref name="access" /> ist kein gültiger <see cref="T:System.IO.MemoryMappedFiles.MemoryMappedFileAccess" />-Enumerationswert.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="offset" /> plus <paramref name="capacity" /> würde das größere Ende des Adressbereichs umschließen.</exception>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.CanRead">
      <summary>Bestimmt, ob der Accessor lesbar ist.</summary>
      <returns>true, wenn der Accessor lesbar ist, andernfalls false. </returns>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.CanWrite">
      <summary>Bestimmt, ob in den Accessor geschrieben werden kann.</summary>
      <returns>true, wenn in den Accessor geschrieben werden kann, andernfalls false. </returns>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.Capacity">
      <summary>Ruft die Kapazität des Accessors ab.</summary>
      <returns>Die Kapazität des Accessors.</returns>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Dispose">
      <summary>Gibt alle vom <see cref="T:System.IO.UnmanagedMemoryAccessor" /> verwendeten Ressourcen frei. </summary>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.UnmanagedMemoryAccessor" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei. </summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben. </param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Initialize(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>Legt die Anfangswerte für den Accessor fest.</summary>
      <param name="buffer">Der Puffer, der den Accessor enthalten soll.</param>
      <param name="offset">Das Byte, ab dem der Accessor beginnen soll.</param>
      <param name="capacity">Die Größe des zu belegenden Speichers in Bytes.</param>
      <param name="access">Der zulässige Typ des Zugriffs auf den Speicher.Die Standardeinstellung ist <see cref="F:System.IO.MemoryMappedFiles.MemoryMappedFileAccess.ReadWrite" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> plus <paramref name="capacity" /> ist größer als <paramref name="buffer" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="buffer" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="capacity" /> ist kleiner als 0.- oder - <paramref name="access" /> ist kein gültiger <see cref="T:System.IO.MemoryMappedFiles.MemoryMappedFileAccess" />-Enumerationswert.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="offset" /> plus <paramref name="capacity" /> würde das größere Ende des Adressbereichs umschließen.</exception>
    </member>
    <member name="P:System.IO.UnmanagedMemoryAccessor.IsOpen">
      <summary>Bestimmt, ob der Accessor derzeit durch einen Prozess geöffnet ist.</summary>
      <returns>true, wenn der Accessor geöffnet ist, andernfalls false. </returns>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadBoolean(System.Int64)">
      <summary>Liest einen booleschen Wert aus dem Accessor.</summary>
      <returns>true oder false.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll. </param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadByte(System.Int64)">
      <summary>Liest einen Bytewert aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadChar(System.Int64)">
      <summary>Liest ein Zeichen aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadDecimal(System.Int64)">
      <summary>Liest einen Dezimalwert aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.- oder - Die zu lesende Dezimalzahl ist ungültig.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadDouble(System.Int64)">
      <summary>Liest einen Gleitkommawert mit doppelter Genauigkeit aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadInt16(System.Int64)">
      <summary>Liest eine 16-Bit-Ganzzahl aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadInt32(System.Int64)">
      <summary>Liest eine 32-Bit-Ganzzahl aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadInt64(System.Int64)">
      <summary>Liest eine 64-Bit-Ganzzahl aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadSByte(System.Int64)">
      <summary>Liest eine 8-Bit-Ganzzahl mit Vorzeichen aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadSingle(System.Int64)">
      <summary>Liest einen Gleitkommawert mit einfacher Genauigkeit aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadUInt16(System.Int64)">
      <summary>Liest eine 16-Bit-Ganzzahl ohne Vorzeichen aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadUInt32(System.Int64)">
      <summary>Liest eine 32-Bit-Ganzzahl ohne Vorzeichen aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.ReadUInt64(System.Int64)">
      <summary>Liest eine 64-Bit-Ganzzahl ohne Vorzeichen aus dem Accessor.</summary>
      <returns>Der gelesene Wert.</returns>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Lesevorgang beginnen soll.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu lesen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Boolean)">
      <summary>Schreibt einen booleschen Wert in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Byte)">
      <summary>Schreibt einen Bytewert in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Char)">
      <summary>Schreibt ein Zeichen in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Decimal)">
      <summary>Schreibt einen Dezimalwert in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.- oder - Die Dezimalzahl ist ungültig.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Double)">
      <summary>Schreibt einen Double-Wert in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Int16)">
      <summary>Schreibt eine 16-Bit-Ganzzahl in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Int32)">
      <summary>Schreibt eine 32-Bit-Ganzzahl in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Int64)">
      <summary>Schreibt eine 64-Bit-Ganzzahl in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach Position, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.SByte)">
      <summary>Schreibt eine 8-Bit-Ganzzahl in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.Single)">
      <summary>Schreibt einen Single-Wert in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.UInt16)">
      <summary>Schreibt eine 16-Bit-Ganzzahl ohne Vorzeichen in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.UInt32)">
      <summary>Schreibt eine 32-Bit-Ganzzahl ohne Vorzeichen in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryAccessor.Write(System.Int64,System.UInt64)">
      <summary>Schreibt eine 64-Bit-Ganzzahl ohne Vorzeichen in den Accessor.</summary>
      <param name="position">Die Anzahl der Bytes im Accessor, ab der der Schreibvorgang beginnen soll.</param>
      <param name="value">Der zu schreibende Wert.</param>
      <exception cref="T:System.ArgumentException">Es gibt nicht genug Bytes nach <paramref name="position" />, um einen Wert zu schreiben.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="position" /> ist kleiner als 0 (null) oder größer als die Kapazität des Accessors.</exception>
      <exception cref="T:System.NotSupportedException">Der Accessor unterstützt keine Schreibvorgänge.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Accessor wurde freigegeben.</exception>
    </member>
    <member name="T:System.IO.UnmanagedMemoryStream">
      <summary>Ermöglicht den Zugriff auf nicht verwaltete Speicherblöcke aus verwaltetem Code heraus.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse.</summary>
      <exception cref="T:System.Security.SecurityException">Der Benutzer verfügt nicht über die erforderliche Berechtigung.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Byte*,System.Int64)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse mit der angegebenen Position und Speicherlänge.</summary>
      <param name="pointer">Ein Zeiger auf eine nicht verwaltete Speicheradresse.</param>
      <param name="length">Die Länge des zu verwendenden Speichers.</param>
      <exception cref="T:System.Security.SecurityException">Der Benutzer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="pointer" />-Wert ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="length" />-Wert ist kleiner als 0.- oder -Die <paramref name="length" /> ist groß genug, um einen Überlauf zu verursachen.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Byte*,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse mit der angegebenen Position, Speicherlänge, Gesamtspeichergröße und den Dateizugriffswerten.</summary>
      <param name="pointer">Ein Zeiger auf eine nicht verwaltete Speicheradresse.</param>
      <param name="length">Die Länge des zu verwendenden Speichers.</param>
      <param name="capacity">Die Gesamtgröße des dem Stream zugewiesenen Speichers.</param>
      <param name="access">Einer der <see cref="T:System.IO.FileAccess" />-Werte.</param>
      <exception cref="T:System.Security.SecurityException">Der Benutzer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="pointer" />-Wert ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="length" />-Wert ist kleiner als 0.- oder - Der <paramref name="capacity" />-Wert ist kleiner als 0.- oder -Der <paramref name="length" />-Wert ist größer als der <paramref name="capacity" />-Wert.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse in einem sicheren Puffer mit einem angegebenen Offset und einer angegebenen Länge. </summary>
      <param name="buffer">Der Puffer, der den nicht verwalteten Speicherstream enthalten soll.</param>
      <param name="offset">Die Byteposition im Puffer, ab der der nicht verwaltete Speicherstream beginnen soll.</param>
      <param name="length">Die Länge des nicht verwalteten Speicherstreams.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.#ctor(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse in einem sicheren Puffer mit einem angegebenen Offset, einer angegebenen Länge und angegebenem Dateizugriff. </summary>
      <param name="buffer">Der Puffer, der den nicht verwalteten Speicherstream enthalten soll.</param>
      <param name="offset">Die Byteposition im Puffer, ab der der nicht verwaltete Speicherstream beginnen soll.</param>
      <param name="length">Die Länge des nicht verwalteten Speicherstreams.</param>
      <param name="access">Der Modus des Dateizugriffs auf den nicht verwalteten Speicherstream. </param>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.CanRead">
      <summary>Ruft einen Wert ab, der angibt, ob ein Stream Lesevorgänge unterstützt.</summary>
      <returns>false, wenn das Objekt von einem Konstruktor mit einem <paramref name="access" />-Parameter erstellt wurde, das Lesen des Streams nicht eingeschlossen war und der Stream geschlossen ist, andernfalls true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.CanSeek">
      <summary>Ruft einen Wert ab, der angibt, ob ein Stream Suchvorgänge unterstützt.</summary>
      <returns>false, wenn der Stream geschlossen ist, andernfalls true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.CanWrite">
      <summary>Ruft einen Wert ab, der angibt, ob ein Stream Schreibvorgänge unterstützt.</summary>
      <returns>false, wenn das Objekt von einem Konstruktor mit einem <paramref name="access" />-Parameterwert erstellt wurde, der Schreibvorgänge unterstützt, oder wenn es von einem Konstruktor ohne Parameter erstellt wurde oder wenn der Stream geschlossen ist, andernfalls true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.Capacity">
      <summary>Ruft die Streamlänge (Größe) oder die Gesamtgröße des einem Stream zugewiesenen Speichers (Kapazität) ab.</summary>
      <returns>Die Größe oder die Kapazität des Streams.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.UnmanagedMemoryStream" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Flush">
      <summary>Überschreibt die <see cref="M:System.IO.Stream.Flush" />-Methode, sodass keine Aktion durchgeführt wird.</summary>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.FlushAsync(System.Threading.CancellationToken)">
      <summary>Überschreibt die <see cref="M:System.IO.Stream.FlushAsync(System.Threading.CancellationToken)" />-Methode. Der Vorgang wird abgebrochen, wenn dies angegeben wurde, ansonsten wird keine Aktion durchgeführt.Verfügbar ab .NET Framework 2015</summary>
      <returns>Eine Aufgabe, die die asynchrone Leerung darstellt.</returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Initialize(System.Byte*,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse unter Verwendung eines Zeigers auf eine nicht verwaltete Speicheradresse. </summary>
      <param name="pointer">Ein Zeiger auf eine nicht verwaltete Speicheradresse.</param>
      <param name="length">Die Länge des zu verwendenden Speichers.</param>
      <param name="capacity">Die Gesamtgröße des dem Stream zugewiesenen Speichers.</param>
      <param name="access">Einer der <see cref="T:System.IO.FileAccess" />-Werte. </param>
      <exception cref="T:System.Security.SecurityException">Der Benutzer verfügt nicht über die erforderliche Berechtigung.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="pointer" />-Wert ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="length" />-Wert ist kleiner als 0.- oder - Der <paramref name="capacity" />-Wert ist kleiner als 0.- oder -Der <paramref name="length" />-Wert ist groß genug, um einen Überlauf zu verursachen.</exception>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Initialize(System.Runtime.InteropServices.SafeBuffer,System.Int64,System.Int64,System.IO.FileAccess)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.UnmanagedMemoryStream" />-Klasse in einem sicheren Puffer mit einem angegebenen Offset, einer angegebenen Länge und angegebenem Dateizugriff. </summary>
      <param name="buffer">Der Puffer, der den nicht verwalteten Speicherstream enthalten soll.</param>
      <param name="offset">Die Byteposition im Puffer, ab der der nicht verwaltete Speicherstream beginnen soll.</param>
      <param name="length">Die Länge des nicht verwalteten Speicherstreams.</param>
      <param name="access">Der Modus des Dateizugriffs auf den nicht verwalteten Speicherstream.</param>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.Length">
      <summary>Ruft die Länge der Daten in einem Stream ab.</summary>
      <returns>Die Länge der Daten im Stream.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.Position">
      <summary>Ruft die aktuelle Position in einem Stream ab oder legt diese fest.</summary>
      <returns>Die aktuelle Position in dem Stream.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die Position wurde auf einen Wert festgelegt, der kleiner als 0 ist, oder die Position ist größer als <see cref="F:System.Int32.MaxValue" /> oder führt beim Hinzufügen zum aktuellen Zeiger zu einem Überlauf.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.IO.UnmanagedMemoryStream.PositionPointer">
      <summary>Ruft einen Bytezeiger auf einen Stream auf Grundlage der aktuellen Position im Stream ab oder legt diesen fest.</summary>
      <returns>Ein Bytezeiger.</returns>
      <exception cref="T:System.IndexOutOfRangeException">Die aktuelle Position ist größer als die Kapazität des Streams.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Die festgelegte Position ist keine gültige Position im aktuellen Stream.</exception>
      <exception cref="T:System.IO.IOException">Der Zeiger wird auf einen niedrigeren Wert festgelegt als die Startposition des Streams.</exception>
      <exception cref="T:System.NotSupportedException">Der Stream wurde zur Verwendung mit einem <see cref="T:System.Runtime.InteropServices.SafeBuffer" /> initialisiert.Die <see cref="P:System.IO.UnmanagedMemoryStream.PositionPointer" />-Eigenschaft ist nur für Streams gültig, die mit einem <see cref="T:System.Byte" />-Zeiger initialisiert sind.</exception>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest die angegebene Anzahl von Bytes in das angegebene Array.</summary>
      <returns>Die Gesamtanzahl der in den Puffer gelesenen Bytes.Dies kann weniger als die Anzahl der angeforderten Bytes sein, wenn diese Anzahl an Bytes derzeit nicht verfügbar ist, oder 0, wenn das Ende des Streams erreicht ist.</returns>
      <param name="buffer">Enthält nach dem Beenden dieser Methode das angegebene Bytearray mit den Werten zwischen <paramref name="offset" /> und (<paramref name="offset" /> + <paramref name="count" /> - 1), die durch die aus der aktuellen Quelle gelesenen Bytes ersetzt wurden.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem die aus dem aktuellen Stream gelesenen Daten gespeichert werden.</param>
      <param name="count">Die maximale Anzahl an Bytes, die aus dem aktuellen Stream gelesen werden sollen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <exception cref="T:System.NotSupportedException">Der zugrunde liegende Speicher unterstützt keine Lesevorgänge.- oder - Die <see cref="P:System.IO.UnmanagedMemoryStream.CanRead" />-Eigenschaft ist auf false festgelegt. </exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Parameter ist auf null festgelegt.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="offset" />-Parameter ist kleiner als 0 (null). - oder - Der <paramref name="count" />-Parameter ist kleiner als 0 (null).</exception>
      <exception cref="T:System.ArgumentException">Die Länge des Pufferarrays minus dem <paramref name="offset" />-Parameter ist kleiner als der <paramref name="count" />-Parameter.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Liest die angegebene Anzahl von Bytes asynchron in das angegebene Array.Verfügbar ab .NET Framework 2015</summary>
      <returns>Eine Aufgabe, die den asynchronen Lesevorgang darstellt.Der Wert des <paramref name="TResult" />-Parameters enthält die Gesamtzahl der Bytes, die in den Puffer gelesen werden.Der Ergebniswert kann niedriger als die Anzahl der angeforderten Bytes sein, wenn die Anzahl an derzeit verfügbaren Bytes kleiner ist als die angeforderte Anzahl, oder sie kann 0 (null) sein, wenn das Datenstromende erreicht ist.</returns>
      <param name="buffer">Der Puffer, in den die Daten geschrieben werden sollen.</param>
      <param name="offset">Der Byteoffset im <paramref name="buffer" />, ab dem Daten aus dem Stream geschrieben werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.ReadByte">
      <summary>Liest ein Byte aus einem Stream und erhöht die Position im Stream um ein Byte, oder gibt -1 zurück, wenn das Ende des Streams erreicht ist.</summary>
      <returns>Das Byte ohne Vorzeichen, umgewandelt in ein <see cref="T:System.Int32" />-Objekt, oder -1, wenn das Ende des Streams erreicht ist.</returns>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <exception cref="T:System.NotSupportedException">Der zugrunde liegende Speicher unterstützt keine Lesevorgänge.- oder -Die aktuelle Position befindet sich am Ende des Streams.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Legt die aktuelle Position des aktuellen Streams auf den angegebenen Wert fest.</summary>
      <returns>Die neue Position im Stream.</returns>
      <param name="offset">Der Punkt relativ zu <paramref name="origin" />, ab dem gesucht werden soll. </param>
      <param name="loc">Bestimmt den Anfang, das Ende oder die aktuelle Position als Bezugspunkt für <paramref name="origin" /> unter Verwendung eines Werts vom Typ <see cref="T:System.IO.SeekOrigin" />. </param>
      <exception cref="T:System.IO.IOException">Es wurde versucht, vor dem Anfang des Streams einen Suchvorgang durchzuführen.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der <paramref name="offset" />-Wert ist größer als die maximale Größe des Streams.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="loc" /> ist ungültig.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.SetLength(System.Int64)">
      <summary>Legt die Länge eines Streams auf einen angegebenen Wert fest.</summary>
      <param name="value">Die Länge des Streams.</param>
      <exception cref="T:System.IO.IOException">Ein E/A-Fehler ist aufgetreten. </exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <exception cref="T:System.NotSupportedException">Der zugrunde liegende Speicher unterstützt keine Schreibvorgänge.- oder -Es wird versucht, in den Stream zu schreiben, und die <see cref="P:System.IO.UnmanagedMemoryStream.CanWrite" />-Eigenschaft ist false.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der angegebene <paramref name="value" /> übersteigt die Kapazität des Streams.- oder -Der angegebene <paramref name="value" /> ist negativ.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt einen Byteblock mit den Daten aus einem Puffer in den aktuellen Stream.</summary>
      <param name="buffer">Das Bytearray, aus dem Bytes in den aktuellen Stream kopiert werden sollen.</param>
      <param name="offset">Der Offset im Puffer, ab dem Bytes in den aktuellen Stream kopiert werden sollen.</param>
      <param name="count">Die Anzahl der Bytes, die in den aktuellen Stream geschrieben werden sollen.</param>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <exception cref="T:System.NotSupportedException">Der zugrunde liegende Speicher unterstützt keine Schreibvorgänge. - oder -Es wird versucht, in den Stream zu schreiben, und die <see cref="P:System.IO.UnmanagedMemoryStream.CanWrite" />-Eigenschaft ist false.- oder -Der <paramref name="count" />-Wert ist größer als die Kapazität des Streams.- oder -Die Position befindet sich am Ende der Streamkapazität.</exception>
      <exception cref="T:System.IO.IOException">E/A-Fehler. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Einer der angegebenen Parameter ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="offset" />-Parameter minus der Länge des <paramref name="buffer" />-Parameters ist kleiner als der <paramref name="count" />-Parameter.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="buffer" />-Parameter ist null.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
      <summary>Schreibt beim Überschreiben in einer abgeleiteten Klasse eine Folge von Bytes asynchron in den aktuellen Stream und erhöht die aktuelle Position im Stream um die Anzahl der geschriebenen Bytes und überwacht Abbruchanforderungen.Verfügbar ab .NET Framework 2015</summary>
      <returns>Eine Aufgabe, die den asynchronen Schreibvorgang darstellt.</returns>
      <param name="buffer">Der Puffer, aus dem Daten geschrieben werden sollen.</param>
      <param name="offset">Der nullbasierte Byteoffset im <paramref name="buffer" />, ab dem Bytes in den Stream kopiert werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .Der Standardwert ist <see cref="P:System.Threading.CancellationToken.None" />.</param>
    </member>
    <member name="M:System.IO.UnmanagedMemoryStream.WriteByte(System.Byte)">
      <summary>Schreibt ein Byte an die aktuelle Position im Dateistream.</summary>
      <param name="value">Ein Bytewert, der in den Stream geschrieben werden soll.</param>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <exception cref="T:System.NotSupportedException">Der zugrunde liegende Speicher unterstützt keine Schreibvorgänge.- oder -Es wird versucht, in den Stream zu schreiben, und die <see cref="P:System.IO.UnmanagedMemoryStream.CanWrite" />-Eigenschaft ist false.- oder - Die aktuelle Position befindet sich am Ende der Kapazität des Streams.</exception>
      <exception cref="T:System.IO.IOException">Der angegebene <paramref name="value" /> verursacht das Überschreiten der maximalen Kapazität des Streams.</exception>
      <filterpriority>2</filterpriority>
    </member>
  </members>
</doc>