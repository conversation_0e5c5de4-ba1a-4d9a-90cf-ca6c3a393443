﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.Model
{
    public class Sperm
    {
        public int id { get; set; }

        public int photoId { get; set; }

        public string sampleId { get; set; }

        public float xmin { get; set; }

        public float xmax { get; set; }

        public float ymin { get; set; }

        public float ymax { get; set; }

        public int classid { get; set; }

        public string clsname { get; set; }

        public float score { get; set; }

        public string imageSource { get; set; }

        public Point[] vop_acrosome { get; set; }

        public Point[] vop_kernel { get; set; }

        public Point[] vop_sperm { get; set; }

        public List<Point[]> vvop_vacuoles { get; set; }

        public double acrosomeArea { get; set; }

        public double kernelArea { get; set; }

        public double spermArea { get; set; }


        public double spermGirth { get; set; }

        public double longAxis { get; set; }

        public double shortAxis { get; set; }

        /// <summary>
        /// 增加合成字段 
        /// </summary>
        public PointF center { get; set; }

        public double center_x { get; set; }

        public double center_y { get; set; }


        public double angle { get; set; }

        public double ellipsRatio { get; set; }

        public double acrosomeRatio { get; set; }

        public double areaCV { get; set; }

        public double girthCV { get; set; }

        public double ellipseCV { get; set; }

        public double SymmetryRatio { get; set; }

        public double SymmetryRatioLongAxis { get; set; }

        public bool valid { get; set; }

        public int ellipseRatioValid { get; set; }

        public int spermAreaValid { get; set; }

        public int ellipseCVValid { get; set; }

        public int SymmetryRatioValid { get; set; }

        public int SymmetryRatioLongAxisValid { get; set; }

        public int girthCVValid { get; set; }

        public int areaCVValid { get; set; }

        public int acrosomeRatioValid { get; set; }

        public int longAxisValid { get; set; }

        public int shortAxisValid { get; set; }


        public int spermGirthValid { get; set; }

        public int kernelAreaValid { get; set; }

        public int acrosomeAreaValid { get; set; }

        public int isNormal { get; set; }

        public bool tailValid { get; set; }

        public bool vacuolesValid { get; set; }

        public int vacuolesNum { get; set; }

        public bool vacuolesNumValid { get; set; }

        public int bigVaucolesNum { get; set; }

        public bool bigVacuolesNumValid { get; set; }

        public int acrosomeVacuolesNum { get; set; }

        public int kernelVacuolesNum { get; set; }

        public bool kernelVacuolesNumValid { get; set; }

        public double acrosomeVacuolesArea { get; set; }

        public double kernelVacuolesArea { get; set; }

        public bool middlePieceValid { get; set; }

        public double middlepieceAngle { get; set; }

        public bool middlepieceAngleValid { get; set; }

        public double middlepieceWidth { get; set; }

        public bool middlepieceWidthValid { get; set; }

        public Point[] vop_middlepiece { get; set; }

        public int fieldId { get; set; }

        public int spermindex { get; set; }

        public int shapeType { get; set; }

        public int acrosomeType { get; set; }

        public int kernelType { get; set; }

        public double acrosome_uniformity { get; set; }

        public double kernel_uniformity { get; set; }

        public int middlePieceType { get; set; }

        public double outer_x { get; set; }

        public double outer_y { get; set; }

        public double outer_width { get; set; }

        public double outer_height { get; set; }

        public double outer_angle { get; set; }

        public double inner_x { get; set; }

        public double inner_y { get; set; }

        public double inner_width { get; set; }

        public double inner_height { get; set; }

        public double inner_angle { get; set; }

        public double DFI { get; set; }

        public double ellipseCV1 { get; set; }

        public double ellipseCV1Range { get; set; }

        public double tailLength { get; set; }

        public double tailAngle { get; set; }

        public double ERCArea { get; set; }

        public double ERCWidth { get; set; }

        public double ERCHeight { get; set; }

        public double R { get; set; }

        public double G { get; set; }

        public double B { get; set; }

        public double RBRatio { get; set; }

        public int MDPnum { get; set; }

        public int isWrite { get; set; }

        /// <summary>
        /// 拼接成精子的唯一字符串
        /// </summary>
        public string spermId { get; set; }


        public bool kernel_uniformityValid { get; set; }
    }
}
