using System;
using System.Collections.Generic;
using System.IO;
using SpermAnalysisWPF.Model;

namespace SpermAnalysisWPF.DataHelper
{
    public class SpermDataService
    {
        // 简化：使用内存存储，不使用数据库
        private List<Mrfrules> defaultRules;

        public SpermDataService()
        {
            // 初始化默认规则
            defaultRules = new List<Mrfrules>
            {
                new Mrfrules { id = 1, Usefield = "spermArea", Min_value = 20.0, Max_value = 80.0, Description = "精子面积" },
                new Mrfrules { id = 2, Usefield = "longAxis", Min_value = 4.0, Max_value = 5.5, Description = "长轴长度" },
                new Mrfrules { id = 3, Usefield = "shortAxis", Min_value = 2.5, Max_value = 3.5, Description = "短轴长度" },
                new Mrfrules { id = 4, Usefield = "ellipsRatio", Min_value = 1.3, Max_value = 1.8, Description = "椭圆比" },
                new Mrfrules { id = 5, Usefield = "acrosomeRatio", Min_value = 0.4, Max_value = 0.7, Description = "顶体比例" }
            };
        }

        public void SaveSperm(Sperm sperm)
        {
            // 简化：不保存到数据库，只在内存中处理
        }

        public void SaveSampleInfo(Sampleinfo sampleInfo)
        {
            // 简化：不保存到数据库，只在内存中处理
        }

        public List<Mrfrules> GetMrfRules()
        {
            return defaultRules;
        }
    }
}
