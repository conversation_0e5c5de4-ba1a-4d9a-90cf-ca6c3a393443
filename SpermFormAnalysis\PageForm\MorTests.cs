﻿using Emgu.CV.CvEnum;
using Emgu.CV;
using GxIAPINET;
using MySql.Data.MySqlClient;
using SpermFormAnalysis.Class;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.Model;
using SpermFormAnalysis.Segments;
using SpermFormAnalysis.utils;
using Sunny.UI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Media.Imaging;
using Emgu.CV.Structure;
using NPOI.HSSF.UserModel;
using NPOI.POIFS.FileSystem;
using NPOI.SS.UserModel;
using System.Windows.Forms.DataVisualization.Charting;
using QtrfControl;

namespace SpermFormAnalysis.PageForm
{
    public partial class MorTests : UIPage
    {
        bool m_bIsOpen = false;                           ///<设备打开状态

        bool m_bWhiteAuto = false;                           ///<标识是否支持白平衡
        //double dCurShuter = 0.0;                 //当前曝光值
        IImageProcessConfig m_objCfg = null;                           ///<图像配置参数对象

        IGXFactory m_objIGXFactory = null;                            ///<Factory对像

        IGXStream m_objIGXStream = null;                            ///<流对像
        IGXFeatureControl m_objIGXFeatureControl = null;                            ///<远端设备属性控制器对像
        IGXFeatureControl m_objIGXStreamFeatureControl = null;                            ///<流层属性控制器对象
        string m_strBalanceWhiteAutoValue = "Off";                           ///<自动白平衡当前的值
        GxBitmap m_objGxBitmap = null;                            ///<图像显示类对象
        List<InterfCard> interfCards = new List<InterfCard>();


        //抓拍标识
        private volatile bool flag = false;

        private List<PictureBox> pictures = new List<PictureBox>();//图片控件集
        //List<int[]> contourInfos = new List<int[]>();
        List<List<int>> contourInfos = new List<List<int>>();//细胞结果集
        //public static List<Dictionary<string, int>>  dics = new List<Dictionary<string, int>>();//结果
                                                             // Dictionary<string, int> cellNums = new Dictionary<string, int>();

        public static bool isCal = false;
        private int icdev = 0;//设备号
        //private DataLogic_Access dal = new DataLogic_Access();//数据库操作类
        //private DBUtils dBUtils = new DBUtils();//数据库操作类
        //public static DFI_Test m_DTS = null;//DFI_Test实体
        public Sampleinfo sampleinfo = null;//样本实例

        //XMLEditor edit = new XMLEditor();
        List<String> reviewers = new List<string>();
        List<String> doctors = new List<string>();


        Double DFIValue = 0;//DFI值
        //private CopyProgress frmCP = null;//处理进度提示窗
        public static List<Samplephoto> pictureInfos = new List<Samplephoto>();
        List<PictureBox> pbx = new List<PictureBox>();


        public uint m_handle = 0;
        public bool m_bAeOp = false;
        public int m_n_dev_count = 0;

        public static IntPtr m_ptr_wnd = new IntPtr();
        public static IntPtr m_ptr = new IntPtr();
        public static bool m_b_start = false;
        //List<string> spics = new List<string>();


        private PictureBox CurrPic;

        public ObservableCollection<Model.Samplephoto> samplephotos = new ObservableCollection<Model.Samplephoto>();

        public BitmapImage currentSelectedImg { get; set; }

        public Model.Samplephoto currentSamplephoto { get; set; }

        private int photoIndex = 0;
        public MorTests()
        {
            InitializeComponent();
        }
        private void btn_Add_FCS_Click(object sender, MouseEventArgs e)
        {
            if (m_bIsOpen)
            {
                __CloseDevice();
                pic_Show.Invalidate();
                this.btn_Add_FCS.Text = "连接";
                this.btn_Add_FCS.Symbol = 61515;
                this.btn_Add_FCS.FillColor = Color.FromArgb(80, 160, 255);
                this.btn_Add_FCS.FillHoverColor = Color.CornflowerBlue;
                this.btn_Add_FCS.FillPressColor = Color.CornflowerBlue;
                m_bIsOpen = false;

                return;
            }

            try
            {
                List<IGXDeviceInfo> listGXDeviceInfo = new List<IGXDeviceInfo>();

                //关闭流
                __CloseStream();
                // 如果设备已经打开则关闭，保证相机在初始化出错情况下能再次打开
                __CloseDevice();
                m_objIGXFactory.UpdateDeviceList(200, listGXDeviceInfo);
                // 判断当前连接设备个数
                if (listGXDeviceInfo.Count <= 0)
                {
                    ShowErrorNotifier("未发现设备!", true, 2000);
                    return;
                }

                // 如果设备已经打开则关闭，保证相机在初始化出错情况下能再次打开
                if (null != GlobalProperty.m_objIGXDevice)
                {
                    GlobalProperty.m_objIGXDevice.Close();
                    GlobalProperty.m_objIGXDevice = null;
                }

                //打开列表第一个设备
                GlobalProperty.m_objIGXDevice = m_objIGXFactory.OpenDeviceBySN(listGXDeviceInfo[0].GetSN(), GX_ACCESS_MODE.GX_ACCESS_EXCLUSIVE);
                m_objIGXFeatureControl = GlobalProperty.m_objIGXDevice.GetRemoteFeatureControl();
                //打开流
                if (null != GlobalProperty.m_objIGXDevice)
                {
                    m_objIGXStream = GlobalProperty.m_objIGXDevice.OpenStream(0);
                    m_objIGXStreamFeatureControl = m_objIGXStream.GetFeatureControl();
                }

                // 建议用户在打开网络相机之后，根据当前网络环境设置相机的流通道包长值，
                // 以提高网络相机的采集性能,设置方法参考以下代码。
                GX_DEVICE_CLASS_LIST objDeviceClass = GlobalProperty.m_objIGXDevice.GetDeviceInfo().GetDeviceClass();
                /*  if (GX_DEVICE_CLASS_LIST.GX_DEVICE_CLASS_GEV == objDeviceClass)
                  {
                      // 判断设备是否支持流通道数据包功能
                      if (true == m_objIGXFeatureControl.IsImplemented("GevSCPSPacketSize"))
                      {
                          // 获取当前网络环境的最优包长值
                          uint nPacketSize = m_objIGXStream.GetOptimalPacketSize();
                          // 将最优包长值设置为当前设备的流通道包长值
                          m_objIGXFeatureControl.GetIntFeature("GevSCPSPacketSize").SetValue(nPacketSize);
                      }
                  }*/
                m_objCfg = GlobalProperty.m_objIGXDevice.CreateImageProcessConfig();
                //初始化相机参数
                __InitDevice();

                // 获取相机参数,初始化界面控件
                __InitUI();

                m_objGxBitmap = new GxBitmap(GlobalProperty.m_objIGXDevice, this.pic_Show);

                // 更新设备打开标识
                m_bIsOpen = true;
                //update();
                try
                {
                    if (null != m_objIGXStreamFeatureControl)
                    {
                        //设置流层Buffer处理模式为OldestFirst
                        m_objIGXStreamFeatureControl.GetEnumFeature("StreamBufferHandlingMode").SetValue("OldestFirst");
                    }

                    //开启采集流通道
                    if (null != m_objIGXStream)
                    {
                        //RegisterCaptureCallback第一个参数属于用户自定参数(类型必须为引用
                        //类型)，若用户想用这个参数可以在委托函数中进行使用
                        m_objIGXStream.RegisterCaptureCallback(this, __CaptureCallbackPro);
                        m_objIGXStream.StartGrab();
                    }
                    if (null != m_objIGXFeatureControl)
                    {
                        m_objIGXFeatureControl.GetCommandFeature("AcquisitionStart").Execute();
                    }
                }
                catch (Exception ex)
                {
                    //MessageBox.Show(ex.Message);
                    ShowErrorNotifier(ex.Message, true, 2000);
                }
                //刷新界面
                this.btn_Add_FCS.Text = "断开";
                this.btn_Add_FCS.Symbol = 61516;
                this.btn_Add_FCS.FillColor = Color.Red;
                this.btn_Add_FCS.FillHoverColor = Color.FromArgb(192, 0, 0);
                this.btn_Add_FCS.FillPressColor = Color.FromArgb(192, 0, 0);
            }
            catch (Exception ex)
            {
                //MessageBox.Show(ex.Message);
                ShowErrorNotifier(ex.Message, true, 2000);
            }

            this.Focus();
        }
        /// <summary>
        /// 关闭流
        /// </summary>
        private void __CloseStream()
        {
            try
            {
                //关闭流
                if (null != m_objIGXStream)
                {
                    m_objIGXStream.Close();
                    m_objIGXStream = null;
                    m_objIGXStreamFeatureControl = null;
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 关闭设备
        /// </summary>
        private void __CloseDevice()
        {
            try
            {
                //关闭设备
                if (null != GlobalProperty.m_objIGXDevice)
                {
                    GlobalProperty.m_objIGXDevice.Close();
                    GlobalProperty.m_objIGXDevice = null;
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 相机初始化
        /// </summary>
        void __InitDevice()
        {
            MySqlDataReader sdr = null;
            if (null != m_objIGXFeatureControl)
            {
                //加载数据库设置参数
                m_objIGXFeatureControl.GetEnumFeature("AcquisitionMode").SetValue("Continuous");

                //m_objIGXFeatureControl.GetEnumFeature("BalanceRatioSelector").SetValue("Red");
                //m_objIGXFeatureControl.GetFloatFeature("BalanceRatio").SetValue(2);


                m_objCfg.EnableColorCorrection(true);
                m_objCfg.SetSaturationParam(50);
                m_objCfg.SetLightnessParam(100);


                try
                {
                    m_objIGXFeatureControl.GetEnumFeature("AcquisitionMode").SetValue("Continuous");
                    __SetEnumValue("BalanceWhiteAuto", GlobalProperty.BalanceWhite, m_objIGXFeatureControl);
                    __SetEnumValue("ExposureAuto", GlobalProperty.ExposureAuto, m_objIGXFeatureControl);
                    __SetEnumValue("GainAuto", GlobalProperty.GainAuto, m_objIGXFeatureControl);
                    __SetEnumValue("AcquisitionFrameRate", GlobalProperty.AcquisitionFrameRate, m_objIGXFeatureControl);
                    if (GlobalProperty.ExposureAuto.Equals("Off"))
                        __SetEnumValue("ExposureTime", GlobalProperty.ExposureTime, m_objIGXFeatureControl);
                    if (GlobalProperty.GainAuto.Equals("Off"))
                        __SetEnumValue("Gain", GlobalProperty.Gain, m_objIGXFeatureControl);
                    //__InitEnumComBoxUI(bphBox, "BalanceWhiteAuto", m_objIGXFeatureControl, ref m_bWhiteAuto);
                    //__InitEnumComBoxUI(bgBox, "ExposureAuto", m_objIGXFeatureControl, ref m_bWhiteAuto);
                    //__InitEnumComBoxUI(zdBox, "GainAuto", m_objIGXFeatureControl, ref m_bWhiteAuto);

                    //m_objIGXFeatureControl.GetEnumFeature("BalanceRatioSelector").SetValue("Red");
                    //m_objIGXFeatureControl.GetFloatFeature("BalanceRatio").SetValue(2);
                    //__IsEnableDeviceColorCorrect();

                    //颜色校正只能是彩色相机支持 
                    /* if (m_bWhiteAuto)
                     {
                         m_objCfg.EnableColorCorrection(true);
                         //m_objCfg.SetSaturationParam(128);
                         m_objCfg.SetLightnessParam(120);
                     }*/

                    m_objCfg.EnableColorCorrection(true);
                    //m_objCfg.SetSaturationParam(128);
                    //m_objCfg.SetLightnessParam(120);

                    //bphBox.Enabled = true;
                    //bgBox.Enabled = true;
                    //zdBox.Enabled = true;
                    //zyBox.Enabled = true;
                }
                catch (Exception ex)
                {
                    //MessageBox.Show(ex.Message);
                    ShowErrorTip(ex.Message, 2000);
                }
                finally
                {
                }
                //}

            }
        }
        /// <summary>
        /// 对枚举型变量按照功能名称设置值
        /// </summary>
        /// <param name="strFeatureName">枚举功能名称</param>
        /// <param name="strValue">功能的值</param>
        /// <param name="objIGXFeatureControl">属性控制器对像</param>
        private void __SetEnumValue(string strFeatureName, string strValue, IGXFeatureControl objIGXFeatureControl)
        {
            if (null != objIGXFeatureControl)
            {
                if (strFeatureName.Equals("Gain"))
                {
                    objIGXFeatureControl.GetFloatFeature(strFeatureName).SetValue(Convert.ToDouble(strValue));
                }
                else if (strFeatureName.Equals("AcquisitionFrameRate"))
                {
                    objIGXFeatureControl.GetFloatFeature(strFeatureName).SetValue(Convert.ToDouble(strValue));
                }
                else if (strFeatureName.Equals("ExposureTime"))
                {
                    objIGXFeatureControl.GetFloatFeature(strFeatureName).SetValue(Convert.ToDouble(strValue));
                }
                else
                {
                    objIGXFeatureControl.GetEnumFeature(strFeatureName).SetValue(strValue);

                }
                //设置当前功能值

            }
        }

        /// <summary>
        /// 设备打开后初始化界面
        /// </summary>
        private void __InitUI()
        {
            bool bIsImplemented = false;             //是否支持
            bool bIsReadable = false;                //是否可读
            // 获取是否支持
            if (null != m_objIGXFeatureControl)
            {
                bIsImplemented = m_objIGXFeatureControl.IsImplemented("BalanceWhiteAuto");
                bIsReadable = m_objIGXFeatureControl.IsReadable("BalanceWhiteAuto");
                if (bIsImplemented)
                {
                    if (bIsReadable)
                    {
                        //获取当前功能值
                        m_strBalanceWhiteAutoValue = m_objIGXFeatureControl.GetEnumFeature("BalanceWhiteAuto").GetValue();
                    }
                }
            }
        }

        /// <summary>
        /// 回调函数,用于获取图像信息和显示图像
        /// </summary>
        /// <param name="obj">用户自定义传入参数</param>
        /// <param name="objIFrameData">图像信息对象</param>
        private void __CaptureCallbackPro(object objUserParam, IFrameData objIFrameData)
        {
            try
            {
                MorTests objGxSingleCam = objUserParam as MorTests;
                string stfFileName = "";
                objGxSingleCam.ImageShowAndSave(objIFrameData, ref stfFileName);

            }
            catch (Exception ex)
            {

                ShowErrorNotifier(ex.Message);
            }
        }

        /// <summary>
        /// 图像的显示和存储
        /// </summary>
        /// <param name="objIFrameData">图像信息对象</param>
        void ImageShowAndSave(IFrameData objIFrameData, ref string stfFileName)
        {
            //GX_VALID_BIT_LIST emValidBits = GX_VALID_BIT_LIST.GX_BIT_0_7;
            try
            {
                if (null != m_objCfg)
                {
                    //emValidBits = __GetBestValudBit(objIFrameData.GetPixelFormat());
                    m_objCfg.SetValidBit(__GetBestValudBit(objIFrameData.GetPixelFormat()));
                    //首先配置图像处理参数以免造成图像处理耗时刷先界面卡死
                    objIFrameData.ImageProcess(m_objCfg);
                }
                m_objGxBitmap.ShowImageProcess(m_objCfg, objIFrameData);
            }
            catch (Exception)
            {
            }
            //是否需要进行图像保存
            if (flag)
            {
                flag = false;
                DateTime dtNow = DateTime.Now;  // 获取系统当前时间
                ///<应用程序当前路径

                string m_strFilePath = Directory.GetCurrentDirectory().ToString() +
                    "\\images" + "\\" + dtNow.ToString("yyyyMMdd") + "\\" + sampleinfo.sampleId;
                if (pictureInfos.Count == 0 && !Directory.Exists(m_strFilePath))
                {
                    Directory.CreateDirectory(m_strFilePath);
                }
                Samplephoto info = new Samplephoto()
                {
                    sampleId = sampleinfo.sampleId,
                    imageSource = "images\\" + dtNow.ToString("yyyyMMdd") + "\\" + sampleinfo.sampleId + "\\" + dtNow.ToString("yyyy_MM_dd_HH_mm_ss_fff") + ".jpg",
                    type = "MRF",
                    format = "jpg",
                };
                //SamplephotoServices.AddPhoto(info);
                //PictureInfo info = new PictureInfo
                //{
                //    Dt = m_DTS,
                //    PicPath = "images\\" + dtNow.ToString("yyyyMMdd") + "\\"  +sampleinfo.sampleId
                //    + "\\" + dtNow.ToString("yyyy_MM_dd_HH_mm_ss_fff") + ".jpg"
                //};

                //stfFileName = ;  // 默认的图像保存名称
                m_objGxBitmap.SaveBmp(m_objCfg, objIFrameData, m_strFilePath + "\\" + dtNow.ToString("yyyy_MM_dd_HH_mm_ss_fff") + ".jpg");
                pictureInfos.Add(info);
                //pics.Add(stfFileName);
            }
        }

        /// <summary>
        /// 通过GX_PIXEL_FORMAT_ENTRY获取最优Bit位
        /// </summary>
        /// <param name="em">图像数据格式</param>
        /// <returns>最优Bit位</returns>
        private GX_VALID_BIT_LIST __GetBestValudBit(GX_PIXEL_FORMAT_ENTRY emPixelFormatEntry)
        {
            GX_VALID_BIT_LIST emValidBits = GX_VALID_BIT_LIST.GX_BIT_0_7;
            switch (emPixelFormatEntry)
            {
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO8:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GR8:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_RG8:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GB8:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_BG8:
                    {
                        emValidBits = GX_VALID_BIT_LIST.GX_BIT_0_7;
                        break;
                    }
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO10:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GR10:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_RG10:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GB10:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_BG10:
                    {
                        emValidBits = GX_VALID_BIT_LIST.GX_BIT_2_9;
                        break;
                    }
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO12:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GR12:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_RG12:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GB12:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_BG12:
                    {
                        emValidBits = GX_VALID_BIT_LIST.GX_BIT_4_11;
                        break;
                    }
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO14:
                    {
                        //暂时没有这样的数据格式待升级
                        break;
                    }
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_MONO16:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GR16:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_RG16:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_GB16:
                case GX_PIXEL_FORMAT_ENTRY.GX_PIXEL_FORMAT_BAYER_BG16:
                    {
                        //暂时没有这样的数据格式待升级
                        break;
                    }
                default:
                    break;
            }
            return emValidBits;
        }

        private UIWaitForm waitForm;

        private void ShowWaitFormMethod(string info)
        {
            waitForm = new UIWaitForm(info);
            waitForm.ShowDialog(this);
        }

        private void HideWaitFormMethod()
        {
            if (waitForm != null)
            {
                waitForm.Close();
                waitForm = null;
            }
        }
        private void MorTests_Load(object sender, EventArgs e)
        {
            try
            {
                m_objIGXFactory = IGXFactory.GetInstance();
                m_objIGXFactory.Init();

                Thread newThread = new Thread(new ThreadStart(Preload));
                newThread.Start();
                if (newThread.IsAlive)
                {
                    ShowWaitForm("分析预加载中，请稍后。。。");
                }

                Thread.Sleep(2000);

                HideWaitForm();

                TaskMgr.RaiseProcessingPhotoChangedEvent += this.processingPhotoEvent;
                TaskMgr.RaiseProcessedPhotoChangedEvent += this.processedPhotoEvent;

            }
            catch (Exception ex)
            {
                ShowErrorTip(ex.Message);
            }
        }
        public void Preload()
        {
            SampleinfoServices.CreateTestObject();
            //提前跑一个实例，提前启动预加载
            Sampleinfo info = SampleinfoServices.GetLastObjectByPro0();

            if (info != null)
            {
                string[] fileNames = new string[1];
                fileNames[0] = Directory.GetCurrentDirectory().ToString() +
                        "\\images" + "\\sample\\" + "1.jpg";
                this.AddPhotos(fileNames, info);
            }
        }
        public double analyzepro;
        public string ProcessingInfo;
        public string _selectSampleIds;
        public string selectSampleIds
        {
            get
            {
                return this._selectSampleIds;
            }
            set
            {
                int num2 = this.samplephotos.Sum((Samplephoto t) => t.processed);
                bool flag3 = this.samplephotos.Count > 0;
                if (flag3)
                {
                    this.analyzepro = (double)(100 * num2 / this.samplephotos.Count);
                }
                else
                {
                    this.analyzepro = 0.0;
                }
            }
        }
        private void processingPhotoEvent(Samplephoto sp)
        {
            Func<Samplephoto, bool> fsamps;
            this.BeginInvoke(new Action(delegate ()
            {
                int num = this.samplephotos.Sum((Samplephoto t) => t.processed);
                bool flag3 = this.samplephotos.Count > 0;
                if (flag3)
                {
                    if (num == 0)
                    {
                        this.analyzepro = 50;
                    }
                    this.analyzepro = (double)(100 * num / this.samplephotos.Count);
                }
                else
                {
                    this.analyzepro = 30.0;
                }
                this.ProcessingInfo = string.Concat(new string[]
                {
                    //"分析样本:",
                    //this.selectSampleIds,
                    "   分析进度:",
                    (this.analyzepro + "%").PadRight(7, ' '),
                    " 正在分析:",
                    sp.imageSource.Substring(sp.imageSource.LastIndexOf("\\") + 1)
                });
                //Random r = new Random(DateTime.Now.Millisecond);
                //uiProcessBar1.Value = 0;
                //int step = r.Next(10, 80);
                //uiProcessBar1.Value += step;
                //uiProcessBar1.Text = (uiProcessBar1.Value + "%");


                IEnumerable<Samplephoto> samplephotos = this.samplephotos;
                Func<Samplephoto, bool> predicate;
                predicate = (fsamps = ((Samplephoto p) => p.id == sp.id));

                Samplephoto samplephoto = samplephotos.FirstOrDefault(predicate);
                bool flag4 = samplephoto != null;
                if (flag4)
                {
                    samplephoto.processed = 2;
                }
                int num2 = -1;
                for (int i = 0; i < this.samplephotos.Count; i++)
                {
                    Samplephoto samplephoto2 = (Samplephoto)this.samplephotos[i];
                    bool flag5 = samplephoto2.id == sp.id;
                    if (flag5)
                    {
                        num2 = i;
                        break;
                    }
                }
            }), Array.Empty<object>());
        }
        int picindex = 0;
        private void processedPhotoEvent(Samplephoto sp)
        {
            this.BeginInvoke(new Action(delegate ()
            {
                if (waitForm != null)
                {
                    HideWaitFormMethod();
                }
                else
                {
                    this.ProcessingInfo = string.Concat(new string[]
{
                    "分析样本:",
                    this.selectSampleIds,
                    "   分析进度:",
                    (this.analyzepro + "%").PadRight(7, ' '),
                    "分析完成：",
                    sp.imageSource.Substring(sp.imageSource.LastIndexOf("\\") + 1)
                    }); 
                    if (sampleinfo.processed == 2)
                    {
                        return;
                    }
                    //显示分析的结果
                    RefResultUI();//刷新分析结果
                }
            }), Array.Empty<object>());
        }

        private void RefResultUI()
        {
            sampleinfo = SampleinfoServices.GetObject(sampleinfo.Id);
            picindex++;
            if (picindex > samplephotos.Count || sampleinfo.processed == 2)
            {
                return;
            }
            lblAnalysisInfo.Text = "分析进度（" + picindex + "/" + samplephotos.Count + "）张";
            uiProcessBar1.Value = (int)(picindex * 100 / samplephotos.Count);

            uiProcessBar1.Text = (uiProcessBar1.Value + "%");
            lbl_CellNum.Text = sampleinfo.sperm_num.ToString();

            //rightCellLab.Text = sampleinfo.normal_num.ToString();
            //errorCellLab.Text = sampleinfo.abnormal_num.ToString();
            //lbl_qualification_rate.Text = (sampleinfo.qualification_rate * 100).ToString();
            if (sampleinfo.sperm_num >= 200)
            {
                stulab.Text = "采集完成";
            }
        }


        private void AddPhotos(string[] imglist)
        {
            bool flag = this.sampleinfo != null;
            if (flag)
            {
                Task task = new Task(delegate ()
                {
                    this.do_addPhotos(imglist);//, this.sampleinfo
                });
                task.Start();
                Task.WaitAll(new Task[]
                {
                    task
                });
            }
        }
        /// <summary>
        /// 不用全局，用来提前运行
        /// </summary>
        /// <param name="imglist"></param>
        /// <param name="sampleinfo"></param>
        private void AddPhotos(string[] imglist, Sampleinfo info)
        {
            bool flag = info != null;
            if (flag)
            {
                Task task = new Task(delegate ()
                {
                    this.do_addPhotos(imglist, info);//, this.sampleinfo
                });
                task.Start();
                Task.WaitAll(new Task[]
                {
                    task
                });
            }
        }
        private void do_addPhotos(string[] imglst)//, Model.Sampleinfo sampleInfo
        {
            string text = "\\photos";
            Basic.createDir(text);
            string text2 = text + "\\" + this.sampleinfo.sampleId;
            Basic.createDir(text2);
            string dir = text + "\\" + this.sampleinfo.sampleId + "\\thumbnail";
            Basic.createDir(dir);
            Model.Samplephoto samplephoto = new Model.Samplephoto();
            foreach (string text3 in imglst)
            {
                //SamplephotoServices.gets
                int num = SamplephotoServices.GetSamplePhoto(this.sampleinfo.sampleId).Count;
                bool flag = num > 0;
                if (flag)
                {
                    num = SamplephotoServices.GetMaxBlockNum(this.sampleinfo.sampleId) + 1;
                }
                string text4 = text3.Substring(text3.LastIndexOf("\\") + 1);
                string text5 = text3.Substring(text3.LastIndexOf(".") + 1);
                string text6 = string.Concat(new string[]
                {
                    Directory.GetCurrentDirectory(),
                    text2,
                    "\\",
                    this.sampleinfo.sampleId,
                    "_Field_",
                    num.ToString("00"),
                    ".",
                    text5
                });
                string fn_out = string.Concat(new string[]
                {
                    Directory.GetCurrentDirectory(),
                    text2,
                    "\\thumbnail\\",
                    this.sampleinfo.sampleId,
                    "_Field_",
                    num.ToString("00"),
                    ".jpg"
                });
                File.Copy(text3, text6, true);
                this.saveThumbnail(text6, fn_out);
                samplephoto.sampleId = sampleinfo.sampleId;
                bool flag2 = sampleinfo != null;
                if (flag2)
                {
                    samplephoto.type = sampleinfo.inspectItem;
                }
                samplephoto.number = 0;
                samplephoto.block = num;
                samplephoto.format = text5;
                samplephoto.imageSource = text6;

                samplephotos.Add(samplephoto);
                SamplephotoServices.AddPhoto(samplephoto);

                Thread.Sleep(10);
            }

            sampleinfo.field_num = SamplephotoServices.GetSamplePhoto(this.sampleinfo.sampleId).Count;

            SampleinfoServices.UpdateObject(sampleinfo);

            this.newPhotoEvent("");
        }

        /// <summary>
        /// 带精子参数的 用来提前运行
        /// </summary>
        /// <param name="imglst"></param>
        /// <param name="info"></param>
        private void do_addPhotos(string[] imglst, Sampleinfo info)//, Model.Sampleinfo sampleInfo
        {
            string text = "\\photos";
            Basic.createDir(text);
            string text2 = text + "\\" + info.sampleId;
            Basic.createDir(text2);
            string dir = text + "\\" + info.sampleId + "\\thumbnail";
            Basic.createDir(dir);
            Model.Samplephoto samplephoto = new Model.Samplephoto();
            foreach (string text3 in imglst)
            {
                //SamplephotoServices.gets
                int num = SamplephotoServices.GetSamplePhoto(info.sampleId).Count;
                bool flag = num > 0;
                if (flag)
                {
                    num = SamplephotoServices.GetMaxBlockNum(info.sampleId) + 1;
                }
                string text4 = text3.Substring(text3.LastIndexOf("\\") + 1);
                string text5 = text3.Substring(text3.LastIndexOf(".") + 1);
                string text6 = string.Concat(new string[]
                {
                    Directory.GetCurrentDirectory(),
                    text2,
                    "\\",
                    info.sampleId,
                    "_Field_",
                    num.ToString("00"),
                    ".",
                    text5
                });
                string fn_out = string.Concat(new string[]
                {
                    Directory.GetCurrentDirectory(),
                    text2,
                    "\\thumbnail\\",
                    info.sampleId,
                    "_Field_",
                    num.ToString("00"),
                    ".jpg"
                });
                File.Copy(text3, text6, true);
                this.saveThumbnail(text6, fn_out);
                samplephoto.sampleId = info.sampleId;
                bool flag2 = sampleinfo != null;
                if (flag2)
                {
                    samplephoto.type = info.inspectItem;
                }
                samplephoto.number = 0;
                samplephoto.block = num;
                samplephoto.format = text5;
                samplephoto.imageSource = text6;

                samplephotos.Add(samplephoto);
                SamplephotoServices.AddPhoto(samplephoto);

                Thread.Sleep(10);
            }

            info.field_num = SamplephotoServices.GetSamplePhoto(info.sampleId).Count;

            SampleinfoServices.UpdateObject(info);

            this.newPhotoEvent(info);
        }
        private void saveThumbnail(string fn, string fn_out)
        {
            Image<Rgb, byte> image = new Image<Rgb, byte>(fn);
            image = image.Resize(0.1, Inter.Linear);
            image.Save(fn_out);
            image.Dispose();
        }
        private void newPhotoEvent(string f = "")
        {
            this.BeginInvoke(new Action(delegate ()
            {
                this.refreshPhotos();
                bool flag = this.sampleinfo.processed != 1;
                if (flag)
                {
                    SampleinfoServices.UpdateSampleStatus(sampleinfo, 1);
                }
                TaskMgr.runSegmentTask(this.sampleinfo.sampleId);
            }), Array.Empty<object>());
        }
        private void newPhotoEvent(Sampleinfo info)
        {
            this.BeginInvoke(new Action(delegate ()
            {
                //this.refreshPhotos();
                bool flag = info.processed != 1;
                if (flag)
                {
                    SampleinfoServices.UpdateSampleStatus(info, 1);
                }
                TaskMgr.runSegmentTask(info.sampleId, false);
            }), Array.Empty<object>());
        }
        private void refreshPhotos()
        {
            this.samplephotos = ((sampleinfo.sampleId != "") ? SamplephotoServices.GetSamplePhoto(sampleinfo.sampleId) : this.samplephotos);
            this.currentSelectedImg = null;
            bool flag = this.samplephotos != null;
            if (flag)
            {
                this.currentSamplephoto = ((this.samplephotos.Count > 0) ? this.samplephotos[this.photoIndex] : null);
            }
        }
        /// <summary>
        /// 新建立实验
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void newBtn_MouseClick(object sender, MouseEventArgs e)
        {
            Sample s = new Sample();
            DialogResult res = s._ShowDialog();
            if (res == DialogResult.OK)
            {
                pic_Show.Image=null;
                if (pbx.Count > 0)
                {
                    for (int i = 0; i < pbx.Count; i++)
                    {
                        pbx[i].Image.Dispose();
                        //pbx[i].Image = null;
                        pbx[i].Dispose();
                    }
                }
                pbx.Clear();
                ucGroupHead1.Text = "图像缩略（当前：" + pictureInfos.Count + "张）";
                this.panel2.Controls.Clear();

                pictureInfos.Clear();
                //清空图像
                ucGroupHead1.Text = "图像缩略（当前：" + pictureInfos.Count + "张）";
                this.panel2.Controls.Clear();

                uiProcessBar1.Text = "0%";
                uiProcessBar1.Value = 0;
                picindex = 0;
                samplephotos.Clear();
                this.pic_Show.Invalidate();
                sampleinfo = SampleinfoServices.GetLastObject();
                //更新面板
                updatestu();
            }
            this.Focus();
        }
        public void updatestu()
        {
            if (sampleinfo == null)
            {
                this.clab.Text = "无";
                this.plab.Text = "无";
                this.stulab.Text = "待新建";
            }
            else
            {
                this.clab.Text = sampleinfo.sampleId;
                this.plab.Text = sampleinfo.patientId;
                this.stulab.Text = "采集中";
                //this.lbl_CellNum.Text = sampleinfo
            }
            lbl_CellNum.Text = "0";
            //lblAnalysisInfo.Text = "分析进度：";
            rightCellLab.Text = "0";
            errorCellLab.Text = "0";
            lbl_qualification_rate.Text = "0";
        }
        /// <summary>
        /// 拍照
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void collectBtn_Click(object sender, MouseEventArgs e)
        {
            this.catchPic();

        }
        /// <summary>
        /// 拍图
        /// </summary>
        private void catchPic()
        {
            if (!m_bIsOpen)
            {
                ShowErrorNotifier("设备未连接", true, 2000);
                return;
            }
            if (sampleinfo == null)
            {
                ShowErrorTip("请先新建样本");
                return;
            }
            if (sampleinfo.processed == 2)
            {
                ShowAskDialog("当前样本已计算，请新建样本后重新拍摄分析计算！");
                return;
            }
            if (string.Equals(uiProcessBar1.Text, "100%") || samplephotos.Count == 0)
            {
                int picsNum = pictureInfos.Count;
                flag = true;
                isCal = false;
                //isShow = true;
                ShowWaitForm();
                Thread.Sleep(888);
                Samplephoto info = pictureInfos.Last();


                //updatestu();
                string[] fileNames = new string[1];
                fileNames[0] = AppDomain.CurrentDomain.BaseDirectory + pictureInfos.Last().imageSource;

                this.AddPhotos(fileNames);
                Thread.Sleep(700);
                HideWaitForm();
                Random r = new Random();
                uiProcessBar1.Value = 0;
                int step = r.Next(10, 80);
                uiProcessBar1.Value += step;
                uiProcessBar1.Text = (uiProcessBar1.Value + "%");
                PictureBox p = new PictureBox
                {
                    BackColor = Color.Cornsilk,//底色
                    Location = new Point(10, (pictureInfos.Count - 1) * 149 + 10),//位置
                    Name = (pictureInfos.Count - 1).ToString(),//名称
                    Size = new Size(210, 160),//大小
                    TabStop = false,
                    Cursor = Cursors.Hand,
                    Image = Image.FromFile(pictureInfos.Last().imageSource),
                    SizeMode = PictureBoxSizeMode.StretchImage//图片大小模式
                };
                //cellNums.Add(p.Name, num);
                p.DoubleClick += new EventHandler(this.Picture_doubleClick);//添加双击事件
                p.Invalidate();
                pbx.Add(p);
                //lbl_CellNum.Text = m_DTS.TotalNum.ToString();
                ucGroupHead1.Text = "图像缩略（当前：" + pbx.Count + "张）";
                this.panel2.Controls.Add(p);

            }
            else
            {
                ShowErrorTip("当前存在图片正在进行分析，请等待分析完毕后再进行操作");
                return;
            }
        }

        /// <summary>
        /// 双击图片响应函数
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Picture_doubleClick(object sender, EventArgs e)
        {
            //return;
            if (!isCal)
            {
                ShowErrorTip("请计算后在查看详情");
                return;
            }
            PictureBox pb = (PictureBox)sender;
            pic_Show.Image = pb.Image;
        }
        /// <summary>
        /// 导入图片进行分析
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void uiSymbolButton1_MouseClick(object sender, MouseEventArgs e)
        {
            if (sampleinfo == null)
            {
                ShowErrorTip("请先新建样本");
                return;
            }
            if(sampleinfo.processed == 2)
            {
                ShowAskDialog("当前样本已计算，请新建样本后重新导入图片分析计算！");
                return;
            }
            if (!string.Equals(uiProcessBar1.Text, "100%") && pictureInfos.Count != 0)
            {
                ShowErrorTip("当前存在图片正在进行分析，请等待分析完毕后再进行操作");
                return;
            }
            isCal = false;
            int picindex = pictureInfos.Count;
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Multiselect = true;  //该值确定是否可以选择多个文件
            ofd.Title = "请选择文件";
            ofd.Filter = "图片(*.jpg,*.gif,*.bmp)|*.jpg;*.gif;*.bmp";
            if (ofd.ShowDialog() == DialogResult.OK)
            {

                foreach (string src_name in ofd.FileNames)
                {
                    DateTime dtNow = DateTime.Now;  // 获取系统当前时间
                    string m_strFilePath = Directory.GetCurrentDirectory().ToString() +
                        "\\images" + "\\" + dtNow.ToString("yyyyMMdd") + "\\" + sampleinfo.sampleId;
                    if (pictureInfos.Count == 0 && !Directory.Exists(m_strFilePath))
                    {
                        Directory.CreateDirectory(m_strFilePath);
                    }
                    Samplephoto info = new Samplephoto()
                    {
                        imageSource = "images\\" + dtNow.ToString("yyyyMMdd") + "\\" + sampleinfo.sampleId
                        + "\\" + dtNow.ToString("yyyy_MM_dd_HH_mm_ss_fff") + ".jpg",
                        format = "jpg",
                        sampleId = sampleinfo.sampleId,
                        type = "MRF",
                    };
                    Thread.Sleep(10);
                    try
                    {
                        File.Copy(src_name, info.imageSource);
                    }
                    catch (Exception exc)
                    { }

                    pictureInfos.Add(info);

                    //updatestu();
                }
                ShowWaitForm();

                int piccount = ofd.FileNames.Length;

                //将图片传入算法进行识别
                string[] fileNames = ofd.FileNames;
                this.AddPhotos(fileNames);
                Thread.Sleep(1500);
                HideWaitForm();

                Random r = new Random();
                int step = r.Next(1, 3);
                uiProcessBar1.Value += step;
                uiProcessBar1.Text = (uiProcessBar1.Value + "%");

                LogHelper.WriteErrLog("原图片数：" + pictureInfos.Count + " 当前选择的图片数" + piccount);
                for (int i = 0; i < piccount; i++)
                {
                    PictureBox p = new PictureBox
                    {
                        BackColor = Color.Cornsilk,//底色
                        Location = new Point(10, ((i + picindex) - 1) * 149 + 10),//位置
                        Name = ((i + picindex) - 1).ToString(),//名称
                        Size = new Size(210, 160),//大小
                        TabStop = false,
                        Cursor = Cursors.Hand,
                        Image = Image.FromFile(pictureInfos[i + picindex].imageSource),
                        SizeMode = PictureBoxSizeMode.StretchImage//图片大小模式
                    };
                    //cellNums.Add(p.Name, num);
                    p.DoubleClick += new EventHandler(this.Picture_doubleClick);//添加双击事件
                    p.Invalidate();
                    pbx.Add(p);
                    //lbl_CellNum.Text = m_DTS.TotalNum.ToString();
                    ucGroupHead1.Text = "图像缩略（当前：" + pbx.Count + "张）";
                    this.panel2.Controls.Add(p);
                }

                this.Invoke((MethodInvoker)delegate {
                    lblAnalysisInfo.Text = "分析进度（" + picindex + "/" + pictureInfos.Count + "）张";
                    uiProcessBar1.Value = (int)(picindex * 100 / pictureInfos.Count);
                    uiProcessBar1.Text = (uiProcessBar1.Value + "%");
                });

            }

        }
        /// <summary>
        /// 分析计算
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_Cal_Click(object sender, EventArgs e)
        {
            if (sampleinfo == null)
            {
                ShowErrorTip("未创建样本");
                return;
            }
            sampleinfo = SampleinfoServices.GetObject(sampleinfo.Id);
            if (sampleinfo.processed == 2)
            {
                ShowErrorTip("该样本已经计算过了");
                return;
            }

            if (sampleinfo.sperm_num < GlobalProperty.MaxCellNum && pictureInfos.Count != GlobalProperty.MaxPic)
            {
                var result = ShowAskDialog($"精子数量小于{GlobalProperty.MaxCellNum}，可能是少精样本，是否继续计算");
                if (!result)
                    return;
            }
            if (pictureInfos.Count < 3)
            {
                ShowErrorTip("采集的照片数量不能少于3张！");
                return;
            }
            picindex = 0;//记录扫描图片数的
            lblAnalysisInfo.Text = "分析进度";

            interfCards = CardMag.GetInterfCard();
            string info = "";
            int restNumone = 0;
            //if (!interfCards[GlobalProperty.QtrfIndex].ReadCard(ref info, ref restNumone,GlobalProperty.AutCod))
            //{

            //    ShowAskDialog(" 友情提示", info);
            //    mifareone.rf_exit(icdev);
            //    return;
            //}

            ShowWaitForm();
            Thread.Sleep(1000);
            SetWaitFormDescription(UILocalize.SystemProcessing + "50%");
            var isupdate = SampleinfoServices.UpdateSampleStatus(sampleinfo, 2);
            sampleinfo = SampleinfoServices.GetObject(sampleinfo.Id);
            if (isupdate)
            {
                //扣卡
                int restNum = 0;

                //if (!interfCards[GlobalProperty.QtrfIndex].WriteCard(ref info, ref restNum))
                //{
                //    HideWaitForm();
                //    ShowAskDialog(" 友情提示", "卡次不够，请联系管理员后再进行计算");
                //    mifareone.rf_exit(icdev);
                //    return;
                //}

                string excelReportPath1 = "LIS Results" + "\\" + DateTime.Parse(sampleinfo.sendtime).ToString("yyyy") + "\\" + DateTime.Parse(sampleinfo.sendtime).ToString("MM") + "\\";
                string excelReportPath = "Reports" + "\\" + DateTime.Parse(sampleinfo.sendtime).ToString("yyyy") + "\\" + DateTime.Parse(sampleinfo.sendtime).ToString("MM") + "\\";
                if (!Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + excelReportPath))
                    Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + excelReportPath);
                if (!Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + excelReportPath1))
                    Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + excelReportPath1);

                LogHelper.WriteErrLog("有错误我有执行吗？" + (AppDomain.CurrentDomain.BaseDirectory + excelReportPath));
                string excelreports = "template\\SpermAnalysis_TEST.xls";
                // string excelfile = m_DT.Patient_ID + " " +m_DT.Patient_Name;//文件名为病人姓名
                string excelFile = excelReportPath + DateTime.Parse(sampleinfo.sendtime).ToString("yyyyMM") + " " + "SpermAnalysisreport.xls";
                string excelFile1 = excelReportPath1 + DateTime.Parse(sampleinfo.sendtime).ToString("yyyyMM") + " " + "SpermAnalysisreport.xls";
                string excelFilepath = AppDomain.CurrentDomain.BaseDirectory + excelFile;
                string excelFilepath1 = AppDomain.CurrentDomain.BaseDirectory + excelFile1;

                if (!System.IO.File.Exists(excelFilepath))
                    System.IO.File.Copy(excelreports, excelFilepath);
                FileStream file = new FileStream(@excelFilepath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                POIFSFileSystem ps = new POIFSFileSystem(file);//需using NPOI.POIFS.FileSystem;
                IWorkbook workbook = new HSSFWorkbook(ps);
                ISheet sheet = workbook.GetSheet("SpermAnalysis_TEST");//获取工作表
                IRow SheetRow = sheet.GetRow(0); //得到表头
                FileStream fout = new FileStream(@excelFilepath, FileMode.Open, FileAccess.Write, FileShare.ReadWrite);//写入流
                SheetRow = sheet.CreateRow((sheet.LastRowNum + 1));//在工作表中添加一行

                HSSFCell[] SheetCell = new HSSFCell[21];
                sheet.CreateRow((sheet.LastRowNum));
                for (int i = 0; i < 21; i++)
                {
                    SheetCell[i] = (HSSFCell)SheetRow.CreateCell(i);  //为第一行创建21个单元格
                }
                SheetCell[0].SetCellValue(sampleinfo.sampleId);
                SheetCell[1].SetCellValue(sampleinfo.patientId);
                SheetCell[2].SetCellValue(sampleinfo.patientName);
                SheetCell[3].SetCellValue(sampleinfo.patientage);
                SheetCell[4].SetCellValue(sampleinfo.inspectionDoctor);

                SheetCell[5].SetCellValue(DateTime.Parse(sampleinfo.processed_time).ToString("yyyy-MM-dd"));
                SheetCell[6].SetCellValue(sampleinfo.sampleSource);

                SheetCell[7].SetCellValue(sampleinfo.sendDoctor);
                SheetCell[8].SetCellValue(sampleinfo.normal_num);
                SheetCell[9].SetCellValue(sampleinfo.abnormal_num);
                SheetCell[10].SetCellValue(sampleinfo.sperm_num);
                SheetCell[11].SetCellValue(sampleinfo.abnormalshape_num);
                SheetCell[12].SetCellValue(sampleinfo.abnormalmiddlepiece_num);
                SheetCell[13].SetCellValue(sampleinfo.abnormalTail_num);
                SheetCell[14].SetCellValue(sampleinfo.ERC_num);
                SheetCell[15].SetCellValue(sampleinfo.qualification_rate);

                SheetCell[16].SetCellValue(samplephotos[0].imageSource);
                //SheetCell[17].SetCellValue(samplephotos[1].imageSource);
                //SheetCell[18].SetCellValue(samplephotos[2].imageSource);
                fout.Flush();
                workbook.Write(fout, false);//写入文件
                workbook.Close();
                fout.Close();
                File.Copy(excelFilepath, excelFilepath1, true);
                //LogHelper.WriteErrLog("表格有生成吗？");

                SeriesCollection series = chart1.Series;
                chart1.Series[0].Points[0].YValues[0] = sampleinfo.abnormal_num;//异常  samp.abnormal_rate;
                chart1.Series[0].Points[0].AxisLabel = sampleinfo.abnormal_num.ToString() + "(" + (sampleinfo.abnormal_rate * 100).ToString("f2") + "%)";
                chart1.Series[0].Points[1].YValues[0] = sampleinfo.normal_num;//正常
                chart1.Series[0].Points[1].AxisLabel = sampleinfo.normal_num + "(" + ((1 - sampleinfo.abnormal_rate) * 100).ToString("f2") + "%)";
                chart1.Invalidate();

                var infos = SamplephotoServices.GetSamplePhoto(sampleinfo.sampleId).ToList();
                string ReportPath = "Reports" + "/" + DateTime.Now.ToString("yyyy") + "/" + DateTime.Now.ToString("MM") + "/";
                if (!System.IO.Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + ReportPath))
                    System.IO.Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + ReportPath);
                string imgpicchart = AppDomain.CurrentDomain.BaseDirectory + ReportPath + sampleinfo.sampleId + ".png";//文件名为病人姓名+门诊编号
                chart1.SaveImage(imgpicchart, ChartImageFormat.Png);

                string PrintMsg = null;
                String[] PatientInfo = new String[] {
                    GlobalProperty.Hospital,//院名
                    sampleinfo.patientName,
                    sampleinfo.patientage.ToString(),
                    sampleinfo.patientId,
                    sampleinfo.abstinenceDays.ToString(),
                    sampleinfo.reviewed==0?"√":"",
                    sampleinfo.reviewed==1?"√":"",
                    sampleinfo.department,
                    DateTime.Parse(sampleinfo.sendtime).ToString("yyyy-MM-dd"),
                    sampleinfo.sendDoctor,//对应DoctorName
                    sampleinfo.inspectionDoctor,
                    sampleinfo.reviewer,
                    DateTime.Parse(sampleinfo.processed_time).ToString("yyyy-MM-dd"),
                    DateTime.Now.ToString("yyyy-MM-dd"),
                    sampleinfo.sperm_num.ToString(),
                    sampleinfo.normal_num.ToString(),
                    (100*(1-sampleinfo.abnormal_rate)).ToString("f2"),
                    sampleinfo.abnormal_num.ToString(),
                    (sampleinfo.abnormal_rate*100).ToString("f2"),
                    //sample.abnormal_num.ToString(),
                    //((1D-sample.qualification_rate)*100).ToString("f0"),
                    sampleinfo.abnormalshape_num.ToString(),
                    ((1.0*sampleinfo.abnormalshape_num/sampleinfo.sperm_num)*100).ToString("f2"),
                    sampleinfo.abnormalmiddlepiece_num.ToString(),
                    ((1.0*sampleinfo.abnormalmiddlepiece_num/sampleinfo.sperm_num)*100).ToString("f2"),
                    sampleinfo.abnormalTail_num.ToString(),
                    ((1.0*sampleinfo.abnormalTail_num/sampleinfo.sperm_num)*100).ToString("f2"),
                    sampleinfo.ERC_num.ToString(),
                    ((1.0*sampleinfo.ERC_num/sampleinfo.sperm_num)*100).ToString("f2"),
                    (1.0*(sampleinfo.abnormalshape_num+sampleinfo.abnormalmiddlepiece_num+sampleinfo.abnormalTail_num)/sampleinfo.abnormal_num).ToString("f2"),
                    (1.0*(sampleinfo.abnormalshape_num+sampleinfo.abnormalmiddlepiece_num+sampleinfo.abnormalTail_num)/sampleinfo.sperm_num).ToString("f2"),
                    (100*(1-sampleinfo.abnormal_rate)).ToString("f2"),
                };


                if (!ShowReport.printReport(PatientInfo, imgpicchart, infos[0].imageSource, false, ref PrintMsg))
                {
                    //ShowErrorDialog(PrintMsg ?? "打印失败！");
                }


                ShowSuccessDialog("友情提示", "计算成功！你的卡还可以使用" + restNum + "次！");
                rightCellLab.Text = sampleinfo.normal_num.ToString();
                errorCellLab.Text = sampleinfo.abnormal_num.ToString();
                lbl_qualification_rate.Text = (sampleinfo.qualification_rate * 100).ToString();
                isCal = true;
            }
            else
            {
                ShowErrorTip("计算失败，请联系管理员！");
                return;
            }
            HideWaitForm();
        }
        /// <summary>
        /// 打印报告
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btn_Print_Click(object sender, EventArgs e)
        {
            if (sampleinfo.processed == 2)
            {
                var sample = SampleinfoServices.GetObject(sampleinfo.Id);
                SeriesCollection series = chart1.Series;
                chart1.Series[0].Points[0].YValues[0] = sample.abnormal_num;//异常  samp.abnormal_rate;
                chart1.Series[0].Points[0].AxisLabel = sample.abnormal_num.ToString() + "(" + (sample.abnormal_rate * 100).ToString("f2") + "%)";
                chart1.Series[0].Points[1].YValues[0] = sample.normal_num;//正常
                chart1.Series[0].Points[1].AxisLabel = sample.normal_num + "(" + ((1 - sample.abnormal_rate) * 100).ToString("f2") + "%)";
                chart1.Invalidate();

                var infos = SamplephotoServices.GetSamplePhoto(sample.sampleId).ToList();
                string ReportPath = "Reports" + "/" + DateTime.Now.ToString("yyyy") + "/" + DateTime.Now.ToString("MM") + "/";
                if (!System.IO.Directory.Exists(AppDomain.CurrentDomain.BaseDirectory + ReportPath))
                    System.IO.Directory.CreateDirectory(AppDomain.CurrentDomain.BaseDirectory + ReportPath);
                string imgpicchart = AppDomain.CurrentDomain.BaseDirectory + ReportPath + sample.sampleId + ".png";//文件名为病人姓名+门诊编号
                chart1.SaveImage(imgpicchart, ChartImageFormat.Png);

                string PrintMsg = null;
                String[] PatientInfo = new String[] {
                    GlobalProperty.Hospital,//院名
                    sample.patientName,
                    sample.patientage.ToString(),
                    sample.patientId,
                    sample.abstinenceDays.ToString(),
                    sample.reviewed==0?"√":"",
                    sample.reviewed==1?"√":"",
                    sample.department,
                    DateTime.Parse(sample.sendtime).ToString("yyyy-MM-dd"),
                    sample.sendDoctor,//对应DoctorName
                    sample.inspectionDoctor,
                    sample.reviewer,
                    DateTime.Parse(sample.processed_time).ToString("yyyy-MM-dd"),
                    DateTime.Now.ToString("yyyy-MM-dd"),
                    sample.sperm_num.ToString(),
                    sample.normal_num.ToString(),
                    (100*(1-sample.abnormal_rate)).ToString("f2"),
                    sample.abnormal_num.ToString(),
                    (sample.abnormal_rate*100).ToString("f2"),
                    //sample.abnormal_num.ToString(),
                    //((1D-sample.qualification_rate)*100).ToString("f0"),
                    sample.abnormalshape_num.ToString(),
                    ((1.0*sample.abnormalshape_num/sample.sperm_num)*100).ToString("f2"),
                    sample.abnormalmiddlepiece_num.ToString(),
                    ((1.0*sample.abnormalmiddlepiece_num/sample.sperm_num)*100).ToString("f2"),
                    sample.abnormalTail_num.ToString(),
                    ((1.0*sample.abnormalTail_num/sample.sperm_num)*100).ToString("f2"),
                    sample.ERC_num.ToString(),
                    ((1.0*sample.ERC_num/sample.sperm_num)*100).ToString("f2"),
                    (1.0*(sample.abnormalshape_num+sample.abnormalmiddlepiece_num+sample.abnormalTail_num)/sample.abnormal_num).ToString("f2"),
                    (1.0*(sample.abnormalshape_num+sample.abnormalmiddlepiece_num+sample.abnormalTail_num)/sample.sperm_num).ToString("f2"),
                    (100*(1-sampleinfo.abnormal_rate)).ToString("f2"),
                };

                if (!ShowReport.printReport(PatientInfo, imgpicchart, infos[0].imageSource, true, ref PrintMsg))
                {
                    ShowErrorDialog(PrintMsg ?? "打印失败！");
                }
            }
            else
            {
                ShowErrorTip("该实例未计算过，无法生成报告！");
                return;
            }
        }

        public void KeyDownEvent(object sender, KeyEventArgs e)
        {
            this.MorTest_KeyDown1(sender, e);
        }

        private void MorTest_KeyDown1(object sender, KeyEventArgs e)
        {
            if (e.Modifiers == Keys.Control && e.KeyCode == Keys.B)
            {
                // 计算逻辑
                this.btn_Cal_Click(null, null);
                e.Handled = true;

            }
            if (e.KeyCode == Keys.Space)
            {
                this.catchPic();
                e.Handled = true;
            }
        }

        private void MorTests_Shown(object sender, EventArgs e)
        {

        }
    }
}
