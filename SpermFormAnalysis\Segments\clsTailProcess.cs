﻿using Emgu.CV;
using Emgu.CV.CvEnum;
using Emgu.CV.Structure;
using Emgu.CV.Util;
using NPOI.SS.Formula.Functions;
using SpermFormAnalysis.DataHelper;
using SpermFormAnalysis.utils;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using TFRunModel;

namespace SpermFormAnalysis.Segments
{
    public static class clsTailProcess
    {
        // Token: 0x0600057A RID: 1402
        [DllImport("SpermProcess.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int SegProcess(byte[,,] data0, int cols0, int rows0, ref byte strdata, byte[,,] data, int cols, int rows, ref byte output);

        // Token: 0x0600057B RID: 1403 RVA: 0x0001EB98 File Offset: 0x0001CD98
        public static List<clsTailProcess.tailResult> doTailSegProcess_single(List<Model.Sperm> sperms, string fn)
        {
            List<clsTailProcess.tailResult> list = new List<clsTailProcess.tailResult>();
            string text = "";
            for (int i = 0; i < sperms.Count; i++)
            {
                double num;
                double num2;
                double num3;
                double num4;
                clsMrfSegment.getBox(sperms[i], (double)TFmodel.common.resolutionRatio.Width, (double)TFmodel.common.resolutionRatio.Height, out num, out num2, out num3, out num4);
                string text2 = string.Concat(new object[]
                {
                    sperms[i].id,
                    " ",
                    num.ToString(),
                    " ",
                    num2.ToString(),
                    " ",
                    (num + num3).ToString(),
                    " ",
                    (num2 + num4).ToString(),
                    " ",
                    sperms[i].center_x.ToString(),
                    " ",
                    sperms[i].center_y.ToString()
                });
                bool flag = i < sperms.Count - 1;
                if (flag)
                {
                    text2 += "|";
                }
                text += text2;
            }
            byte[] bytes = Encoding.UTF8.GetBytes(text);
            byte[] array = new byte[1024000];
            try
            {
                Image<Bgr, byte> image = new Image<Bgr, byte>(fn);
                Image<Gray, byte> image2 = TFRunDeepLab.RunModelOnce(fn);
                bool flag2 = image.Size != new Size(TFmodel.common.resolutionRatio.Width, TFmodel.common.resolutionRatio.Height);
                if (flag2)
                {
                    image = image.Resize(TFmodel.common.resolutionRatio.Width, TFmodel.common.resolutionRatio.Height, Inter.Linear);
                }
                VectorOfVectorOfPoint vectorOfVectorOfPoint = new VectorOfVectorOfPoint();
                for (int j = 0; j < sperms.Count; j++)
                {
                    double num;
                    double num2;
                    double num3;
                    double num4;
                    clsMrfSegment.getBox(sperms[j], (double)TFmodel.common.resolutionRatio.Width, (double)TFmodel.common.resolutionRatio.Height, out num, out num2, out num3, out num4);
                    if (sperms[j].vop_sperm == null)
                    {
                        
                        LogHelper.WriteErrLog("这里偶尔会报错，还不知道原因出在那"+j);
                        continue;
                    }
                    else
                    {
                        LogHelper.WriteErrLog("暂时未出错精子索引数：" + j+" 坐标数:"+ sperms[j].vop_sperm.Count<Point>());
                    }
                    Point[] array2 = new Point[sperms[j].vop_sperm.Count<Point>()];
                    for (int k = 0; k < sperms[j].vop_sperm.Count<Point>(); k++)
                    {
                        array2[k].X = sperms[j].vop_sperm[k].X + (int)num;
                        array2[k].Y = sperms[j].vop_sperm[k].Y + (int)num2;
                    }
                    vectorOfVectorOfPoint.Clear();
                    //获取轮廓坐标
                    VectorOfPoint value = new VectorOfPoint(array2);
                    //将轮廓坐标推送到对应轮廓对象组中
                    vectorOfVectorOfPoint.Push(value);
                    //在原有图片上绘制指定轮廓
                    CvInvoke.DrawContours(image2, vectorOfVectorOfPoint, 0, new MCvScalar(0.0), -1, LineType.EightConnected, null, int.MaxValue, default(Point));
                }
                int num5 = clsTailProcess.SegProcess(image.Data, image.Width, image2.Height, ref bytes[0], image2.Data, image2.Width, image2.Height, ref array[0]);
                string @string = Encoding.Default.GetString(array, 0, array.Length);
                string[] array3 = @string.Split(new char[]
                {
                    '|'
                });
                foreach (string text3 in array3)
                {
                    clsTailProcess.tailResult tailResult = default(clsTailProcess.tailResult);
                    tailResult.points = new List<Point>();
                    string[] array5 = text3.Split(new char[]
                    {
                        ' '
                    });
                    tailResult.spermid = Convert.ToInt64(array5[0]);
                    tailResult.boxnum = Convert.ToInt32(array5[7]);
                    tailResult.spermcompute = Convert.ToInt32(array5[8]);
                    tailResult.length = Convert.ToInt32(array5[9]);
                    tailResult.borderestimate = Convert.ToInt32(array5[10]);
                    tailResult.middlepieceAngle = Convert.ToDouble(array5[11]);
                    tailResult.middlepieceWidth = Convert.ToDouble(array5[12]);
                    tailResult.tailAngle = Convert.ToDouble(array5[13]);
                    tailResult.ERCArea = Convert.ToDouble(array5[14]);
                    tailResult.ERCWidth = Convert.ToDouble(array5[15]);
                    tailResult.ERCHeight = Convert.ToDouble(array5[16]);
                    tailResult.MDPnum = Convert.ToInt32(array5[17]);
                    for (int m = 18; m < array5.Length; m++)
                    {
                        string text4 = array5[m];
                        text4 = text4.Replace('{', ' ').Replace('}', ' ').Replace("\0", " ").Trim();
                        bool flag3 = text4 != "";
                        if (flag3)
                        {
                            int x = (int)Convert.ToInt16(text4.Split(new char[]
                            {
                                ','
                            })[0]);
                            int y = (int)Convert.ToInt16(text4.Split(new char[]
                            {
                                ','
                            })[1]);
                            tailResult.points.Add(new Point(x, y));
                        }
                    }
                    list.Add(tailResult);
                }
                image.Dispose();
                image2.Dispose();
                GC.Collect();
            }
            catch(Exception exe)
            {
                LogHelper.WriteErrLog("调试：分析时报错" + exe.Message+exe.StackTrace);
            }
            return list;
        }

        // Token: 0x0600057C RID: 1404 RVA: 0x0001F114 File Offset: 0x0001D314
        public static void createTxt(ObservableCollection<Model.Sperm> sperms, string fn)
        {
            string text = "";
            for (int i = 0; i < sperms.Count; i++)
            {
                double num;
                double num2;
                double num3;
                double num4;
                clsMrfSegment.getBox(sperms[i], (double)TFmodel.common.resolutionRatio.Width, (double)TFmodel.common.resolutionRatio.Height, out num, out num2, out num3, out num4);
                string text2 = string.Concat(new object[]
                {
                    sperms[i].id,
                    " ",
                    num.ToString(),
                    " ",
                    num2.ToString(),
                    " ",
                    (num + num3).ToString(),
                    " ",
                    (num2 + num4).ToString(),
                    " ",
                    sperms[i].center_x.ToString(),
                    " ",
                    sperms[i].center_y.ToString(),
                    " ",
                    sperms[i].angle,
                    " (",
                    SpermServices.pointArrayToString(sperms[i].vop_sperm.ToArray()),

                    ")"
                });
                bool flag = i < sperms.Count - 1;
                if (flag)
                {
                    text2 += "|";
                }
                text += text2;
            }
            string path = fn.Replace(".jpg", ".txt");
            File.WriteAllText(path, text);
        }

        // Token: 0x0200006B RID: 107
        public struct tailResult
        {
            // Token: 0x0400030F RID: 783
            public long spermid;

            // Token: 0x04000310 RID: 784
            public int boxnum;

            // Token: 0x04000311 RID: 785
            public int spermcompute;

            // Token: 0x04000312 RID: 786
            public int length;

            // Token: 0x04000313 RID: 787
            public int borderestimate;

            // Token: 0x04000314 RID: 788
            public double middlepieceAngle;

            // Token: 0x04000315 RID: 789
            public double middlepieceWidth;

            // Token: 0x04000316 RID: 790
            public double tailAngle;

            // Token: 0x04000317 RID: 791
            public double ERCArea;

            // Token: 0x04000318 RID: 792
            public double ERCWidth;

            // Token: 0x04000319 RID: 793
            public double ERCHeight;

            // Token: 0x0400031A RID: 794
            public int MDPnum;

            // Token: 0x0400031B RID: 795
            public List<Point> points;
        }
    }
}
