@echo off
echo 正在配置GPU环境变量...

REM 设置TensorFlow GPU内存优化
set TF_FORCE_GPU_ALLOW_GROWTH=true
set TF_GPU_MEMORY_LIMIT=1400
set TF_CPP_MIN_LOG_LEVEL=2

REM 检查配置文件
if exist "gpu_config.ini" (
    echo 找到GPU配置文件
    findstr /C:"ForceUseCPU=true" gpu_config.ini >nul
    if %errorlevel%==0 (
        echo 配置为CPU模式
        set CUDA_VISIBLE_DEVICES=
    ) else (
        echo 配置为GPU模式
    )
) else (
    echo 未找到配置文件，使用默认设置
)

echo GPU环境配置完成
echo 启动SpermFormAnalysis...

REM 启动程序
SpermFormAnalysis.exe
