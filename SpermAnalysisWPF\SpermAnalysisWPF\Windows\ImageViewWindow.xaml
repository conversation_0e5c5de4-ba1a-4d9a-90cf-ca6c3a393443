<Window x:Class="SpermAnalysisWPF.Windows.ImageViewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="精子形态学分析结果" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" Background="#F5F5F5">
            <Button Name="ZoomInBtn" Content="放大" Width="80" Height="30" Margin="5" Click="ZoomInBtn_Click"/>
            <Button Name="ZoomOutBtn" Content="缩小" Width="80" Height="30" Margin="5" Click="ZoomOutBtn_Click"/>
            <Button Name="ResetZoomBtn" Content="重置" Width="80" Height="30" Margin="5" Click="ResetZoomBtn_Click"/>
            <Separator Width="10"/>
            <CheckBox Name="ShowDetectionBox" Content="显示检测框" IsChecked="True" VerticalAlignment="Center" Margin="5" Checked="ShowDetectionBox_Changed" Unchecked="ShowDetectionBox_Changed"/>
            <CheckBox Name="ShowSegmentation" Content="显示分割结果" IsChecked="True" VerticalAlignment="Center" Margin="5" Checked="ShowSegmentation_Changed" Unchecked="ShowSegmentation_Changed"/>
            <CheckBox Name="ShowLabels" Content="显示标签" IsChecked="True" VerticalAlignment="Center" Margin="5" Checked="ShowLabels_Changed" Unchecked="ShowLabels_Changed"/>
            <Separator Width="10"/>
            <Button Name="ExportBtn" Content="导出图像" Width="80" Height="30" Margin="5" Click="ExportBtn_Click"/>
        </StackPanel>
        
        <!-- 主显示区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>
            
            <!-- 图像显示区域 -->
            <Border Grid.Column="0" BorderBrush="Gray" BorderThickness="1" Margin="10">
                <ScrollViewer Name="ImageScrollViewer" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                    <Canvas Name="ImageCanvas" Background="White">
                        <Image Name="MainImage" Stretch="None"/>
                    </Canvas>
                </ScrollViewer>
            </Border>
            
            <!-- 信息面板 -->
            <Grid Grid.Column="1" Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 统计信息 -->
                <GroupBox Grid.Row="0" Header="分析统计" Margin="0,0,0,10">
                    <StackPanel Margin="10">
                        <TextBlock Name="TotalCountText" Text="总精子数: 0" Margin="0,2"/>
                        <TextBlock Name="NormalCountText" Text="正常精子: 0" Margin="0,2"/>
                        <TextBlock Name="AbnormalCountText" Text="异常精子: 0" Margin="0,2"/>
                        <TextBlock Name="NormalRateText" Text="正常率: 0%" Margin="0,2"/>
                    </StackPanel>
                </GroupBox>
                
                <!-- 精子列表 -->
                <GroupBox Grid.Row="1" Header="精子详情">
                    <DataGrid Name="SpermDataGrid" AutoGenerateColumns="False" IsReadOnly="True" 
                              SelectionMode="Single" SelectionChanged="SpermDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="序号" Binding="{Binding spermindex}" Width="50"/>
                            <DataGridTextColumn Header="面积" Binding="{Binding spermArea, StringFormat=F2}" Width="60"/>
                            <DataGridTextColumn Header="长轴" Binding="{Binding longAxis, StringFormat=F2}" Width="60"/>
                            <DataGridTextColumn Header="短轴" Binding="{Binding shortAxis, StringFormat=F2}" Width="60"/>
                            <DataGridTextColumn Header="状态" Binding="{Binding isNormal}" Width="60"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </GroupBox>
                
                <!-- 详细信息 -->
                <GroupBox Grid.Row="2" Header="选中精子详情" Margin="0,10,0,0">
                    <StackPanel Name="DetailPanel" Margin="10">
                        <TextBlock Name="DetailText" Text="请选择一个精子查看详情" TextWrapping="Wrap"/>
                    </StackPanel>
                </GroupBox>
            </Grid>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="就绪"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="ZoomText" Text="缩放: 100%"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="CoordinateText" Text="坐标: (0, 0)"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
