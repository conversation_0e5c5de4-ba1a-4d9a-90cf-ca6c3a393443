﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Sunny.UI;

namespace AIDFI.Form
{
    public partial class Quality : UIForm
    {
        public Quality()
        {
            InitializeComponent();
        }
        //string loaction = System.Environment.CurrentDirectory + "\\" + "tempFile\\Quality\\";//获取Word文档模板
        private void Quality_Load(object sender, EventArgs e)
        {
                //加载所有图片
            //pictureBox1.Image = Image.FromFile(loaction +"1.jpg");
            //pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;//图片大小模式
            //pictureBox2.Image = Image.FromFile(loaction + "2.jpg");
            //pictureBox2.SizeMode = PictureBoxSizeMode.StretchImage;//图片大小模式
            //pictureBox3.Image = Image.FromFile(loaction + "3.jpg");
            //pictureBox3.SizeMode = PictureBoxSizeMode.StretchImage;//图片大小模式
            //pictureBox4.Image = Image.FromFile(loaction + "4.jpg");
            //pictureBox4.SizeMode = PictureBoxSizeMode.StretchImage;//图片大小模式
            //pictureBox5.Image = Image.FromFile(loaction + "5.jpg");
            //pictureBox5.SizeMode = PictureBoxSizeMode.StretchImage;//图片大小模式
            //pictureBox6.Image = Image.FromFile(loaction + "6.jpg");
            //pictureBox6.SizeMode = PictureBoxSizeMode.StretchImage;//图片大小模式

        }

        private void pictureBox1_Click(object sender, EventArgs e)
        {
            //启动图
            //System.Diagnostics.Process.Start( loaction+"1.jpg");
        }

        private void Quality_FormClosed(object sender, FormClosedEventArgs e)
        {
            pictureBox1.Dispose();
            pictureBox2.Dispose();
            pictureBox3.Dispose();
            pictureBox4.Dispose();
            pictureBox5.Dispose();
            pictureBox6.Dispose();
        }

        private void pictureBox2_Click(object sender, EventArgs e)
        {
            //System.Diagnostics.Process.Start(loaction + "2.jpg");
        }

        private void pictureBox3_Click(object sender, EventArgs e)
        {
            //System.Diagnostics.Process.Start(loaction + "3.jpg");
        }

        private void pictureBox4_Click(object sender, EventArgs e)
        {
            //System.Diagnostics.Process.Start(loaction + "4.jpg");
        }

        private void pictureBox5_Click(object sender, EventArgs e)
        {
            //System.Diagnostics.Process.Start(loaction + "5.jpg");
        }

        private void pictureBox6_Click(object sender, EventArgs e)
        {
            //System.Diagnostics.Process.Start(loaction + "6.jpg");
        }
    }
}
