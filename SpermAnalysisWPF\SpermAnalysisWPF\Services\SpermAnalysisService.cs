using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using Emgu.CV;
using Emgu.CV.Structure;
using SpermAnalysisWPF.Model;
using SpermAnalysisWPF.DataHelper;
using SpermAnalysisWPF.Algorithms;
using SpermAnalysisWPF.Utils;

namespace SpermAnalysisWPF.Services
{
    public class SpermAnalysisService
    {
        private static readonly double MIN_SCORE_FOR_OBJECT = 0.5;
        private static readonly int outerT = 20;
        private List<Mrfrules> validRules = new List<Mrfrules>();
        private SpermDataService dataService;

        public SpermAnalysisService()
        {
            dataService = new SpermDataService();
            LoadValidationRules();
        }

        private void LoadValidationRules()
        {
            try
            {
                validRules = dataService.GetMrfRules();
            }
            catch (Exception ex)
            {
                validRules = new List<Mrfrules>();
                System.Windows.MessageBox.Show($"加载验证规则失败: {ex.Message}", "警告");
            }
        }

        public List<Sperm> AnalyzeImage(string imagePath, string sampleId)
        {
            try
            {
                if (!File.Exists(imagePath))
                {
                    throw new FileNotFoundException($"图像文件不存在: {imagePath}");
                }

                // 设置检测阈值
                MRFSegment.MIN_SCORE_FOR_OBJECT = MIN_SCORE_FOR_OBJECT;

                // 使用完整的MRFSegment算法进行分析
                var results = MRFSegment.doSegment(imagePath);

                // 设置样本ID和图像名称
                foreach (var sperm in results)
                {
                    sperm.sampleId = sampleId;
                    sperm.imageName = System.IO.Path.GetFileName(imagePath);
                }

                // 添加尾巴处理（模拟原项目的逻辑）
                LogHelper.WriteDebugLog($"开始处理尾巴数据，精子数量: {results.Count}");
                ProcessTailData(results, imagePath);
                LogHelper.WriteDebugLog($"尾巴数据处理完成");

                return results;
            }
            catch (Exception ex)
            {
                throw new Exception($"图像分析失败: {ex.Message}", ex);
            }
        }



        public void SaveAnalysisResults(List<Sperm> sperms, string sampleId, string imagePath)
        {
            try
            {
                // 保存精子数据
                foreach (var sperm in sperms)
                {
                    dataService.SaveSperm(sperm);
                }

                // 计算统计信息
                int totalCount = sperms.Count;
                int normalCount = sperms.Count(s => IsNormalSperm(s));
                int abnormalCount = totalCount - normalCount;
                double normalRate = totalCount > 0 ? (double)normalCount / totalCount * 100 : 0;
                double abnormalRate = 100 - normalRate;

                // 保存样本信息
                var sampleInfo = new Sampleinfo
                {
                    sampleId = sampleId,
                    createTime = DateTime.Now,
                    patientName = "未设置",
                    patientId = "",
                    patientAge = 0,
                    doctorName = "",
                    hospitalName = "",
                    department = "",
                    remarks = $"图像文件: {System.IO.Path.GetFileName(imagePath)}",
                    totalSpermCount = totalCount,
                    normalSpermCount = normalCount,
                    normalRate = normalRate,
                    abnormalSpermCount = abnormalCount,
                    abnormalRate = abnormalRate,
                    analysisStatus = "已完成"
                };

                dataService.SaveSampleInfo(sampleInfo);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"保存分析结果失败: {ex.Message}", "错误");
            }
        }

        private bool IsNormalSperm(Sperm sperm)
        {
            foreach (var rule in validRules)
            {
                double value = GetSpermFieldValue(rule.Usefield, sperm);
                if (value < rule.Min_value || value > rule.Max_value)
                {
                    return false;
                }
            }
            return true;
        }

        private double GetSpermFieldValue(string fieldName, Sperm sperm)
        {
            switch (fieldName)
            {
                case "spermArea": return sperm.spermArea;
                case "longAxis": return sperm.longAxis;
                case "shortAxis": return sperm.shortAxis;
                case "ellipsRatio": return sperm.ellipsRatio;
                case "acrosomeRatio": return sperm.acrosomeRatio;
                case "kernelArea": return sperm.kernelArea;
                case "acrosomeArea": return sperm.acrosomeArea;
                default: return 0.0;
            }
        }

        public AnalysisResult CalculateStatistics(List<Sperm> sperms)
        {
            var result = new AnalysisResult
            {
                TotalCount = sperms.Count,
                NormalCount = sperms.Count(s => s.isNormal == 1),
                AbnormalCount = sperms.Count(s => s.isNormal == 0)
            };

            result.NormalRate = result.TotalCount > 0 ? 
                (double)result.NormalCount / result.TotalCount * 100 : 0;
            result.AbnormalRate = 100 - result.NormalRate;

            return result;
        }

        /// <summary>
        /// 处理尾巴数据（模拟原项目的clsTailProcess逻辑）
        /// </summary>
        private void ProcessTailData(List<Sperm> sperms, string imagePath)
        {
            try
            {
                // 为每个精子生成模拟的尾巴数据
                for (int i = 0; i < sperms.Count; i++)
                {
                    var sperm = sperms[i];

                    // 生成模拟的中段和尾巴轮廓数据
                    GenerateTailContours(sperm);

                    // 记录第一个精子的尾巴数据生成情况
                    if (sperm.spermindex == 1)
                    {
                        LogHelper.WriteDebugLog($"生成尾巴数据: 精子{sperm.spermindex}");
                        LogHelper.WriteDebugLog($"  vop_middlePiece: '{sperm.vop_middlePiece}'");
                        LogHelper.WriteDebugLog($"  vop_tail: '{sperm.vop_tail}'");
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"处理尾巴数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成模拟的尾巴轮廓数据
        /// </summary>
        private void GenerateTailContours(Sperm sperm)
        {
            try
            {
                // 基于精子的位置和大小生成模拟的尾巴轮廓
                var random = new Random(sperm.spermindex); // 使用精子索引作为种子，确保一致性

                // 计算精子中心点
                double centerX = (sperm.xmin + sperm.xmax) / 2.0;
                double centerY = (sperm.ymin + sperm.ymax) / 2.0;

                // 生成中段轮廓（相对较短）
                var middlePiecePoints = new List<string>();
                for (int i = 0; i < 8; i++) // 中段8个点
                {
                    double x = centerX + random.Next(-20, 20);
                    double y = centerY + random.Next(-10, 10) + i * 5;
                    middlePiecePoints.Add($"{x:F1},{y:F1}");
                }
                sperm.vop_middlePiece = string.Join(";", middlePiecePoints);

                // 生成尾巴轮廓（相对较长）
                var tailPoints = new List<string>();
                for (int i = 0; i < 15; i++) // 尾巴15个点
                {
                    double x = centerX + random.Next(-15, 15);
                    double y = centerY + 40 + i * 8; // 尾巴从中段下方开始
                    tailPoints.Add($"{x:F1},{y:F1}");
                }
                sperm.vop_tail = string.Join(";", tailPoints);
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"生成精子{sperm.spermindex}尾巴轮廓失败: {ex.Message}");
                sperm.vop_middlePiece = "";
                sperm.vop_tail = "";
            }
        }
    }

    public class AnalysisResult
    {
        public int TotalCount { get; set; }
        public int NormalCount { get; set; }
        public int AbnormalCount { get; set; }
        public double NormalRate { get; set; }
        public double AbnormalRate { get; set; }
    }
}
