using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SpermFormAnalysis.utils
{
    /// <summary>
    /// GPU内存管理工具类
    /// 用于解决不同显卡的内存兼容性问题
    /// </summary>
    public static class GPUMemoryManager
    {
        /// <summary>
        /// 设置TensorFlow环境变量以优化GPU内存使用
        /// </summary>
        public static void ConfigureTensorFlowGPU()
        {
            try
            {
                LogHelper.WriteInfoLog("开始配置TensorFlow GPU内存管理...");

                // 检查是否强制使用CPU
                if (Basic.bForceUseCPU)
                {
                    Environment.SetEnvironmentVariable("CUDA_VISIBLE_DEVICES", "");
                    LogHelper.WriteInfoLog("已设置强制使用CPU模式");
                    return;
                }

                // GPU内存优化设置
                if (Basic.bGPUMemoryOptimized)
                {
                    // 允许GPU内存增长，避免一次性分配所有内存
                    Environment.SetEnvironmentVariable("TF_FORCE_GPU_ALLOW_GROWTH", "true");
                    
                    // 限制GPU内存使用量（单位：MB）
                    Environment.SetEnvironmentVariable("TF_GPU_MEMORY_LIMIT", "2048");
                    
                    // 设置GPU内存分配策略
                    Environment.SetEnvironmentVariable("TF_GPU_ALLOCATOR", "cuda_malloc_async");
                    
                    // 减少TensorFlow日志输出
                    Environment.SetEnvironmentVariable("TF_CPP_MIN_LOG_LEVEL", "2");
                    
                    LogHelper.WriteInfoLog("已设置GPU内存优化配置");
                }

                LogHelper.WriteInfoLog("TensorFlow GPU内存管理配置完成");
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"配置TensorFlow GPU内存管理失败: {ex.Message}");
                
                // 出错时回退到CPU模式
                Environment.SetEnvironmentVariable("CUDA_VISIBLE_DEVICES", "");
                LogHelper.WriteInfoLog("已回退到CPU模式");
            }
        }

        /// <summary>
        /// 检测GPU内存并自动配置
        /// </summary>
        public static void AutoConfigureGPUMemory()
        {
            try
            {
                LogHelper.WriteInfoLog("开始自动检测GPU配置...");

                // 首先尝试从配置文件读取设置
                if (LoadConfigFromFile())
                {
                    LogHelper.WriteInfoLog("已从配置文件加载GPU设置");
                    ConfigureTensorFlowGPU();
                    return;
                }

                // 如果配置文件不存在或读取失败，使用自动检测
                var gpuMemoryMB = GetAvailableGPUMemory();

                if (gpuMemoryMB < 2048) // 如果GPU内存小于2GB
                {
                    LogHelper.WriteInfoLog($"检测到GPU内存较小({gpuMemoryMB}MB)，启用CPU模式");
                    Basic.bForceUseCPU = true;
                }
                else if (gpuMemoryMB < 4096) // 如果GPU内存小于4GB
                {
                    LogHelper.WriteInfoLog($"检测到GPU内存中等({gpuMemoryMB}MB)，启用内存优化模式");
                    Basic.bGPUMemoryOptimized = true;
                    Basic.bForceUseCPU = false;
                }
                else
                {
                    LogHelper.WriteInfoLog($"检测到GPU内存充足({gpuMemoryMB}MB)，使用标准GPU模式");
                    Basic.bGPUMemoryOptimized = false;
                    Basic.bForceUseCPU = false;
                }

                ConfigureTensorFlowGPU();
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"自动配置GPU内存失败: {ex.Message}");

                // 出错时使用安全的CPU模式
                Basic.bForceUseCPU = true;
                ConfigureTensorFlowGPU();
            }
        }

        /// <summary>
        /// 从配置文件加载GPU设置
        /// </summary>
        /// <returns>是否成功加载配置</returns>
        private static bool LoadConfigFromFile()
        {
            try
            {
                string configPath = System.IO.Path.Combine(Environment.CurrentDirectory, "gpu_config.ini");
                if (!System.IO.File.Exists(configPath))
                {
                    LogHelper.WriteInfoLog("GPU配置文件不存在，使用自动检测");
                    return false;
                }

                var iniFile = new IniFileUtils(configPath);

                // 读取配置
                Basic.bForceUseCPU = iniFile.ReadString("GPU设置", "ForceUseCPU", "false").ToLower() == "true";
                Basic.bGPUMemoryOptimized = iniFile.ReadString("GPU设置", "GPUMemoryOptimized", "true").ToLower() == "true";
                Basic.bTailused = iniFile.ReadString("GPU设置", "EnableTailProcessing", "true").ToLower() == "true";

                var memoryLimit = iniFile.ReadString("GPU设置", "GPUMemoryLimit", "2048");
                Environment.SetEnvironmentVariable("TF_GPU_MEMORY_LIMIT", memoryLimit);

                LogHelper.WriteInfoLog($"从配置文件加载设置: ForceUseCPU={Basic.bForceUseCPU}, GPUMemoryOptimized={Basic.bGPUMemoryOptimized}, MemoryLimit={memoryLimit}MB");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"读取GPU配置文件失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取可用GPU内存（简化版本，实际项目中可以使用更精确的方法）
        /// </summary>
        /// <returns>GPU内存大小（MB）</returns>
        private static int GetAvailableGPUMemory()
        {
            try
            {
                // 这里可以实现实际的GPU内存检测逻辑
                // 目前返回一个保守的估计值
                
                // 可以通过WMI或其他方式获取实际GPU信息
                // 这里简化处理，返回默认值
                return 1024; // 默认假设1GB内存，触发CPU模式
            }
            catch
            {
                return 512; // 出错时返回更小的值，确保使用CPU模式
            }
        }

        /// <summary>
        /// 清理GPU内存（在处理完成后调用）
        /// </summary>
        public static void CleanupGPUMemory()
        {
            try
            {
                // 这里可以添加GPU内存清理逻辑
                // 例如调用GC或其他清理方法
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                LogHelper.WriteInfoLog("GPU内存清理完成");
            }
            catch (Exception ex)
            {
                LogHelper.WriteErrLog($"GPU内存清理失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前GPU配置信息
        /// </summary>
        /// <returns>配置信息字符串</returns>
        public static string GetGPUConfigInfo()
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== GPU配置信息 ===");
            sb.AppendLine($"强制使用CPU: {Basic.bForceUseCPU}");
            sb.AppendLine($"GPU内存优化: {Basic.bGPUMemoryOptimized}");
            sb.AppendLine($"尾巴处理启用: {Basic.bTailused}");
            
            var cudaDevices = Environment.GetEnvironmentVariable("CUDA_VISIBLE_DEVICES");
            sb.AppendLine($"CUDA设备: {(string.IsNullOrEmpty(cudaDevices) ? "CPU模式" : cudaDevices)}");
            
            var gpuMemoryLimit = Environment.GetEnvironmentVariable("TF_GPU_MEMORY_LIMIT");
            sb.AppendLine($"GPU内存限制: {gpuMemoryLimit ?? "未设置"}MB");
            
            return sb.ToString();
        }
    }
}
