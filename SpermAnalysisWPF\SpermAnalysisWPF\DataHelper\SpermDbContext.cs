using System;
using System.Collections.Generic;
using System.IO;
using SpermAnalysisWPF.Model;

namespace SpermAnalysisWPF.DataHelper
{
    public class SpermDbContext
    {
        public static void InitializeDatabase()
        {
            // 简化：不需要创建数据库表，只确保目录存在
            try
            {
                string dataDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataDir))
                {
                    Directory.CreateDirectory(dataDir);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"初始化数据目录失败: {ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }


    }
}
