﻿#pragma checksum "..\..\..\..\..\Windows\ImageViewWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F904AE047FBB39E93F9ECDCB2DA5E5A9F38F3BE1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SpermAnalysisWPF.Windows {
    
    
    /// <summary>
    /// ImageViewWindow
    /// </summary>
    public partial class ImageViewWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 15 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomInBtn;
        
        #line default
        #line hidden
        
        
        #line 16 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomOutBtn;
        
        #line default
        #line hidden
        
        
        #line 17 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetZoomBtn;
        
        #line default
        #line hidden
        
        
        #line 19 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowDetectionBox;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowSegmentation;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowLabels;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportBtn;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ImageScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ImageCanvas;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image MainImage;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCountText;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NormalCountText;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AbnormalCountText;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NormalRateText;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SpermDataGrid;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DetailPanel;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DetailText;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ZoomText;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CoordinateText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SpermAnalysisWPF;component/windows/imageviewwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ZoomInBtn = ((System.Windows.Controls.Button)(target));
            
            #line 15 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ZoomInBtn.Click += new System.Windows.RoutedEventHandler(this.ZoomInBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ZoomOutBtn = ((System.Windows.Controls.Button)(target));
            
            #line 16 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ZoomOutBtn.Click += new System.Windows.RoutedEventHandler(this.ZoomOutBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ResetZoomBtn = ((System.Windows.Controls.Button)(target));
            
            #line 17 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ResetZoomBtn.Click += new System.Windows.RoutedEventHandler(this.ResetZoomBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ShowDetectionBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 19 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ShowDetectionBox.Checked += new System.Windows.RoutedEventHandler(this.ShowDetectionBox_Changed);
            
            #line default
            #line hidden
            
            #line 19 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ShowDetectionBox.Unchecked += new System.Windows.RoutedEventHandler(this.ShowDetectionBox_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ShowSegmentation = ((System.Windows.Controls.CheckBox)(target));
            
            #line 20 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ShowSegmentation.Checked += new System.Windows.RoutedEventHandler(this.ShowSegmentation_Changed);
            
            #line default
            #line hidden
            
            #line 20 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ShowSegmentation.Unchecked += new System.Windows.RoutedEventHandler(this.ShowSegmentation_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ShowLabels = ((System.Windows.Controls.CheckBox)(target));
            
            #line 21 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ShowLabels.Checked += new System.Windows.RoutedEventHandler(this.ShowLabels_Changed);
            
            #line default
            #line hidden
            
            #line 21 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ShowLabels.Unchecked += new System.Windows.RoutedEventHandler(this.ShowLabels_Changed);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ExportBtn = ((System.Windows.Controls.Button)(target));
            
            #line 23 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.ExportBtn.Click += new System.Windows.RoutedEventHandler(this.ExportBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ImageScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 9:
            this.ImageCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 10:
            this.MainImage = ((System.Windows.Controls.Image)(target));
            return;
            case 11:
            this.TotalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.NormalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.AbnormalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.NormalRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.SpermDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 63 "..\..\..\..\..\Windows\ImageViewWindow.xaml"
            this.SpermDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SpermDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DetailPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.DetailText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ZoomText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.CoordinateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

